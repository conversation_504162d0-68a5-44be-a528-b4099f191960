<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://www.cim.com" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:dfg="http://dfg.dongfang-china.com/2010/SVGExtensions/MX" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-245" id="thSvg" viewBox="244 -939.286 1326.36 1078.29">
  <defs>
    <style type="text/css"><![CDATA[
      .def {stroke:#a0a0a4;fill:#a0a0a4}
      .kV500 {stroke:#ff0000;fill:#ff0000}
      .kV220 {stroke:#ffffff;fill:#ffffff}
      .kV110 {stroke:#aa557f;fill:#aa557f}
      .kV1 {stroke:#cc0000;fill:#cc0000}
      .kV35 {stroke:#ffff00;fill:#ffff00}
      .kVdisp {stroke:#cc0000;fill:#cc0000}
      .kV10 {stroke:#00ff00;fill:#00ff00}
      .kV11 {stroke:#ffff00;fill:#ffff00}
      .kV6 {stroke:#5db5b9;fill:#5db5b9}
      .JieDi {stroke:#aaaa7f;fill:#aaaa7f}
      .BuDaiDian {stroke:#3c78b4;fill:#3c78b4}
      .BuQueDing {stroke:#cccccc;fill:#cccccc}
      .DianYuanDian {stroke:#d673a6;fill:#d673a6}
      .QuYuGD {stroke:#00ffe2;fill:#00ffe2}
      .AllLevel {stroke:#ccec82;fill:#ccec82}
      .LoadLine {stroke:#ae74b3;fill:#ae74b3}
      .IsLand0 {stroke:#f8e576;fill:#f8e576}
      .IsLand1 {stroke:#a5ffab;fill:#a5ffab}
      .IsLand2 {stroke:#aef2ff;fill:#aef2ff}
      .IsLand3 {stroke:#b6b1fc;fill:#b6b1fc}
      .IsLand4 {stroke:#deafff;fill:#deafff}
      .IsLand5 {stroke:#ff9ec3;fill:#ff9ec3}
      .kV20 {stroke:#ddbf1b;fill:#ddbf1b}
      .NFkV500 {stroke:#ff0000;fill:none}
      .NFkV220 {stroke:#ffffff;fill:none}
      .NFkV110 {stroke:#aa557f;fill:none}
      .NFkV1 {stroke:#cc0000;fill:none}
      .NFkV35 {stroke:#ffff00;fill:none}
      .NFkVdisp {stroke:#cc0000;fill:none}
      .NFkV10 {stroke:#00ff00;fill:none}
      .NFkV11 {stroke:#ffff00;fill:none}
      .NFkV6 {stroke:#5db5b9;fill:none}
      .NFJieDi {stroke:#aaaa7f;fill:none}
      .NFBuDaiDian {stroke:#3c78b4;fill:none}
      .NFBuQueDing {stroke:#cccccc;fill:none}
      .NFDianYuanDian {stroke:#d673a6;fill:none}
      .NFQuYuGD {stroke:#00ffe2;fill:none}
      .NFAllLevel {stroke:#ccec82;fill:none}
      .NFLoadLine {stroke:#ae74b3;fill:none}
      .NFIsLand0 {stroke:#f8e576;fill:none}
      .NFIsLand1 {stroke:#a5ffab;fill:none}
      .NFIsLand2 {stroke:#aef2ff;fill:none}
      .NFIsLand3 {stroke:#b6b1fc;fill:none}
      .NFIsLand4 {stroke:#deafff;fill:none}
      .NFIsLand5 {stroke:#ff9ec3;fill:none}
      .NFkV20 {stroke:#ddbf1b;fill:none}
    ]]></style>
    <symbol id="terminal" viewBox="-3 -3 6 6" visibility="hidden">
      <circle cx="0" cy="0" fill="none" r="2" stroke="rgb(255,255,0)"/>
    </symbol>
    <symbol dfg:desc="限电" dfg:flg="g_si" id="Tag:shape0" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-5)">限电</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="保安电" dfg:flg="g_si" id="Tag:shape1" viewBox="-2 -27 60 29">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,1,-7)">保安电</text>
      <rect fill="none" height="24" stroke="#ff0000" stroke-width="1" width="56" x="0" y="-25"/>
    </symbol>
    <symbol dfg:desc="间隔检修" dfg:flg="g_si" id="Tag:shape2" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止遥控" dfg:flg="g_si" id="Tag:shape3" viewBox="-2 -41 26 23">
      <text fill="#ff0000" font-family="SimSun" font-size="11" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.5,-33.75)" y="11">禁控</text>
      <rect fill="none" height="18" stroke="#ff0000" width="22" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="禁止告警" dfg:flg="g_si" id="Tag:shape4" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-21)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-3)">告警</text>
    </symbol>
    <symbol dfg:desc="线路检修" dfg:flg="g_si" id="Tag:shape5" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止刷新" dfg:flg="g_si" id="Tag:shape6" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-4)">刷新</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="引下线一组" dfg:flg="g_si" id="Tag:shape7" viewBox="-2 -42 46 44">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-21)">引线</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-3)">下一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-40"/>
    </symbol>
    <symbol dfg:desc="小电源" dfg:flg="g_si" id="Tag:shape8" viewBox="-2 -41 24 24">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,7,-70)" y="48"/>
      <text fill="#ff0000" font-family="SimSun" font-size="12" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,-0.198305,-57.3351)" y="35">敏感</text>
      <rect fill="none" height="20" stroke="#ff0000" width="20" x="0.187167" y="-38.6676"/>
    </symbol>
    <symbol dfg:desc="注释1" dfg:flg="g_si" id="Tag:shape9" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">注释</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一级保供" dfg:flg="g_si" id="Tag:shape10" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-23)">一级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-5)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="二级保供" dfg:flg="g_si" id="Tag:shape11" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-22)">二级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="退出运行" dfg:flg="g_si" id="Tag:shape12" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">退出</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">运行</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="新设备未投运" dfg:flg="g_si" id="Tag:shape13" viewBox="-2 -38 79 40">
      <rect fill="none" height="34" stroke="#ff0000" stroke-width="1" width="75" x="0" y="-36"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-21)">新设备</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-3)">未投运</text>
    </symbol>
    <symbol dfg:desc="自愈" dfg:flg="g_si" id="Tag:shape14" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,5,-21)" y="16">自愈</text>
      <rect fill="none" height="22" stroke="#ff0000" width="42" x="0.5" y="-21.5"/>
    </symbol>
    <symbol dfg:desc="禁止操作" dfg:flg="g_si" id="Tag:shape15" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">操作</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="站控" dfg:flg="g_si" id="Tag:shape16" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-5)">站控</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="带电作业" dfg:flg="g_si" id="Tag:shape17" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">带电</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">作业</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="重合闸退出" dfg:flg="g_si" id="Tag:shape18" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">重合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="备自投退出" dfg:flg="g_si" id="Tag:shape19" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">备自投</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="保护退出" dfg:flg="g_si" id="Tag:shape20" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,0.75,-36.5)" y="16">配线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3,-19)" y="16">检 修</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="冷备用" dfg:flg="g_si" id="Tag:shape21" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)"> 冷</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">备用</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="调试一" dfg:flg="g_si" id="Tag:shape22" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">调试</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="全站停电检修" dfg:flg="g_si" id="Tag:shape23" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">全站</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">检修</text>
    </symbol>
    <symbol dfg:desc="特级保供" dfg:flg="g_si" id="Tag:shape24" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">特级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一般保供" dfg:flg="g_si" id="Tag:shape25" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-24)">一般</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-6)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="三级保供" dfg:flg="g_si" id="Tag:shape26" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.48886,-39)" y="16">三级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.25981,-20.7709)" y="16">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-39.1145"/>
    </symbol>
    <symbol dfg:desc="禁止合闸,有人工作" dfg:flg="g_si" id="Tag:shape27" viewBox="-2 -67 109 69">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">有人工作</text>
      <rect fill="none" height="34" stroke="#ff0000" width="75" x="30" y="-38"/>
      <circle cx="14" cy="-51" fill="none" r="14" stroke="#ff0000"/>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-60" y2="-35"/>
    </symbol>
    <symbol dfg:desc="禁止合闸线路有人工作" dfg:flg="g_si" id="Tag:shape28" viewBox="-2 -85 112 87">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-54)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">线路有人</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">工作</text>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-78" y2="-53"/>
      <circle cx="14" cy="-69" fill="none" r="14" stroke="#ff0000"/>
      <rect fill="none" height="55" stroke="#ff0000" width="78" x="30" y="-56"/>
    </symbol>
    <symbol dfg:desc="间隔检修2" dfg:flg="g_si" id="Tag:shape29" viewBox="-2 -41 57 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-37)" y="15">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-19)" y="15">检修2</text>
      <rect fill="none" height="38" stroke="#ff0000" width="53" x="0" y="-39"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Breaker:shape2_0" viewBox="-4 -48 27 48">
      <rect fill="none" height="34" stroke-width="2" width="23" x="-2" y="-41"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Breaker:shape2_1" viewBox="-3 -47 27 46">
      <rect fill="rgb(0,255,0)" height="32" stroke-width="2" width="23" x="-1" y="-40"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Breaker:shape2_2" viewBox="-4 -48 28 48">
      <rect fill="none" height="35" stroke-width="2" width="24" x="-2" y="-41"/>
      <polyline fill="none" points="19,-39 3,-6" stroke-width="2"/>
      <polyline fill="none" points="18,-6 2,-39" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Breaker:shape2_3" viewBox="-4 -46 28 44">
      <rect fill="none" height="33" stroke-width="2" width="24" x="-2" y="-40"/>
      <polyline fill="none" points="18,-7 2,-40" stroke-width="2"/>
      <polyline fill="none" points="19,-40 3,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape1_0" viewBox="8 -100 20 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-61" y2="-52"/>
      <line stroke-width="1" x1="19" x2="10" y1="-61" y2="-86"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape1_1" viewBox="10 -100 18 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-52"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape1_2" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape1_3" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape2_0" viewBox="-1 -30 22 29">
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-5.0564"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape2_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape2_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="9.7856" x2="19.1085" y1="-12.363" y2="-21.6112"/>
      <line x1="10.2755" x2="1.03594" y1="-12.2144" y2="-21.8412"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape2_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="19.1085" x2="9.95129" y1="-21.6451" y2="-12.5633"/>
      <line x1="10.2968" x2="0.939632" y1="-12.3327" y2="-21.8464"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape3_0" viewBox="-1 -30 22 30">
      <line x1="10" x2="1" y1="-15" y2="-24"/>
      <line x1="19" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="1" y1="-7" y2="-16"/>
      <line x1="19" x2="10" y1="-24" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-24"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape3_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape3_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="9.78968" x2="0.951774" y1="-17.3132" y2="-8.47532"/>
      <line x1="10.2429" x2="19.0808" y1="-17.2679" y2="-8.43"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape3_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="0.986614" x2="9.98946" y1="-8.42733" y2="-17.4713"/>
      <line x1="9.98946" x2="19.1979" y1="-17.4302" y2="-8.34511"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape5_0" viewBox="-1 -18 57 20">
      <line stroke-width="1" x1="14" x2="14" y1="-8" y2="-6"/>
      <line stroke-width="1" x1="38" x2="13" y1="-7" y2="-16"/>
      <line stroke-width="1" x1="38" x2="47" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="5" x2="14" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="54" x2="54" y1="-9" y2="-6"/>
      <line stroke-width="1" x1="51" x2="51" y1="-10" y2="-5"/>
      <line stroke-width="1" x1="47" x2="47" y1="-14" y2="0"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape5_1" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape5_2" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape5_3" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape0_0" viewBox="0 0 20 50">
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="47.7308" y2="37.5586"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="12.0711" y2="1.91098"/>
      <line stroke-width="2" x1="1.5339" x2="11.1196" y1="14.6835" y2="38.0229"/>
      <line stroke-width="2" x1="7.79317" x2="14.424" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape0_1" viewBox="0 0 20 50">
      <line stroke-width="2" x1="7.77069" x2="14.4465" y1="11.5353" y2="11.5353"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.95842" y2="47.8157"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape0_2" viewBox="0 0 20 50">
      <line stroke-width="2" x1="5.91257" x2="16.3046" y1="13.614" y2="39.8683"/>
      <line stroke-width="2" x1="17.1558" x2="5.06136" y1="13.7286" y2="39.8683"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.96389" y2="48.2042"/>
      <line stroke-width="2" x1="7.80206" x2="14.4151" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape0_3" viewBox="0 0 20 50">
      <polyline fill="none" points="17.1086,13.8684 5.10859,39.8683" stroke-width="2"/>
      <polyline fill="none" points="5.8936,13.6195 16.3236,39.8683" stroke-width="2"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.7049" y2="47.8157"/>
      <line stroke-width="2" x1="7.78951" x2="14.4277" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape1_0" viewBox="-1 -33 20 34">
      <line stroke-width="2" x1="9" x2="9" y1="-27" y2="-3"/>
      <polyline fill="none" points="1,-13 9,-1 17,-13" stroke-width="2"/>
      <use height="6" terminal-index="0" width="6" x="6" xlink:href="#terminal" y="-30"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape47_0" viewBox="0 -27 29 28">
      <circle cx="15" cy="-15" fill="none" r="6" stroke-width="2"/>
      <circle cx="8" cy="-11" fill="none" r="6" stroke-width="2"/>
      <ellipse cx="14" cy="-7" fill="none" rx="6.5" ry="6" stroke-width="2"/>
      <circle cx="21" cy="-12" fill="none" r="6" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="12" xlink:href="#terminal" y="-24"/>
    </symbol>
    <symbol dfg:desc="绕组1" dfg:flg="g_po" dfg:val="2" id="Transformer2:两卷变压器5_0" viewBox="0 0 122 190">
      <circle cx="53" cy="53" fill="none" r="48" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="33" x2="53" y1="35" y2="55"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="53" x2="53" y1="55" y2="75"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" x1="121" x2="114" y1="9" y2="29"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="73" x2="53" y1="35" y2="55"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="120" y1="87" y2="10"/>
      
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" x1="120" x2="100" y1="9" y2="9"/>
      
      <use height="8" terminal-index="0" width="8" x="50" xlink:href="#terminal" y="1.82988"/>
      <use height="8" terminal-index="2" width="8" x="50" xlink:href="#terminal" y="51.4302"/>
    </symbol>
    <symbol dfg:desc="绕组2" dfg:flg="g_po" dfg:val="2" id="Transformer2:两卷变压器5_1" viewBox="0 0 122 190">
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="73" x2="53" y1="148" y2="118"/>
      
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="53" x2="33" y1="118" y2="148"/>
      <line fill="none" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" x1="33" x2="73" y1="148" y2="148"/>
      <circle cx="51" cy="138" fill="none" r="48" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2"/>
      <use height="8" terminal-index="1" width="8" x="48" xlink:href="#terminal" y="182.994"/>
    </symbol>
    <symbol dfg:desc="generator" dfg:flg="g_si" id="Generator:generator_0" viewBox="-30 -30 60 60">
      <circle cx="0" cy="1.11869" fill="none" r="27.5" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="-29.1677"/>
      <path d="M-22,4.75 C-12.125,-17.5 -4.5,-5 2.875,6.5 C10.25,18 20.75,1.5 22.25,-2.5" dfg:shp="pa_cu" fill="none" stroke="#ff0000" stroke-width="2"/>
    </symbol>
    <symbol dfg:flg="g_si" id="EnergyConsumer:shape1_0" viewBox="-1 -33 20 34">
      <line stroke-width="2" x1="9" x2="9" y1="-27" y2="-3"/>
      <polyline fill="none" points="1,-13 9,-1 17,-13" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="6" xlink:href="#terminal" y="-30"/>
    </symbol>
  </defs>
  
  <g id="HeadClass">
    <rect fill="rgb(0,0,0)" height="1078.286" stroke="none" width="1326.3555" x="244" y="-939.286"/>
  </g>
  <g id="MeasurementClass">
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="">
      <text fill="rgb(255,250,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,250,255)" x="365" y="-689">16:41:28</text>
      <metadata><cge:Meas_Ref ObjectID="ME-0" ObjectName="NULL.NULL"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-420340" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-873.437">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-420340" ObjectName="YH_HDGFZ.103P_JS"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-420341" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-842.437">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-420341" ObjectName="YH_HDGFZ.103Q_JS"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-420342" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-811.437">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-420342" ObjectName="YH_HDGFZ.103I_JS"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-193816" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-780.437">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-193816" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-193817" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-749.437">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-193817" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-193818" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-718.437">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-193818" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-193819" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-687.437">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-193819" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK_Uab"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191137" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="24" stroke="rgb(0,255,0)" x="986.7022" y="-656.437">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191137" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191129" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1184" y="-367">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191129" ObjectName="YH_HDGFZ.YH_HDGFZ_301BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191130" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1184" y="-338">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191130" ObjectName="YH_HDGFZ.YH_HDGFZ_301BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191128" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1184" y="-309">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191128" ObjectName="YH_HDGFZ.YH_HDGFZ_301BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191163" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="579" y="41">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191163" ObjectName="YH_HDGFZ.YH_HDGFZ_311BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191164" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="579" y="70">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191164" ObjectName="YH_HDGFZ.YH_HDGFZ_311BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191162" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="579" y="99">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191162" ObjectName="YH_HDGFZ.YH_HDGFZ_311BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191165" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="579" y="128">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191165" ObjectName="YH_HDGFZ.YH_HDGFZ_311BK_Cos"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191167" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="753" y="41">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191167" ObjectName="YH_HDGFZ.YH_HDGFZ_312BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191166" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="753" y="70">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191166" ObjectName="YH_HDGFZ.YH_HDGFZ_312BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191168" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="753" y="99">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191168" ObjectName="YH_HDGFZ.YH_HDGFZ_312BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191143" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="949" y="41">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191143" ObjectName="YH_HDGFZ.YH_HDGFZ_313BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191144" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="949" y="70">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191144" ObjectName="YH_HDGFZ.YH_HDGFZ_313BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191142" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="949" y="99">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191142" ObjectName="YH_HDGFZ.YH_HDGFZ_313BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191145" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="949" y="128">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191145" ObjectName="YH_HDGFZ.YH_HDGFZ_313BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191147" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1079.83887" y="41">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191147" ObjectName="YH_HDGFZ.YH_HDGFZ_314BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191148" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1079.83887" y="70">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191148" ObjectName="YH_HDGFZ.YH_HDGFZ_314BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191146" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1079.83887" y="99">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191146" ObjectName="YH_HDGFZ.YH_HDGFZ_314BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191149" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1079.83887" y="128">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191149" ObjectName="YH_HDGFZ.YH_HDGFZ_314BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191151" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1210.6777" y="41">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191151" ObjectName="YH_HDGFZ.YH_HDGFZ_315BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191152" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1210.6777" y="70">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191152" ObjectName="YH_HDGFZ.YH_HDGFZ_315BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191150" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1210.6777" y="99">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191150" ObjectName="YH_HDGFZ.YH_HDGFZ_315BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191153" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1210.6777" y="128">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191153" ObjectName="YH_HDGFZ.YH_HDGFZ_315BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191155" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1341.5166" y="41">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191155" ObjectName="YH_HDGFZ.YH_HDGFZ_316BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191156" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1341.5166" y="70">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191156" ObjectName="YH_HDGFZ.YH_HDGFZ_316BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191154" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1341.5166" y="99">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191154" ObjectName="YH_HDGFZ.YH_HDGFZ_316BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191157" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1341.5166" y="128">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191157" ObjectName="YH_HDGFZ.YH_HDGFZ_316BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191159" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1472.3555" y="37.66496">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191159" ObjectName="YH_HDGFZ.YH_HDGFZ_317BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191160" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1472.3555" y="66.66496">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191160" ObjectName="YH_HDGFZ.YH_HDGFZ_317BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191158" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1472.3555" y="95.66496">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191158" ObjectName="YH_HDGFZ.YH_HDGFZ_317BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191161" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="1472.3555" y="124.66496">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191161" ObjectName="YH_HDGFZ.YH_HDGFZ_317BK_Cos"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191138" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="523" y="-383">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191138" ObjectName="YH_HDGFZ.YH_HDGFZ_35#1_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191139" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="523" y="-354">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191139" ObjectName="YH_HDGFZ.YH_HDGFZ_35#1_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191140" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="523" y="-325">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191140" ObjectName="YH_HDGFZ.YH_HDGFZ_35#1_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-191141" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="23" stroke="rgb(0,255,0)" x="523" y="-296">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191141" ObjectName="YH_HDGFZ.YH_HDGFZ_35#1_Uab"/></metadata>
    </g>
    <g MeasureType="Tap" PreSymbol="0" align="1" appendix="" decimal="0" id="ME-191133" prefix="">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="29" stroke="rgb(0,255,0)" x="1251.58" y="-484.648">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191133" ObjectName="YH_HDGFZ.YH_HDGFZ_1T_Tap"/></metadata>
    </g>
    <g MeasureType="Tmp" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-191131" prefix="">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="29" stroke="rgb(0,255,0)" x="1251.58" y="-454.648">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-191131" ObjectName="YH_HDGFZ.YH_HDGFZ_1T_Tmp1"/></metadata>
    </g>
  </g>
  <g id="ConnectiveNodeClass">
    <g>
      <path class="NFkV35" d="M 1006,-388 L 1006,-417 L 1108.71,-418.131" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="transformer2" ObjectIDND0="45811@0" ObjectIDND1="45809@0" ObjectIDND2="45842@1"/></metadata>
    <path d="M 1006,-388 L 1006,-417 L 1108.71,-418.131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1108.71,-418.131 L 1109,-403.031" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" DevType2="switch" ObjectIDND0="45809@0" ObjectIDND1="45842@1" ObjectIDND2="45811@0"/></metadata>
    <path d="M 1108.71,-418.131 L 1109,-403.031" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1109,-329.76 L 1109,-311.25" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45810@0" ObjectIDND1="45808@1"/></metadata>
    <path d="M 1109,-329.76 L 1109,-311.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1109,-387.094 L 1109,-364.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45809@1" ObjectIDND1="45808@0"/></metadata>
    <path d="M 1109,-387.094 L 1109,-364.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-720.103 L 1108.72,-741.655" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="45803@1" ObjectIDND1="45801@0" ObjectIDND2="45805@0"/></metadata>
    <path d="M 1108.72,-720.103 L 1108.72,-741.655" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-704.583 L 1108.72,-720.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="45801@0" ObjectIDND1="45803@1" ObjectIDND2="45805@0"/></metadata>
    <path d="M 1108.72,-704.583 L 1108.72,-720.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1134.95,-720.103 L 1108.72,-720.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="45805@0" ObjectIDND1="45803@1" ObjectIDND2="45801@0"/></metadata>
    <path d="M 1134.95,-720.103 L 1108.72,-720.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-799.103 L 1108.72,-851.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="ACline" ObjectIDND0="45803@0" ObjectIDND1="45806@0" ObjectIDND2="52924@0"/></metadata>
    <path d="M 1108.72,-799.103 L 1108.72,-851.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-773.633 L 1108.72,-799.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="ACline" ObjectIDND0="45803@0" ObjectIDND1="45806@0" ObjectIDND2="52924@0"/></metadata>
    <path d="M 1108.72,-773.633 L 1108.72,-799.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1136.95,-799.103 L 1108.72,-799.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="ACline" ObjectIDND0="45806@0" ObjectIDND1="45803@0" ObjectIDND2="52924@0"/></metadata>
    <path d="M 1136.95,-799.103 L 1108.72,-799.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-652.103 L 1108.72,-623.633" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="45802@0" ObjectIDND1="45801@1" ObjectIDND2="45804@0"/></metadata>
    <path d="M 1108.72,-652.103 L 1108.72,-623.633" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-670.743 L 1108.72,-652.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="45801@1" ObjectIDND1="45802@0" ObjectIDND2="45804@0"/></metadata>
    <path d="M 1108.72,-670.743 L 1108.72,-652.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1137.95,-652.103 L 1108.72,-652.103" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="45804@0" ObjectIDND1="45802@0" ObjectIDND2="45801@1"/></metadata>
    <path d="M 1137.95,-652.103 L 1108.72,-652.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1109,-293.812 L 1109,-263" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45810@1"/></metadata>
    <path d="M 1109,-293.812 L 1109,-263" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1475,-173.76 L 1475,-158.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45830@0" ObjectIDND1="45828@1"/></metadata>
    <path d="M 1475,-173.76 L 1475,-158.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1475,-222.891 L 1475,-208.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45829@1" ObjectIDND1="45828@0"/></metadata>
    <path d="M 1475,-222.891 L 1475,-208.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1475,-263 L 1475,-238.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45829@0"/></metadata>
    <path d="M 1475,-263 L 1475,-238.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1475,-122 L 1475,-89" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45830@1" ObjectIDND1="45831@0" ObjectIDND2="74472@0"/></metadata>
    <path d="M 1475,-122 L 1475,-89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1475,-141.625 L 1475,-122" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45830@1" ObjectIDND1="45831@0" ObjectIDND2="74472@0"/></metadata>
    <path d="M 1475,-141.625 L 1475,-122" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1500,-122 L 1475,-122" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="thermalGenerator" ObjectIDND0="45831@0" ObjectIDND1="45830@1" ObjectIDND2="74472@0"/></metadata>
    <path d="M 1500,-122 L 1475,-122" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 794,-168.76 L 794,-153.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45838@0" ObjectIDND1="45836@1"/></metadata>
    <path d="M 794,-168.76 L 794,-153.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 794,-217.891 L 794,-203.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45837@1" ObjectIDND1="45836@0"/></metadata>
    <path d="M 794,-217.891 L 794,-203.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 794,-263 L 794,-233.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45837@0"/></metadata>
    <path d="M 794,-263 L 794,-233.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 794,-117 L 794,-84" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="load" ObjectIDND0="45838@1" ObjectIDND1="45839@0" ObjectIDND2="75075@0"/></metadata>
    <path d="M 794,-117 L 794,-84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 794,-136.625 L 794,-117" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="load" ObjectIDND0="45838@1" ObjectIDND1="45839@0" ObjectIDND2="75075@0"/></metadata>
    <path d="M 794,-136.625 L 794,-117" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 819,-117 L 794,-117" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="load" ObjectIDND0="45839@0" ObjectIDND1="45838@1" ObjectIDND2="75075@0"/></metadata>
    <path d="M 819,-117 L 794,-117" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 656,-165.76 L 656,-148.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45834@0" ObjectIDND1="45832@1"/></metadata>
    <path d="M 656,-165.76 L 656,-148.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 656,-214.891 L 656,-200.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45833@1" ObjectIDND1="45832@0"/></metadata>
    <path d="M 656,-214.891 L 656,-200.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 656,-112 L 656,-81" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="45834@1" ObjectIDND1="45835@0"/></metadata>
    <path d="M 656,-112 L 656,-81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 656,-263 L 656,-230.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45833@0"/></metadata>
    <path d="M 656,-263 L 656,-230.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 656,-131.625 L 656,-112" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="45834@1" ObjectIDND1="45835@0"/></metadata>
    <path d="M 656,-131.625 L 656,-112" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 681,-112 L 656,-112" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="45835@0" ObjectIDND1="45834@1"/></metadata>
    <path d="M 681,-112 L 656,-112" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1015,-171.76 L 1015,-155.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45814@0" ObjectIDND1="45812@1"/></metadata>
    <path d="M 1015,-171.76 L 1015,-155.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1015,-220.891 L 1015,-206.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45813@1" ObjectIDND1="45812@0"/></metadata>
    <path d="M 1015,-220.891 L 1015,-206.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1015,-263 L 1015,-236.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45813@0"/></metadata>
    <path d="M 1015,-263 L 1015,-236.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1015,-120 L 1015,-87" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45814@1" ObjectIDND1="45815@0" ObjectIDND2="74468@0"/></metadata>
    <path d="M 1015,-120 L 1015,-87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1015,-138.625 L 1015,-120" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45814@1" ObjectIDND1="45815@0" ObjectIDND2="74468@0"/></metadata>
    <path d="M 1015,-138.625 L 1015,-120" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1040,-120 L 1015,-120" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="thermalGenerator" ObjectIDND0="45815@0" ObjectIDND1="45814@1" ObjectIDND2="74468@0"/></metadata>
    <path d="M 1040,-120 L 1015,-120" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1119,-174.76 L 1119,-158.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45818@0" ObjectIDND1="45816@1"/></metadata>
    <path d="M 1119,-174.76 L 1119,-158.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1119,-224.891 L 1119,-209.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45817@1" ObjectIDND1="45816@0"/></metadata>
    <path d="M 1119,-224.891 L 1119,-209.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1119,-263 L 1119,-240.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45817@0"/></metadata>
    <path d="M 1119,-263 L 1119,-240.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1119,-122 L 1119,-89" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45818@1" ObjectIDND1="45819@0" ObjectIDND2="74469@0"/></metadata>
    <path d="M 1119,-122 L 1119,-89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1119,-141.625 L 1119,-122" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45818@1" ObjectIDND1="45819@0" ObjectIDND2="74469@0"/></metadata>
    <path d="M 1119,-141.625 L 1119,-122" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1145,-122 L 1119,-122" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="thermalGenerator" ObjectIDND0="45819@0" ObjectIDND1="45818@1" ObjectIDND2="74469@0"/></metadata>
    <path d="M 1145,-122 L 1119,-122" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 911,-94 L 911,-133.625" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="45841@1"/></metadata>
    <path d="M 911,-94 L 911,-133.625" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 911,-214.891 L 911,-150.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="45840@1" ObjectIDND1="45841@0"/></metadata>
    <path d="M 911,-214.891 L 911,-150.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 911,-263 L 911,-230.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45840@0"/></metadata>
    <path d="M 911,-263 L 911,-230.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1241,-169.76 L 1241,-154.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45822@0" ObjectIDND1="45820@1"/></metadata>
    <path d="M 1241,-169.76 L 1241,-154.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1241,-218.891 L 1241,-204.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45821@1" ObjectIDND1="45820@0"/></metadata>
    <path d="M 1241,-218.891 L 1241,-204.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1241,-263 L 1241,-234.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45821@0"/></metadata>
    <path d="M 1241,-263 L 1241,-234.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1241,-118 L 1241,-85" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45822@1" ObjectIDND1="45823@0" ObjectIDND2="74470@0"/></metadata>
    <path d="M 1241,-118 L 1241,-85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1241,-137.625 L 1241,-118" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45822@1" ObjectIDND1="45823@0" ObjectIDND2="74470@0"/></metadata>
    <path d="M 1241,-137.625 L 1241,-118" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1266,-118 L 1241,-118" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="thermalGenerator" ObjectIDND0="45823@0" ObjectIDND1="45822@1" ObjectIDND2="74470@0"/></metadata>
    <path d="M 1266,-118 L 1241,-118" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1357,-172.76 L 1357,-152.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45826@0" ObjectIDND1="45824@1"/></metadata>
    <path d="M 1357,-172.76 L 1357,-152.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1357,-221.891 L 1357,-207.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="45825@1" ObjectIDND1="45824@0"/></metadata>
    <path d="M 1357,-221.891 L 1357,-207.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1357,-263 L 1357,-237.297" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="45799@-1" ObjectIDND1="45825@0"/></metadata>
    <path d="M 1357,-263 L 1357,-237.297" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1357,-121 L 1357,-88" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45826@1" ObjectIDND1="45827@0" ObjectIDND2="74471@0"/></metadata>
    <path d="M 1357,-121 L 1357,-88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1357,-135.625 L 1357,-121" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="thermalGenerator" ObjectIDND0="45826@1" ObjectIDND1="45827@0" ObjectIDND2="74471@0"/></metadata>
    <path d="M 1357,-135.625 L 1357,-121" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1382,-121 L 1357,-121" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="thermalGenerator" ObjectIDND0="45827@0" ObjectIDND1="45826@1" ObjectIDND2="74471@0"/></metadata>
    <path d="M 1382,-121 L 1357,-121" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1109.41,-543.183 L 1109.77,-568.552" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" ObjectIDND0="45842@0" ObjectIDND1="45802@1"/></metadata>
    <path d="M 1109.41,-543.183 L 1109.77,-568.552" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1108.71,-418.131 L 1108.35,-447.391" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="transformer2" ObjectIDND0="45809@0" ObjectIDND1="45811@0" ObjectIDND2="45842@1"/></metadata>
    <path d="M 1108.71,-418.131 L 1108.35,-447.391" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1109.41,-516.956 L 1168.08,-517.108 L 1168.08,-449.5" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" ObjectIDND0="45842@2" ObjectIDND1="45807@0"/></metadata>
    <path d="M 1109.41,-516.956 L 1168.08,-517.108 L 1168.08,-449.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1108.72,-591.655 L 1108.68,-582.607 L 1108.44,-580.005 L 1109.77,-568.552" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" ObjectIDND0="45842@0" ObjectIDND1="45802@1"/></metadata>
    <path d="M 1108.72,-591.655 L 1108.68,-582.607 L 1108.44,-580.005 L 1109.77,-568.552" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="BusbarSectionClass">
    <g id="YH_HDGFZ.YH_HDGFZ_35#1">
      <path class="NFkV35" d="M 616,-263 L 1505,-263" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="45799" ObjectName="YH_HDGFZ.YH_HDGFZ_35#1"/><cge:TPSR_Ref TObjectID="45799"/></metadata>
    <path d="M 616,-263 L 1505,-263" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="ACLineSegmentClass">
    <g beginPointId="0" endPointId="0" id="Lleihao" runFlow="0">
      <path class="NFkV110" d="M 1108.72,-851.103 L 1108.72,-892.103" stroke-width="4"/>
      <metadata><cge:PSR_Ref ObjectID="52924" ObjectName="52924"/><cge:TPSR_Ref TObjectID="52924_SS-245"/></metadata>
    <path d="M 1108.72,-851.103 L 1108.72,-892.103" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="PowerTransformer2Class">
    <g id="YH_HDGFZ.YH_HDGFZ_1T" primType="transformer2">
      <g id="WD-0">
        <use class="kV110" height="100.464" transform="translate(1023.9,-635.272)" width="64.5088" x="57.4912" xlink:href="#Transformer2:两卷变压器5_0" y="89.5355"/>
        <metadata><cge:PSR_Ref ObjectID="45842-H"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV35" height="100.464" transform="translate(1023.9,-635.272)" width="64.5088" x="57.4912" xlink:href="#Transformer2:两卷变压器5_1" y="89.5355"/>
        <metadata><cge:PSR_Ref ObjectID="45842-L"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="45842" ObjectName="YH_HDGFZ.YH_HDGFZ_1T"/><cge:TPSR_Ref TObjectID="45842"/></metadata>
    <rect fill="white" height="100.464" opacity="0" stroke="white" transform="translate(1023.9,-635.272)" width="64.5088" x="57.4912" y="89.5355"/></g>
  </g>
  <g id="BreakerClass">
    <g id="191193" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.586207,0,0,0.96,1350.55,-167)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45824" ObjectName="YH_HDGFZ.YH_HDGFZ_316BK"/>
        <cge:Meas_Ref ObjectID="ME-191193"/><cge:TPSR_Ref TObjectID="45824"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,1350.55,-167)" width="27" x="-4" y="-48"/></g>
    <g id="191190" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.62069,0,0,0.96,1234.17,-164)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45820" ObjectName="YH_HDGFZ.YH_HDGFZ_315BK"/>
        <cge:Meas_Ref ObjectID="ME-191190"/><cge:TPSR_Ref TObjectID="45820"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.62069,0,0,0.96,1234.17,-164)" width="27" x="-4" y="-48"/></g>
    <g id="191187" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.62069,0,0,0.96,1112.17,-169)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45816" ObjectName="YH_HDGFZ.YH_HDGFZ_314BK"/>
        <cge:Meas_Ref ObjectID="ME-191187"/><cge:TPSR_Ref TObjectID="45816"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.62069,0,0,0.96,1112.17,-169)" width="27" x="-4" y="-48"/></g>
    <g id="191184" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.586207,0,0,0.96,1008.55,-166)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45812" ObjectName="YH_HDGFZ.YH_HDGFZ_313BK"/>
        <cge:Meas_Ref ObjectID="ME-191184"/><cge:TPSR_Ref TObjectID="45812"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,1008.55,-166)" width="27" x="-4" y="-48"/></g>
    <g id="191199" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.586207,0,0,0.96,649.552,-160)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45832" ObjectName="YH_HDGFZ.YH_HDGFZ_311BK"/>
        <cge:Meas_Ref ObjectID="ME-191199"/><cge:TPSR_Ref TObjectID="45832"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,649.552,-160)" width="27" x="-4" y="-48"/></g>
    <g id="191202" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.62069,0,0,0.96,787.172,-163)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45836" ObjectName="YH_HDGFZ.YH_HDGFZ_312BK"/>
        <cge:Meas_Ref ObjectID="ME-191202"/><cge:TPSR_Ref TObjectID="45836"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.62069,0,0,0.96,787.172,-163)" width="27" x="-4" y="-48"/></g>
    <g id="191196" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.586207,0,0,0.96,1468.55,-168)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45828" ObjectName="YH_HDGFZ.YH_HDGFZ_317BK"/>
        <cge:Meas_Ref ObjectID="ME-191196"/><cge:TPSR_Ref TObjectID="45828"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,1468.55,-168)" width="27" x="-4" y="-48"/></g>
    <g id="191170" primType="breaker">
      <use class="kV110" height="48" transform="matrix(0.586207,0,0,0.94,1102.27,-665.103)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45801" ObjectName="YH_HDGFZ.YH_HDGFZ_103BK"/>
        <cge:Meas_Ref ObjectID="ME-191170"/><cge:TPSR_Ref TObjectID="45801"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1102.27,-665.103)" width="27" x="-4" y="-48"/></g>
    <g id="191179" primType="breaker">
      <use class="kV35" height="48" transform="matrix(0.655172,0,0,0.96,1101.79,-324)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="45808" ObjectName="YH_HDGFZ.YH_HDGFZ_301BK"/>
        <cge:Meas_Ref ObjectID="ME-191179"/><cge:TPSR_Ref TObjectID="45808"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.655172,0,0,0.96,1101.79,-324)" width="27" x="-4" y="-48"/></g>
  </g>
  <g id="DisconnectorClass">
    <g id="191195" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="1371" xlink:href="#Disconnector:shape1_0" y="-127"/>
      <metadata><cge:PSR_Ref ObjectID="45827" ObjectName="YH_HDGFZ.YH_HDGFZ_31637SW"/>
        <cge:Meas_Ref ObjectID="ME-191195"/><cge:TPSR_Ref TObjectID="45827"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1371" y="-127"/></g>
    <g id="191194" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.90625,1347,-216)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45825" ObjectName="YH_HDGFZ.YH_HDGFZ_316XC"/>
        <cge:Meas_Ref ObjectID="ME-191194"/><cge:TPSR_Ref TObjectID="45825"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.90625,1347,-216)" width="22" x="-1" y="-30"/></g>
    <g id="191194" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,1347,-130)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45826" ObjectName="YH_HDGFZ.YH_HDGFZ_316XC1"/>
        <cge:Meas_Ref ObjectID="ME-191194"/><cge:TPSR_Ref TObjectID="45826"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,1347,-130)" width="22" x="-1" y="-30"/></g>
    <g id="191192" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="1255" xlink:href="#Disconnector:shape1_0" y="-124"/>
      <metadata><cge:PSR_Ref ObjectID="45823" ObjectName="YH_HDGFZ.YH_HDGFZ_31537SW"/>
        <cge:Meas_Ref ObjectID="ME-191192"/><cge:TPSR_Ref TObjectID="45823"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1255" y="-124"/></g>
    <g id="191191" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.90625,1231.5,-213)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45821" ObjectName="YH_HDGFZ.YH_HDGFZ_315XC"/>
        <cge:Meas_Ref ObjectID="ME-191191"/><cge:TPSR_Ref TObjectID="45821"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.90625,1231.5,-213)" width="22" x="-1" y="-30"/></g>
    <g id="191191" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.9375,1231.5,-132)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45822" ObjectName="YH_HDGFZ.YH_HDGFZ_315XC1"/>
        <cge:Meas_Ref ObjectID="ME-191191"/><cge:TPSR_Ref TObjectID="45822"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.9375,1231.5,-132)" width="22" x="-1" y="-30"/></g>
    <g id="191205" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,901,-128)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45841" ObjectName="YH_HDGFZ.YH_HDGFZ_0351XC1"/>
        <cge:Meas_Ref ObjectID="ME-191205"/><cge:TPSR_Ref TObjectID="45841"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,901,-128)" width="22" x="-1" y="-30"/></g>
    <g id="191205" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.90625,901,-209)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45840" ObjectName="YH_HDGFZ.YH_HDGFZ_0351XC"/>
        <cge:Meas_Ref ObjectID="ME-191205"/><cge:TPSR_Ref TObjectID="45840"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.90625,901,-209)" width="22" x="-1" y="-30"/></g>
    <g id="191189" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="1134" xlink:href="#Disconnector:shape1_0" y="-128"/>
      <metadata><cge:PSR_Ref ObjectID="45819" ObjectName="YH_HDGFZ.YH_HDGFZ_31437SW"/>
        <cge:Meas_Ref ObjectID="ME-191189"/><cge:TPSR_Ref TObjectID="45819"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1134" y="-128"/></g>
    <g id="191188" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.90625,1109.5,-219)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45817" ObjectName="YH_HDGFZ.YH_HDGFZ_314XC"/>
        <cge:Meas_Ref ObjectID="ME-191188"/><cge:TPSR_Ref TObjectID="45817"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.90625,1109.5,-219)" width="22" x="-1" y="-30"/></g>
    <g id="191188" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.9375,1109.5,-136)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45818" ObjectName="YH_HDGFZ.YH_HDGFZ_314XC1"/>
        <cge:Meas_Ref ObjectID="ME-191188"/><cge:TPSR_Ref TObjectID="45818"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.9375,1109.5,-136)" width="22" x="-1" y="-30"/></g>
    <g id="191186" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="1029" xlink:href="#Disconnector:shape1_0" y="-126"/>
      <metadata><cge:PSR_Ref ObjectID="45815" ObjectName="YH_HDGFZ.YH_HDGFZ_31337SW"/>
        <cge:Meas_Ref ObjectID="ME-191186"/><cge:TPSR_Ref TObjectID="45815"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1029" y="-126"/></g>
    <g id="191185" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.90625,1005,-215)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45813" ObjectName="YH_HDGFZ.YH_HDGFZ_313XC"/>
        <cge:Meas_Ref ObjectID="ME-191185"/><cge:TPSR_Ref TObjectID="45813"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.90625,1005,-215)" width="22" x="-1" y="-30"/></g>
    <g id="191185" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,1005,-133)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45814" ObjectName="YH_HDGFZ.YH_HDGFZ_313XC1"/>
        <cge:Meas_Ref ObjectID="ME-191185"/><cge:TPSR_Ref TObjectID="45814"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,1005,-133)" width="22" x="-1" y="-30"/></g>
    <g id="191200" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.90625,646,-209)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45833" ObjectName="YH_HDGFZ.YH_HDGFZ_311XC"/>
        <cge:Meas_Ref ObjectID="ME-191200"/><cge:TPSR_Ref TObjectID="45833"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.90625,646,-209)" width="22" x="-1" y="-30"/></g>
    <g id="191200" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,646,-126)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45834" ObjectName="YH_HDGFZ.YH_HDGFZ_311XC1"/>
        <cge:Meas_Ref ObjectID="ME-191200"/><cge:TPSR_Ref TObjectID="45834"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,646,-126)" width="22" x="-1" y="-30"/></g>
    <g id="191201" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="670" xlink:href="#Disconnector:shape1_0" y="-118"/>
      <metadata><cge:PSR_Ref ObjectID="45835" ObjectName="YH_HDGFZ.YH_HDGFZ_31137SW"/>
        <cge:Meas_Ref ObjectID="ME-191201"/><cge:TPSR_Ref TObjectID="45835"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="670" y="-118"/></g>
    <g id="191204" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="808" xlink:href="#Disconnector:shape1_0" y="-123"/>
      <metadata><cge:PSR_Ref ObjectID="45839" ObjectName="YH_HDGFZ.YH_HDGFZ_31237SW"/>
        <cge:Meas_Ref ObjectID="ME-191204"/><cge:TPSR_Ref TObjectID="45839"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="808" y="-123"/></g>
    <g id="191203" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.90625,784.5,-212)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45837" ObjectName="YH_HDGFZ.YH_HDGFZ_312XC"/>
        <cge:Meas_Ref ObjectID="ME-191203"/><cge:TPSR_Ref TObjectID="45837"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.90625,784.5,-212)" width="22" x="-1" y="-30"/></g>
    <g id="191203" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(0.95,0,0,0.9375,784.5,-131)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45838" ObjectName="YH_HDGFZ.YH_HDGFZ_312XC1"/>
        <cge:Meas_Ref ObjectID="ME-191203"/><cge:TPSR_Ref TObjectID="45838"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(0.95,0,0,0.9375,784.5,-131)" width="22" x="-1" y="-30"/></g>
    <g id="191198" primType="switch">
      <use class="kV35" height="57" transform="matrix(1,0,0,1,0,0)" width="20" x="1489" xlink:href="#Disconnector:shape1_0" y="-128"/>
      <metadata><cge:PSR_Ref ObjectID="45831" ObjectName="YH_HDGFZ.YH_HDGFZ_31737SW"/>
        <cge:Meas_Ref ObjectID="ME-191198"/><cge:TPSR_Ref TObjectID="45831"/></metadata>
    <rect fill="white" height="57" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1489" y="-128"/></g>
    <g id="191197" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.90625,1465,-217)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45829" ObjectName="YH_HDGFZ.YH_HDGFZ_317XC"/>
        <cge:Meas_Ref ObjectID="ME-191197"/><cge:TPSR_Ref TObjectID="45829"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.90625,1465,-217)" width="22" x="-1" y="-30"/></g>
    <g id="191197" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,1465,-136)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45830" ObjectName="YH_HDGFZ.YH_HDGFZ_317XC1"/>
        <cge:Meas_Ref ObjectID="ME-191197"/><cge:TPSR_Ref TObjectID="45830"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,1465,-136)" width="22" x="-1" y="-30"/></g>
    <g id="191174" primType="switch">
      <use class="kV110" height="20" transform="matrix(1,0,0,1,0,0)" width="57" x="1128.95" xlink:href="#Disconnector:shape5_0" y="-731.103"/>
      <metadata><cge:PSR_Ref ObjectID="45805" ObjectName="YH_HDGFZ.YH_HDGFZ_10337SW"/>
        <cge:Meas_Ref ObjectID="ME-191174"/><cge:TPSR_Ref TObjectID="45805"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="57" x="1128.95" y="-731.103"/></g>
    <g id="191173" primType="switch">
      <use class="kV110" height="20" transform="matrix(1,0,0,1,0,0)" width="57" x="1131.95" xlink:href="#Disconnector:shape5_0" y="-663.103"/>
      <metadata><cge:PSR_Ref ObjectID="45804" ObjectName="YH_HDGFZ.YH_HDGFZ_10317SW"/>
        <cge:Meas_Ref ObjectID="ME-191173"/><cge:TPSR_Ref TObjectID="45804"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="57" x="1131.95" y="-663.103"/></g>
    <g id="191171" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.06383,1093.91,-565.103)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="45802" ObjectName="YH_HDGFZ.YH_HDGFZ_1031SW"/>
        <cge:Meas_Ref ObjectID="ME-191171"/><cge:TPSR_Ref TObjectID="45802"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.06383,1093.91,-565.103)" width="16" x="5" y="-63"/></g>
    <g id="191172" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.06383,1093.91,-715.103)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="45803" ObjectName="YH_HDGFZ.YH_HDGFZ_1033SW"/>
        <cge:Meas_Ref ObjectID="ME-191172"/><cge:TPSR_Ref TObjectID="45803"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.06383,1093.91,-715.103)" width="16" x="5" y="-63"/></g>
    <g id="191175" primType="switch">
      <use class="kV110" height="22" transform="matrix(1,0,0,1,0,0)" width="52" x="1131.48" xlink:href="#Disconnector:shape5_0" y="-811.015"/>
      <metadata><cge:PSR_Ref ObjectID="45806" ObjectName="YH_HDGFZ.YH_HDGFZ_10338SW"/>
        <cge:Meas_Ref ObjectID="ME-191175"/><cge:TPSR_Ref TObjectID="45806"/></metadata>
    <rect fill="white" height="22" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="52" x="1131.48" y="-811.015"/></g>
    <g id="191180" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.9375,1099,-381)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45809" ObjectName="YH_HDGFZ.YH_HDGFZ_301XC"/>
        <cge:Meas_Ref ObjectID="ME-191180"/><cge:TPSR_Ref TObjectID="45809"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.9375,1099,-381)" width="22" x="-1" y="-30"/></g>
    <g id="191180" primType="xcswitch">
      <use class="kV35" height="30" transform="matrix(1,0,0,0.96875,1099,-288)" width="22" x="-1" xlink:href="#Disconnector:shape3_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="45810" ObjectName="YH_HDGFZ.YH_HDGFZ_301XC1"/>
        <cge:Meas_Ref ObjectID="ME-191180"/><cge:TPSR_Ref TObjectID="45810"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1099,-288)" width="22" x="-1" y="-30"/></g>
    <g id="191178" primType="switch">
      <use class="kV110" height="53" transform="matrix(1,0,0,1,0,0)" width="22" x="1156.15" xlink:href="#Disconnector:shape1_0" y="-455.079"/>
      <metadata><cge:PSR_Ref ObjectID="45807" ObjectName="YH_HDGFZ.YH_HDGFZ_1010SW"/>
        <cge:Meas_Ref ObjectID="ME-191178"/><cge:TPSR_Ref TObjectID="45807"/></metadata>
    <rect fill="white" height="53" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1156.15" y="-455.079"/></g>
    <g id="191181" primType="switch">
      <use class="kV35" height="53" transform="matrix(1,0,0,1,0,0)" width="22" x="994.07" xlink:href="#Disconnector:shape1_0" y="-393.579"/>
      <metadata><cge:PSR_Ref ObjectID="45811" ObjectName="YH_HDGFZ.YH_HDGFZ_30137SW"/>
        <cge:Meas_Ref ObjectID="ME-191181"/><cge:TPSR_Ref TObjectID="45811"/></metadata>
    <rect fill="white" height="53" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="994.07" y="-393.579"/></g>
  </g>
  <g id="GeneratorClass">
    <g id="DEV-0">
      <use class="kV35" height="35.7749" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="997.113" xlink:href="#Generator:generator_0" y="-89.285"/>
      <metadata><cge:PSR_Ref ObjectID="74468" ObjectName="YH_HDGFZ.#1JZ"/><cge:TPSR_Ref TObjectID="74468"/></metadata>
    <rect fill="white" height="35.7749" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="997.113" y="-89.285"/></g>
    <g id="DEV-0">
      <use class="kV35" height="35.7749" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1101.11" xlink:href="#Generator:generator_0" y="-91.285"/>
      <metadata><cge:PSR_Ref ObjectID="74469" ObjectName="YH_HDGFZ.#2JZ"/><cge:TPSR_Ref TObjectID="74469"/></metadata>
    <rect fill="white" height="35.7749" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1101.11" y="-91.285"/></g>
    <g id="DEV-0">
      <use class="kV35" height="35.7749" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1223.11" xlink:href="#Generator:generator_0" y="-87.285"/>
      <metadata><cge:PSR_Ref ObjectID="74470" ObjectName="YH_HDGFZ.#3JZ"/><cge:TPSR_Ref TObjectID="74470"/></metadata>
    <rect fill="white" height="35.7749" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1223.11" y="-87.285"/></g>
    <g id="DEV-0">
      <use class="kV35" height="35.7749" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1339.11" xlink:href="#Generator:generator_0" y="-90.285"/>
      <metadata><cge:PSR_Ref ObjectID="74471" ObjectName="YH_HDGFZ.#4JZ"/><cge:TPSR_Ref TObjectID="74471"/></metadata>
    <rect fill="white" height="35.7749" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1339.11" y="-90.285"/></g>
    <g id="DEV-0">
      <use class="kV35" height="35.7749" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1457.11" xlink:href="#Generator:generator_0" y="-91.285"/>
      <metadata><cge:PSR_Ref ObjectID="74472" ObjectName="YH_HDGFZ.#5JZ"/><cge:TPSR_Ref TObjectID="74472"/></metadata>
    <rect fill="white" height="35.7749" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35.7749" x="1457.11" y="-91.285"/></g>
  </g>
  <g id="EnergyConsumerClass">
    <g id="DEV-0">
      <use class="kV35" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="784" xlink:href="#EnergyConsumer:shape1_0" y="-90"/>
      <metadata><cge:PSR_Ref ObjectID="75075" ObjectName="YH_HDGFZ.312Ld"/><cge:TPSR_Ref TObjectID="75075"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="784" y="-90"/></g>
  </g>
  <g id="OtherClass">
    <g id="DEV-0">
      <use class="kV35" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="646" xlink:href="#Other:shape1_0" y="-87"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="646" y="-87"/></g>
    <g id="DEV-0">
      <use class="kV35" height="28" transform="matrix(1,0,0,1,0,0)" width="29" x="896" xlink:href="#Other:shape47_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="28" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="29" x="896" y="-100"/></g>
  </g>
  <g id="Base_MotifButtonClass">
    <g>
      
    <metadata/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="564.82,-818.14 564.82,-758.331 567.82,-761.331 567.82,-815.14 821.947,-815.14 824.947,-818.14" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="824.947,-758.331 564.82,-758.331 567.82,-761.331 821.947,-761.331 821.947,-815.14 824.947,-818.14" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="53.809" stroke="rgb(248,248,255)" width="254.127" x="567.82" y="-815.14"/>
    <metadata/><rect fill="white" height="53.809" opacity="0" stroke="white" transform="" width="254.127" x="567.82" y="-815.14"/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="563.215,-739.835 563.215,-671.973 566.215,-674.973 566.215,-736.835 858.131,-736.835 861.131,-739.835" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="861.131,-671.973 563.215,-671.973 566.215,-674.973 858.131,-674.973 858.131,-736.835 861.131,-739.835" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="61.8618" stroke="rgb(248,248,255)" width="291.916" x="566.215" y="-736.835"/>
    <metadata/><rect fill="white" height="61.8618" opacity="0" stroke="white" transform="" width="291.916" x="566.215" y="-736.835"/></g>
  </g>
  <g id="TextClass">
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="365" y="-238">危险点说明：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="365" y="-422">事故总信号：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="365" y="-130">联系方式：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="365" y="-472">总无功：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="366" y="-527">总有功：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="1186.81" y="-489.726">档位：</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="44" stroke="rgb(0,0,0)" transform="matrix(0.907895,0,0,0.73913,327.149,-1252.57)" y="629">浩德光伏站</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1118.95" y="-681.103">103</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1117.95" y="-751.103">3</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1146.95" y="-816.103">38</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1146.95" y="-735.103">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1120.95" y="-605.103">1</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1145.95" y="-667.103">17</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1121" y="-341">301</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1182.38" y="-418.488">1010</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="616" y="-277">35kV#1段</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1484" y="-184">317</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1510" y="-94">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1276" y="-90">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1251" y="-180">315</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1155" y="-94">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1130" y="-184">314</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1050" y="-92">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1024" y="-182">313</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="829" y="-89">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="804" y="-179">312</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="691" y="-86">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="665" y="-176">311</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1395" y="-93">37</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1367" y="-183">316</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="886" y="-1">母线PT</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="617" y="-4">＃1接地变</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="767" y="-1">＃1SVG</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1003" y="-3">馈线1</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1110" y="-3">馈线2</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1231" y="-3">馈线3</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1354" y="-2">馈线4</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1468" y="-2">馈线5</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="24" stroke="rgb(255,255,255)" x="1156.68" y="-534.705">#1主变</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="24" stroke="rgb(255,250,255)" x="1187.25" y="-458.767">绕温：</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="24" stroke="rgb(255,255,255)" x="1020.07" y="-357.079">37</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="24" stroke="rgb(255,255,255)" x="1072.22" y="-911.286">雷浩线</text>
    <text fill="rgb(0,0,0)" font-family="Serif" font-size="29" stroke="rgb(0,0,0)" x="575.536" y="-777.488">新能源AGC监视图</text>
    <text fill="rgb(0,0,0)" font-family="Serif" font-size="29" stroke="rgb(0,0,0)" x="570.851" y="-696.794">新能源场站AVC监视图</text>
  </g>
  <g id="LinkPointClass">
    <g href="110kV用户_浩德光伏站35_110kV_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1118.95" y="-681.103">103</text></g>
    <g href="110kV用户_浩德光伏站35_110kV_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1121" y="-341">301</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_317间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1484" y="-184">317</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_315间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1251" y="-180">315</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_314间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1130" y="-184">314</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_313间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1024" y="-182">313</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_312间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="804" y="-179">312</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_311间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="665" y="-176">311</text></g>
    <g href="110kV用户_浩德光伏站YH_HDGFZ_316间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,250,255)" font-family="SimSun" font-size="19" stroke="rgb(255,250,255)" x="1367" y="-183">316</text></g>
    <g href="nn_一次接线索引图_35kv20210522.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="77" stroke-width="1" width="299" x="244" y="-833"/></g>
    <g href="110kV用户_浩德光伏站35_110kV_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Serif" font-size="24" stroke="rgb(255,255,255)" x="1156.68" y="-534.705">#1主变</text></g>
    <g href="agc_monInfo_n.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(0,0,0)" font-family="Serif" font-size="29" stroke="rgb(0,0,0)" x="575.536" y="-777.488">新能源AGC监视图</text></g>
    <g href="新能源场站AVC监视图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="61.8832" stroke-width="1" width="288.768" x="571.123" y="-736.338"/></g>
  </g>
</svg>