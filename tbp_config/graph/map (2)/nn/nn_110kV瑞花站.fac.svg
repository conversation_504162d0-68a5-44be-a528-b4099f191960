<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://www.cim.com" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:dfg="http://dfg.dongfang-china.com/2010/SVGExtensions/MX" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-401" id="thSvg" viewBox="-207 -2079.68 3833.27 1987.36">
  <defs>
    <style type="text/css"><![CDATA[
      .def {stroke:#a0a0a4;fill:#a0a0a4}
      .kV500 {stroke:#ff0000;fill:#ff0000}
      .kV220 {stroke:#ffffff;fill:#ffffff}
      .kV110 {stroke:#aa557f;fill:#aa557f}
      .kV1 {stroke:#cc0000;fill:#cc0000}
      .kV35 {stroke:#ffff00;fill:#ffff00}
      .kVdisp {stroke:#cc0000;fill:#cc0000}
      .kV10 {stroke:#00ff00;fill:#00ff00}
      .kV11 {stroke:#ffff00;fill:#ffff00}
      .kV6 {stroke:#5db5b9;fill:#5db5b9}
      .JieDi {stroke:#aaaa7f;fill:#aaaa7f}
      .BuDaiDian {stroke:#3c78b4;fill:#3c78b4}
      .BuQueDing {stroke:#cccccc;fill:#cccccc}
      .DianYuanDian {stroke:#d673a6;fill:#d673a6}
      .QuYuGD {stroke:#00ffe2;fill:#00ffe2}
      .AllLevel {stroke:#ccec82;fill:#ccec82}
      .LoadLine {stroke:#ae74b3;fill:#ae74b3}
      .IsLand0 {stroke:#f8e576;fill:#f8e576}
      .IsLand1 {stroke:#a5ffab;fill:#a5ffab}
      .IsLand2 {stroke:#aef2ff;fill:#aef2ff}
      .IsLand3 {stroke:#b6b1fc;fill:#b6b1fc}
      .IsLand4 {stroke:#deafff;fill:#deafff}
      .IsLand5 {stroke:#ff9ec3;fill:#ff9ec3}
      .kV20 {stroke:#ddbf1b;fill:#ddbf1b}
      .NFkV500 {stroke:#ff0000;fill:none}
      .NFkV220 {stroke:#ffffff;fill:none}
      .NFkV110 {stroke:#aa557f;fill:none}
      .NFkV1 {stroke:#cc0000;fill:none}
      .NFkV35 {stroke:#ffff00;fill:none}
      .NFkVdisp {stroke:#cc0000;fill:none}
      .NFkV10 {stroke:#00ff00;fill:none}
      .NFkV11 {stroke:#ffff00;fill:none}
      .NFkV6 {stroke:#5db5b9;fill:none}
      .NFJieDi {stroke:#aaaa7f;fill:none}
      .NFBuDaiDian {stroke:#3c78b4;fill:none}
      .NFBuQueDing {stroke:#cccccc;fill:none}
      .NFDianYuanDian {stroke:#d673a6;fill:none}
      .NFQuYuGD {stroke:#00ffe2;fill:none}
      .NFAllLevel {stroke:#ccec82;fill:none}
      .NFLoadLine {stroke:#ae74b3;fill:none}
      .NFIsLand0 {stroke:#f8e576;fill:none}
      .NFIsLand1 {stroke:#a5ffab;fill:none}
      .NFIsLand2 {stroke:#aef2ff;fill:none}
      .NFIsLand3 {stroke:#b6b1fc;fill:none}
      .NFIsLand4 {stroke:#deafff;fill:none}
      .NFIsLand5 {stroke:#ff9ec3;fill:none}
      .NFkV20 {stroke:#ddbf1b;fill:none}
    ]]></style>
    <symbol id="terminal" viewBox="-3 -3 6 6" visibility="hidden">
      <circle cx="0" cy="0" fill="none" r="2" stroke="rgb(255,255,0)"/>
    </symbol>
    <symbol dfg:desc="限电" dfg:flg="g_si" id="Tag:shape0" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-5)">限电</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="保安电" dfg:flg="g_si" id="Tag:shape1" viewBox="-2 -27 60 29">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,1,-7)">保安电</text>
      <rect fill="none" height="24" stroke="#ff0000" stroke-width="1" width="56" x="0" y="-25"/>
    </symbol>
    <symbol dfg:desc="间隔检修" dfg:flg="g_si" id="Tag:shape2" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止遥控" dfg:flg="g_si" id="Tag:shape3" viewBox="-2 -41 26 23">
      <text fill="#ff0000" font-family="SimSun" font-size="11" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.5,-33.75)" y="11">禁控</text>
      <rect fill="none" height="18" stroke="#ff0000" width="22" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="禁止告警" dfg:flg="g_si" id="Tag:shape4" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-21)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-3)">告警</text>
    </symbol>
    <symbol dfg:desc="线路检修" dfg:flg="g_si" id="Tag:shape5" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止刷新" dfg:flg="g_si" id="Tag:shape6" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-4)">刷新</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="引下线一组" dfg:flg="g_si" id="Tag:shape7" viewBox="-2 -42 46 44">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-21)">引线</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-3)">下一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-40"/>
    </symbol>
    <symbol dfg:desc="小电源" dfg:flg="g_si" id="Tag:shape8" viewBox="-2 -41 24 24">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,7,-70)" y="48"/>
      <text fill="#ff0000" font-family="SimSun" font-size="12" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,-0.198305,-57.3351)" y="35">敏感</text>
      <rect fill="none" height="20" stroke="#ff0000" width="20" x="0.187167" y="-38.6676"/>
    </symbol>
    <symbol dfg:desc="注释1" dfg:flg="g_si" id="Tag:shape9" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">注释</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一级保供" dfg:flg="g_si" id="Tag:shape10" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-23)">一级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-5)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="二级保供" dfg:flg="g_si" id="Tag:shape11" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-22)">二级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="退出运行" dfg:flg="g_si" id="Tag:shape12" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">退出</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">运行</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="新设备未投运" dfg:flg="g_si" id="Tag:shape13" viewBox="-2 -38 79 40">
      <rect fill="none" height="34" stroke="#ff0000" stroke-width="1" width="75" x="0" y="-36"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-21)">新设备</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-3)">未投运</text>
    </symbol>
    <symbol dfg:desc="自愈" dfg:flg="g_si" id="Tag:shape14" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,5,-21)" y="16">自愈</text>
      <rect fill="none" height="22" stroke="#ff0000" width="42" x="0.5" y="-21.5"/>
    </symbol>
    <symbol dfg:desc="禁止操作" dfg:flg="g_si" id="Tag:shape15" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">操作</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="站控" dfg:flg="g_si" id="Tag:shape16" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-5)">站控</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="带电作业" dfg:flg="g_si" id="Tag:shape17" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">带电</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">作业</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="重合闸退出" dfg:flg="g_si" id="Tag:shape18" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">重合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="备自投退出" dfg:flg="g_si" id="Tag:shape19" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">备自投</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="保护退出" dfg:flg="g_si" id="Tag:shape20" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,0.75,-36.5)" y="16">配线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3,-19)" y="16">检 修</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="冷备用" dfg:flg="g_si" id="Tag:shape21" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)"> 冷</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">备用</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="调试一" dfg:flg="g_si" id="Tag:shape22" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">调试</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="全站停电检修" dfg:flg="g_si" id="Tag:shape23" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">全站</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">检修</text>
    </symbol>
    <symbol dfg:desc="特级保供" dfg:flg="g_si" id="Tag:shape24" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">特级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一般保供" dfg:flg="g_si" id="Tag:shape25" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-24)">一般</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-6)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="三级保供" dfg:flg="g_si" id="Tag:shape26" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.48886,-39)" y="16">三级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.25981,-20.7709)" y="16">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-39.1145"/>
    </symbol>
    <symbol dfg:desc="禁止合闸,有人工作" dfg:flg="g_si" id="Tag:shape27" viewBox="-2 -67 109 69">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">有人工作</text>
      <rect fill="none" height="34" stroke="#ff0000" width="75" x="30" y="-38"/>
      <circle cx="14" cy="-51" fill="none" r="14" stroke="#ff0000"/>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-60" y2="-35"/>
    </symbol>
    <symbol dfg:desc="禁止合闸线路有人工作" dfg:flg="g_si" id="Tag:shape28" viewBox="-2 -85 112 87">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-54)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">线路有人</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">工作</text>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-78" y2="-53"/>
      <circle cx="14" cy="-69" fill="none" r="14" stroke="#ff0000"/>
      <rect fill="none" height="55" stroke="#ff0000" width="78" x="30" y="-56"/>
    </symbol>
    <symbol dfg:desc="间隔检修2" dfg:flg="g_si" id="Tag:shape29" viewBox="-2 -41 57 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-37)" y="15">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-19)" y="15">检修2</text>
      <rect fill="none" height="38" stroke="#ff0000" width="53" x="0" y="-39"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Breaker:shape3_0" viewBox="0 -28 48 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Breaker:shape3_1" viewBox="1 -29 46 27">
      <rect fill="rgb(0,255,0)" height="23" stroke-width="2" width="32" x="8" y="-27"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Breaker:shape3_2" viewBox="0 -28 48 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <polyline fill="none" points="9,-23 41,-7" stroke-width="2"/>
      <polyline fill="none" points="41,-23 9,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Breaker:shape3_3" viewBox="2 -28 44 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <polyline fill="none" points="40,-23 8,-7" stroke-width="2"/>
      <polyline fill="none" points="8,-23 40,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Breaker:shape2_0" viewBox="-4 -48 27 48">
      <rect fill="none" height="34" stroke-width="2" width="23" x="-2" y="-41"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Breaker:shape2_1" viewBox="-3 -47 27 46">
      <rect fill="rgb(0,255,0)" height="32" stroke-width="2" width="23" x="-1" y="-40"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Breaker:shape2_2" viewBox="-4 -48 28 48">
      <rect fill="none" height="35" stroke-width="2" width="24" x="-2" y="-41"/>
      <polyline fill="none" points="19,-39 3,-6" stroke-width="2"/>
      <polyline fill="none" points="18,-6 2,-39" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Breaker:shape2_3" viewBox="-4 -46 28 44">
      <rect fill="none" height="33" stroke-width="2" width="24" x="-2" y="-40"/>
      <polyline fill="none" points="18,-7 2,-40" stroke-width="2"/>
      <polyline fill="none" points="19,-40 3,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape0_0" viewBox="0 0 20 50">
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="47.7308" y2="37.5586"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="12.0711" y2="1.91098"/>
      <line stroke-width="2" x1="1.5339" x2="11.1196" y1="14.6835" y2="38.0229"/>
      <line stroke-width="2" x1="7.79317" x2="14.424" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape0_1" viewBox="0 0 20 50">
      <line stroke-width="2" x1="7.77069" x2="14.4465" y1="11.5353" y2="11.5353"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.95842" y2="47.8157"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape0_2" viewBox="0 0 20 50">
      <line stroke-width="2" x1="5.91257" x2="16.3046" y1="13.614" y2="39.8683"/>
      <line stroke-width="2" x1="17.1558" x2="5.06136" y1="13.7286" y2="39.8683"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.96389" y2="48.2042"/>
      <line stroke-width="2" x1="7.80206" x2="14.4151" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape0_3" viewBox="0 0 20 50">
      <polyline fill="none" points="17.1086,13.8684 5.10859,39.8683" stroke-width="2"/>
      <polyline fill="none" points="5.8936,13.6195 16.3236,39.8683" stroke-width="2"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.7049" y2="47.8157"/>
      <line stroke-width="2" x1="7.78951" x2="14.4277" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape5_0" viewBox="-1 -18 57 20">
      <line stroke-width="1" x1="14" x2="14" y1="-8" y2="-6"/>
      <line stroke-width="1" x1="38" x2="13" y1="-7" y2="-16"/>
      <line stroke-width="1" x1="38" x2="47" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="5" x2="14" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="54" x2="54" y1="-9" y2="-6"/>
      <line stroke-width="1" x1="51" x2="51" y1="-10" y2="-5"/>
      <line stroke-width="1" x1="47" x2="47" y1="-14" y2="0"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape5_1" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape5_2" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape5_3" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape1_0" viewBox="8 -100 20 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-61" y2="-52"/>
      <line stroke-width="1" x1="19" x2="10" y1="-61" y2="-86"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape1_1" viewBox="10 -100 18 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-52"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape1_2" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape1_3" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape4_0" viewBox="-1 -16 48 17">
      <line stroke-width="1" x1="14" x2="39" y1="-5" y2="-14"/>
      <line stroke-width="1" x1="42" x2="34" y1="-5" y2="-5"/>
      <line stroke-width="1" x1="34" x2="34" y1="-7" y2="-4"/>
      <line stroke-width="1" x1="14" x2="5" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape4_1" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape4_2" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape4_3" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape2_0" viewBox="-1 -30 22 29">
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-5.0564"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape2_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape2_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="9.7856" x2="19.1085" y1="-12.363" y2="-21.6112"/>
      <line x1="10.2755" x2="1.03594" y1="-12.2144" y2="-21.8412"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape2_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="19.1085" x2="9.95129" y1="-21.6451" y2="-12.5633"/>
      <line x1="10.2968" x2="0.939632" y1="-12.3327" y2="-21.8464"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape3_0" viewBox="-1 -30 22 30">
      <line x1="10" x2="1" y1="-15" y2="-24"/>
      <line x1="19" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="1" y1="-7" y2="-16"/>
      <line x1="19" x2="10" y1="-24" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-24"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape3_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape3_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="9.78968" x2="0.951774" y1="-17.3132" y2="-8.47532"/>
      <line x1="10.2429" x2="19.0808" y1="-17.2679" y2="-8.43"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape3_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="0.986614" x2="9.98946" y1="-8.42733" y2="-17.4713"/>
      <line x1="9.98946" x2="19.1979" y1="-17.4302" y2="-8.34511"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_si" id="EnergyConsumer:shape1_0" viewBox="-1 -33 20 34">
      <line stroke-width="2" x1="9" x2="9" y1="-27" y2="-3"/>
      <polyline fill="none" points="1,-13 9,-1 17,-13" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="6" xlink:href="#terminal" y="-30"/>
    </symbol>
    <symbol dfg:flg="g_po" dfg:val="2" id="Transformer2:shape12_1" viewBox="-1 -93 61 92">
      <ellipse cx="29" cy="-62" fill="none" rx="24" ry="24.5" stroke-width="2"/>
      <line stroke-width="2" x1="29" x2="29" y1="-60" y2="-68"/>
      <line stroke-width="2" x1="29" x2="37" y1="-68" y2="-76"/>
      <line stroke-width="2" x1="21" x2="29" y1="-76" y2="-68"/>
      <line stroke-width="2" x1="50" x2="50" y1="-80" y2="-80"/>
      <line stroke-width="2" x1="1" x2="58" y1="-53" y2="-86"/>
      <use height="8" terminal-index="0" width="8" x="26" xlink:href="#terminal" y="-89"/>
      <use height="8" terminal-index="2" width="8" x="26" xlink:href="#terminal" y="-71.25"/>
    </symbol>
    <symbol dfg:flg="g_po" dfg:val="2" id="Transformer2:shape12_0" viewBox="-1 -93 61 92">
      <circle cx="29" cy="-31" fill="none" r="24" stroke-width="2"/>
      <line stroke-width="2" x1="40" x2="24" y1="-26" y2="-35"/>
      <line stroke-width="2" x1="40" x2="24" y1="-26" y2="-18"/>
      <line stroke-width="2" x1="24" x2="24" y1="-35" y2="-18"/>
      <use height="8" terminal-index="1" width="8" x="27.25" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="GroundLine:shape0_0" viewBox="-2 -32 22 32">
      <line stroke-width="1" x1="7" x2="11" y1="-2" y2="-2"/>
      <line stroke-width="1" x1="9" x2="9" y1="-27" y2="-9"/>
      <line stroke-width="1" x1="0" x2="18" y1="-9" y2="-9"/>
      <line stroke-width="1" x1="6" x2="13" y1="-6" y2="-6"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-29"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape36_0" viewBox="-1 -51 17 51">
      <rect fill="none" height="19" stroke-width="1" width="8" x="4" y="-40"/>
      <line stroke-width="1" x1="6" x2="9" y1="-2" y2="-2"/>
      <line stroke-width="1" x1="5" x2="11" y1="-5" y2="-5"/>
      <line stroke-width="1" x1="1" x2="14" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="8" x2="8" y1="-21" y2="-7"/>
      <line stroke-width="1" x1="7" x2="7" y1="-46" y2="-27"/>
      <line stroke-width="1" x1="6" x2="7" y1="-29" y2="-27"/>
      <line stroke-width="1" x1="8" x2="9" y1="-27" y2="-29"/>
      <use height="8" terminal-index="0" width="8" x="4" xlink:href="#terminal" y="-48"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape63_0" viewBox="-1 -26 25 26">
      <circle cx="16" cy="-8" fill="none" r="6" stroke-width="2"/>
      <circle cx="11" cy="-15" fill="none" r="6" stroke-width="2"/>
      <circle cx="7" cy="-8" fill="none" r="6" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8.5" xlink:href="#terminal" y="-24.5"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape39_0" viewBox="-1 -16 52 18">
      <line stroke-width="1" x1="49" x2="49" y1="-6" y2="-9"/>
      <rect fill="none" height="8" stroke-width="1" width="18" x="11" y="-11"/>
      <line stroke-width="1" x1="24" x2="22" y1="-7" y2="-9"/>
      <line stroke-width="1" x1="22" x2="24" y1="-5" y2="-7"/>
      <line stroke-width="1" x1="4" x2="24" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="29" x2="43" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="43" x2="43" y1="0" y2="-14"/>
      <line stroke-width="1" x1="46" x2="46" y1="-4" y2="-10"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape37_0" viewBox="-1 -53 17 52">
      <rect fill="none" height="19" stroke-width="2" width="8" x="3" y="-36"/>
      <line stroke-width="2" x1="10" x2="7" y1="-26" y2="-29"/>
      <line stroke-width="2" x1="8" x2="8" y1="-29" y2="-29"/>
      <line stroke-width="2" x1="4" x2="7" y1="-26" y2="-29"/>
      <line stroke-width="2" x1="7" x2="7" y1="-37" y2="-45"/>
      <line stroke-width="2" x1="7" x2="7" y1="-8" y2="-27"/>
      <line stroke-width="2" x1="14" x2="1" y1="-45" y2="-45"/>
      <line stroke-width="2" x1="10" x2="4" y1="-48" y2="-48"/>
      <line stroke-width="2" x1="9" x2="6" y1="-51" y2="-51"/>
      <use height="8" terminal-index="0" width="8" x="4" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape56_0" viewBox="-1 -41 18 42">
      <circle cx="8" cy="-20" fill="none" r="7" stroke-width="1"/>
      <line stroke-width="1" x1="8" x2="8" y1="-36" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="5" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="5" xlink:href="#terminal" y="-38"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="DynamicPoint:shape48_0" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="DynamicPoint:shape48_1" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="DynamicPoint:shape48_2" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="DynamicPoint:shape48_3" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="DynamicPoint:shape47_0" viewBox="0 -56 108 56">
      <rect fill="none" height="52" stroke="#00ff00" stroke-width="1" width="104" x="2" y="-54"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="DynamicPoint:shape47_1" viewBox="0 -56 108 56">
      <rect fill="none" height="52" stroke="#00ff00" stroke-width="1" width="104" x="2" y="-54"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="DynamicPoint:shape47_2" viewBox="-1 -29 55 30">
      <rect fill="#ff0000" height="26" stroke="#ff0000" stroke-width="1" width="51" x="1" y="-27"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="DynamicPoint:shape47_3" viewBox="-1 -29 55 30">
      <rect fill="#ff0000" height="26" stroke="#ff0000" stroke-width="1" width="51" x="1" y="-27"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape10_0" viewBox="-1 -46 44 47">
      <ellipse cx="21" cy="-20" fill="none" rx="20" ry="19.5" stroke-width="2"/>
      <line stroke-width="2" x1="13" x2="20" y1="-27" y2="-21"/>
      <line stroke-width="2" x1="28" x2="21" y1="-27" y2="-21"/>
      <line stroke-width="2" x1="28" x2="31" y1="-26" y2="-34"/>
      <line stroke-width="2" x1="21" x2="21" y1="-12" y2="-22"/>
      <line stroke-width="2" x1="5" x2="14" y1="-24" y2="-26"/>
      <line stroke-width="2" x1="28" x2="20" y1="-7" y2="-13"/>
      <use height="8" terminal-index="0" width="8" x="18" xlink:href="#terminal" y="-43"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape9_0" viewBox="-1 -81 44 82">
      <line stroke-width="2" x1="27" x2="16" y1="-56" y2="-64"/>
      <line stroke-width="2" x1="16" x2="16" y1="-64" y2="-48"/>
      <line stroke-width="2" x1="27" x2="16" y1="-56" y2="-48"/>
      <ellipse cx="21" cy="-55" fill="none" rx="20" ry="19.5" stroke-width="2"/>
      <line stroke-width="2" x1="14" x2="21" y1="-19" y2="-25"/>
      <line stroke-width="2" x1="21" x2="21" y1="-25" y2="-31"/>
      <line stroke-width="2" x1="29" x2="22" y1="-19" y2="-25"/>
      <ellipse cx="21" cy="-24" fill="none" rx="20" ry="19.5" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="18" xlink:href="#terminal" y="-78"/>
      <use height="8" terminal-index="1" width="8" x="18" xlink:href="#terminal" y="-8.5"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape57_0" viewBox="-1 -44 22 45">
      <line stroke-width="1" x1="10" x2="10" y1="-6" y2="-16"/>
      <line stroke-width="1" x1="10" x2="10" y1="-37" y2="-27"/>
      <line stroke-width="1" x1="11" x2="11" y1="-26" y2="-26"/>
      <polyline fill="none" points="19,-26 10,-14 1,-26 19,-26" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-41"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape52_0" viewBox="-1 -11 24 12">
      <polyline fill="none" points="1,-9 1,-4 21,-4 21,-9" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape51_0" viewBox="0 -57 26 56">
      <line stroke-width="2" x1="13" x2="13" y1="-41" y2="-52"/>
      <line stroke-width="2" x1="13" x2="13" y1="-7" y2="-29"/>
      <line stroke-width="2" x1="24" x2="12" y1="-29" y2="-29"/>
      <polyline fill="none" points="24,-30 24,-28 24,-26 23,-25 22,-23 21,-22 20,-21 19,-20 17,-19 16,-19 14,-19 13,-19 11,-19 9,-19 8,-20 6,-21 5,-22 4,-23 3,-24 2,-26 2,-27 2,-29 2,-30 2,-32 2,-34 3,-35 4,-36 5,-38 6,-39 7,-40 8,-41 10,-41 12,-41" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="10" xlink:href="#terminal" y="-54"/>
      <use height="8" terminal-index="1" width="8" x="10" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape4_0" viewBox="-1 -43 20 44">
      <rect fill="none" height="31" stroke-width="2" width="16" x="1" y="-36"/>
      <line stroke-width="1" x1="9" x2="9" y1="-35" y2="-35"/>
      <line stroke-width="2" x1="9" x2="9" y1="-35" y2="-6"/>
      <use height="8" terminal-index="0" width="8" x="6" xlink:href="#terminal" y="-40"/>
      <use height="8" terminal-index="1" width="8" x="6" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape59_0" viewBox="-1 -39 47 38">
      <ellipse cx="21" cy="-15" fill="none" rx="10" ry="9" stroke-width="1"/>
      <ellipse cx="33" cy="-22" fill="none" rx="10.5" ry="9" stroke-width="1"/>
      <ellipse cx="22" cy="-27" fill="none" rx="10.5" ry="9.5" stroke-width="1"/>
      <ellipse cx="11" cy="-22" fill="none" rx="10" ry="9" stroke-width="1"/>
      <use height="8" terminal-index="0" width="8" x="18" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Arrester:配电柱上避雷器（左向图符）_0_0" viewBox="0 0 28 42">
      <line x1="1" x2="21" y1="1" y2="1"/>
      <line x1="21" x2="21" y1="1" y2="13"/>
      <line x1="21" x2="21" y1="29" y2="35"/>
      <line x1="15" x2="27" y1="35" y2="35"/>
      <line x1="18" x2="24" y1="38" y2="38"/>
      <line x1="19" x2="23" y1="41" y2="41"/>
      <polygon fill="none" points="17,9 25,9 25,29 17,29"/>
      <path d="M20,19 L22,13 L18,13 L20,19"/>
      <use height="8" terminal-index="0" width="8" x="-2.75" xlink:href="#terminal" y="-1.5"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Capacitor:shape0_0" viewBox="-2 -24 35 24">
      <line stroke-width="1" x1="31" x2="0" y1="-18" y2="-18"/>
      <line stroke-width="1" x1="31" x2="0" y1="-2" y2="-2"/>
      <use height="8" terminal-index="0" width="8" x="12" xlink:href="#terminal" y="-21"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape14_0" viewBox="-1 -66 15 66">
      <polyline fill="none" points="9,-9 3,-12 1,-13 1,-14 1,-14 3,-16 6,-17 10,-19 11,-20 11,-20 11,-21 10,-22 6,-23 3,-25 2,-25 2,-26 2,-27 3,-28 6,-29 10,-31 11,-31 11,-32 11,-33 10,-33 6,-35 3,-36 2,-37 2,-38 2,-39 3,-39 6,-41 10,-42 11,-43 11,-44 11,-44 10,-45 6,-47 3,-48 1,-50 1,-50 1,-51 3,-52 9,-55" stroke-width="2"/>
      <line stroke-width="2" x1="9" x2="9" y1="-10" y2="-4"/>
      <line stroke-width="2" x1="9" x2="9" y1="-54" y2="-61"/>
      <use height="8" terminal-index="0" width="8" x="6" xlink:href="#terminal" y="-7.5"/>
      <use height="8" terminal-index="1" width="8" x="6" xlink:href="#terminal" y="-64"/>
    </symbol>
  </defs>
  
  <g id="HeadClass">
    <rect fill="rgb(0,0,0)" height="1987.363" stroke="none" width="3833.27" x="-207" y="-2079.682"/>
  </g>
  <g id="OtherClass">
    <ellipse cx="459.136" cy="-1840.68" fill="rgb(0,255,0)" rx="48.5" ry="26.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <rect fill="rgb(170,255,170)" height="0.1" stroke="rgb(255,0,0)" stroke-width="1" width="0.1" x="24.0181" y="-1188.9"/>
    <circle cx="1311.28" cy="-2057.182" fill="rgb(255,255,0)" r="20.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <circle cx="1272.28" cy="-2058.182" fill="rgb(0,255,0)" r="20.5" stroke="rgb(255,0,0)" stroke-width="1"/>
  </g>
  <g id="MeasurementClass">
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="">
      <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="37" stroke="rgb(255,255,255)" x="-114.017" y="-1769.9">11:21:55</text>
      <metadata><cge:Meas_Ref ObjectID="ME-0" ObjectName="NULL.NULL"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426231" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="767.618" y="-2007.92">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426231" ObjectName="RuiHZ.103BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426232" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="767.618" y="-1980.92">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426232" ObjectName="RuiHZ.103BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426234" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="767.618" y="-1953.92">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426234" ObjectName="RuiHZ.103BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426233" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="767.618" y="-1926.92">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426233" ObjectName="RuiHZ.103BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426243" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1257.74" y="-2001.55">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426243" ObjectName="RuiHZ.104BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426244" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1257.74" y="-1974.55">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426244" ObjectName="RuiHZ.104BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426246" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1257.74" y="-1947.55">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426246" ObjectName="RuiHZ.104BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426245" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1257.74" y="-1920.55">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426245" ObjectName="RuiHZ.104BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426319" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="995.04" y="-1748.84">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426319" ObjectName="RuiHZ.100BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426320" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="995.04" y="-1721.84">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426320" ObjectName="RuiHZ.100BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426322" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="995.04" y="-1694.84">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426322" ObjectName="RuiHZ.100BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426321" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="995.04" y="-1667.84">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426321" ObjectName="RuiHZ.100BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426532" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="429.597" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426532" ObjectName="RuiHZ.993BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426533" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="429.597" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426533" ObjectName="RuiHZ.993BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426535" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="429.597" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426535" ObjectName="RuiHZ.993BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426534" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="429.597" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426534" ObjectName="RuiHZ.993BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426256" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="827.417" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426256" ObjectName="RuiHZ.901BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426258" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="827.417" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426258" ObjectName="RuiHZ.901BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426262" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="827.417" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426262" ObjectName="RuiHZ.901BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426274" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="827.417" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426274" ObjectName="RuiHZ.901BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426340" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="943.983" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426340" ObjectName="RuiHZ.911BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426341" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="943.983" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426341" ObjectName="RuiHZ.911BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426343" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="943.983" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426343" ObjectName="RuiHZ.911BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426342" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="943.983" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426342" ObjectName="RuiHZ.911BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426346" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1045.65" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426346" ObjectName="RuiHZ.912BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426347" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1045.65" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426347" ObjectName="RuiHZ.912BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426349" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1045.65" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426349" ObjectName="RuiHZ.912BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426348" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1045.65" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426348" ObjectName="RuiHZ.912BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426352" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1145.73" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426352" ObjectName="RuiHZ.913BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426353" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1145.73" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426353" ObjectName="RuiHZ.913BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426355" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1145.73" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426355" ObjectName="RuiHZ.913BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426354" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1145.73" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426354" ObjectName="RuiHZ.913BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426358" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1245.78" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426358" ObjectName="RuiHZ.914BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426359" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1245.78" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426359" ObjectName="RuiHZ.914BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426361" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1245.78" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426361" ObjectName="RuiHZ.914BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426360" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1245.78" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426360" ObjectName="RuiHZ.914BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426364" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1340.27" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426364" ObjectName="RuiHZ.915BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426365" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1340.27" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426365" ObjectName="RuiHZ.915BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426367" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1340.27" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426367" ObjectName="RuiHZ.915BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426366" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1340.27" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426366" ObjectName="RuiHZ.915BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426370" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1438.31" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426370" ObjectName="RuiHZ.916BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426371" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1438.31" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426371" ObjectName="RuiHZ.916BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426373" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1438.31" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426373" ObjectName="RuiHZ.916BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426372" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1438.31" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426372" ObjectName="RuiHZ.916BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426376" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1536.5" y="-879.736">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426376" ObjectName="RuiHZ.917BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426377" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1536.5" y="-852.736">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426377" ObjectName="RuiHZ.917BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426379" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1536.5" y="-825.736">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426379" ObjectName="RuiHZ.917BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426378" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1536.5" y="-798.736">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426378" ObjectName="RuiHZ.917BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426520" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1664.21" y="-866.669">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426520" ObjectName="RuiHZ.991BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426521" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1664.21" y="-839.669">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426521" ObjectName="RuiHZ.991BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426523" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1664.21" y="-812.669">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426523" ObjectName="RuiHZ.991BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426522" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1664.21" y="-785.669">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426522" ObjectName="RuiHZ.991BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426382" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1885.21" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426382" ObjectName="RuiHZ.918BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426383" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1885.21" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426383" ObjectName="RuiHZ.918BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426385" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1885.21" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426385" ObjectName="RuiHZ.918BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426384" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1885.21" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426384" ObjectName="RuiHZ.918BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426388" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1981.42" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426388" ObjectName="RuiHZ.919BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426389" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1981.42" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426389" ObjectName="RuiHZ.919BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426391" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1981.42" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426391" ObjectName="RuiHZ.919BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426390" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1981.42" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426390" ObjectName="RuiHZ.919BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426394" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2075.79" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426394" ObjectName="RuiHZ.920BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426395" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2075.79" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426395" ObjectName="RuiHZ.920BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426397" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2075.79" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426397" ObjectName="RuiHZ.920BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426396" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2075.79" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426396" ObjectName="RuiHZ.920BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426400" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2170.16" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426400" ObjectName="RuiHZ.921BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426401" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2170.16" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426401" ObjectName="RuiHZ.921BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426403" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2170.16" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426403" ObjectName="RuiHZ.921BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426402" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2170.16" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426402" ObjectName="RuiHZ.921BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426406" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2270.09" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426406" ObjectName="RuiHZ.922BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426407" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2270.09" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426407" ObjectName="RuiHZ.922BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426409" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2270.09" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426409" ObjectName="RuiHZ.922BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426408" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2270.09" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426408" ObjectName="RuiHZ.922BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426412" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2364.78" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426412" ObjectName="RuiHZ.923BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426413" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2364.78" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426413" ObjectName="RuiHZ.923BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426415" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2364.78" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426415" ObjectName="RuiHZ.923BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426414" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2364.78" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426414" ObjectName="RuiHZ.923BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426418" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2467.77" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426418" ObjectName="RuiHZ.924BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426419" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2467.77" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426419" ObjectName="RuiHZ.924BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426421" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2467.77" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426421" ObjectName="RuiHZ.924BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426420" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2467.77" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426420" ObjectName="RuiHZ.924BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426424" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2570.58" y="-881.884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426424" ObjectName="RuiHZ.925BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426425" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2570.58" y="-854.884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426425" ObjectName="RuiHZ.925BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426427" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2570.58" y="-827.884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426427" ObjectName="RuiHZ.925BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426426" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2570.58" y="-800.884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426426" ObjectName="RuiHZ.925BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426430" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="691.317" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426430" ObjectName="RuiHZ.926BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426431" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="691.317" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426431" ObjectName="RuiHZ.926BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426433" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="691.317" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426433" ObjectName="RuiHZ.926BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426432" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="691.317" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426432" ObjectName="RuiHZ.926BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426436" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="798.466" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426436" ObjectName="RuiHZ.927BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426437" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="798.466" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426437" ObjectName="RuiHZ.927BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426439" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="798.466" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426439" ObjectName="RuiHZ.927BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426438" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="798.466" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426438" ObjectName="RuiHZ.927BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426442" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="899.271" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426442" ObjectName="RuiHZ.928BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426443" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="899.271" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426443" ObjectName="RuiHZ.928BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426445" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="899.271" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426445" ObjectName="RuiHZ.928BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426444" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="899.271" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426444" ObjectName="RuiHZ.928BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426448" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1001.99" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426448" ObjectName="RuiHZ.929BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426449" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1001.99" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426449" ObjectName="RuiHZ.929BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426451" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1001.99" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426451" ObjectName="RuiHZ.929BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426450" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1001.99" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426450" ObjectName="RuiHZ.929BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426454" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1102.84" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426454" ObjectName="RuiHZ.930BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426455" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1102.84" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426455" ObjectName="RuiHZ.930BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426457" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1102.84" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426457" ObjectName="RuiHZ.930BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426456" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1102.84" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426456" ObjectName="RuiHZ.930BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426460" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1195.07" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426460" ObjectName="RuiHZ.931BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426461" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1195.07" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426461" ObjectName="RuiHZ.931BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426463" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1195.07" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426463" ObjectName="RuiHZ.931BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426462" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1195.07" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426462" ObjectName="RuiHZ.931BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426466" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1290.2" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426466" ObjectName="RuiHZ.932BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426467" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1290.2" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426467" ObjectName="RuiHZ.932BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426469" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1290.2" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426469" ObjectName="RuiHZ.932BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426468" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1290.2" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426468" ObjectName="RuiHZ.932BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426526" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1407.73" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426526" ObjectName="RuiHZ.992BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426527" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1407.73" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426527" ObjectName="RuiHZ.992BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426529" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1407.73" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426529" ObjectName="RuiHZ.992BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426528" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1407.73" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426528" ObjectName="RuiHZ.992BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426472" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1519.51" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426472" ObjectName="RuiHZ.933BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426473" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1519.51" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426473" ObjectName="RuiHZ.933BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426475" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1519.51" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426475" ObjectName="RuiHZ.933BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426474" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1519.51" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426474" ObjectName="RuiHZ.933BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426279" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1836.27" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426279" ObjectName="RuiHZ.902BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426282" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1836.27" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426282" ObjectName="RuiHZ.902BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426287" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1836.27" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426287" ObjectName="RuiHZ.902BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426306" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1836.27" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426306" ObjectName="RuiHZ.902BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426280" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2068.07" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426280" ObjectName="RuiHZ.905BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426283" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2068.07" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426283" ObjectName="RuiHZ.905BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426290" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2068.07" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426290" ObjectName="RuiHZ.905BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426307" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2068.07" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426307" ObjectName="RuiHZ.905BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426478" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2209.22" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426478" ObjectName="RuiHZ.934BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426479" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2209.22" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426479" ObjectName="RuiHZ.934BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426481" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2209.22" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426481" ObjectName="RuiHZ.934BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426480" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2209.22" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426480" ObjectName="RuiHZ.934BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426484" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2316.37" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426484" ObjectName="RuiHZ.935BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426485" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2316.37" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426485" ObjectName="RuiHZ.935BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426487" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2316.37" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426487" ObjectName="RuiHZ.935BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426486" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2316.37" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426486" ObjectName="RuiHZ.935BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426490" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2419.32" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426490" ObjectName="RuiHZ.936BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426491" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2419.32" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426491" ObjectName="RuiHZ.936BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426493" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2419.32" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426493" ObjectName="RuiHZ.936BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426492" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2419.32" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426492" ObjectName="RuiHZ.936BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426496" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2525.18" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426496" ObjectName="RuiHZ.937BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426497" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2525.18" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426497" ObjectName="RuiHZ.937BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426499" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2525.18" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426499" ObjectName="RuiHZ.937BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426498" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2525.18" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426498" ObjectName="RuiHZ.937BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426502" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2630.32" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426502" ObjectName="RuiHZ.938BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426503" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2630.32" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426503" ObjectName="RuiHZ.938BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426505" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2630.32" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426505" ObjectName="RuiHZ.938BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426504" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2630.32" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426504" ObjectName="RuiHZ.938BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426508" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2722.55" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426508" ObjectName="RuiHZ.939BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426509" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2722.55" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426509" ObjectName="RuiHZ.939BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426511" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2722.55" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426511" ObjectName="RuiHZ.939BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426510" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2722.55" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426510" ObjectName="RuiHZ.939BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426514" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2823.12" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426514" ObjectName="RuiHZ.940BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426515" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2823.12" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426515" ObjectName="RuiHZ.940BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426517" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2823.12" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426517" ObjectName="RuiHZ.940BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426516" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="2823.12" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426516" ObjectName="RuiHZ.940BK_Cos"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426538" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3207.13" y="-182.319">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426538" ObjectName="RuiHZ.994BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426539" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3207.13" y="-155.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426539" ObjectName="RuiHZ.994BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426541" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3207.13" y="-128.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426541" ObjectName="RuiHZ.994BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426540" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3207.13" y="-101.319">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426540" ObjectName="RuiHZ.994BK_Cos"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426556" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3095.75" y="-182.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426556" ObjectName="RuiHZ.C04BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426557" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3095.75" y="-155.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426557" ObjectName="RuiHZ.C04BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426558" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3095.75" y="-128.319">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426558" ObjectName="RuiHZ.C04BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426559" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3095.75" y="-101.319">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426559" ObjectName="RuiHZ.C04BK_Ic"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426544" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="575.329" y="-880.816">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426544" ObjectName="RuiHZ.C01BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426545" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="575.329" y="-853.816">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426545" ObjectName="RuiHZ.C01BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426546" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="575.329" y="-826.816">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426546" ObjectName="RuiHZ.C01BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426547" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="575.329" y="-799.816">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426547" ObjectName="RuiHZ.C01BK_Ic"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426548" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="732.795" y="-878.712">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426548" ObjectName="RuiHZ.C02BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426549" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="732.795" y="-851.712">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426549" ObjectName="RuiHZ.C02BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426550" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="732.795" y="-824.712">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426550" ObjectName="RuiHZ.C02BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426551" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="732.795" y="-797.712">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426551" ObjectName="RuiHZ.C02BK_Ic"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426552" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="598.395" y="-182.319">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426552" ObjectName="RuiHZ.C03BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426553" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="598.395" y="-155.319">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426553" ObjectName="RuiHZ.C03BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426554" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="598.395" y="-128.319">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426554" ObjectName="RuiHZ.C03BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426555" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="598.395" y="-101.319">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426555" ObjectName="RuiHZ.C03BK_Ic"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426335" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3535.27" y="-701.918">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426335" ObjectName="RuiHZ.10#2B_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426336" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3535.27" y="-674.918">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426336" ObjectName="RuiHZ.10#2B_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426337" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3535.27" y="-647.918">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426337" ObjectName="RuiHZ.10#2B_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426338" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3535.27" y="-620.918">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426338" ObjectName="RuiHZ.10#2B_Uab"/></metadata>
    </g>
    <g MeasureType="Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426339" prefix="Uo ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="3535.27" y="-593.918">Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426339" ObjectName="RuiHZ.10#2B_U0"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426330" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="325.93" y="-642.464">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426330" ObjectName="RuiHZ.10#2A_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426331" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="325.93" y="-615.464">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426331" ObjectName="RuiHZ.10#2A_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426332" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="325.93" y="-588.464">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426332" ObjectName="RuiHZ.10#2A_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426333" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="325.93" y="-561.464">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426333" ObjectName="RuiHZ.10#2A_Uab"/></metadata>
    </g>
    <g MeasureType="Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426334" prefix="Uo ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="325.93" y="-534.464">Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426334" ObjectName="RuiHZ.10#2A_U0"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426325" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="334.71" y="-1347.08">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426325" ObjectName="RuiHZ.10#1_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426326" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="334.71" y="-1320.08">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426326" ObjectName="RuiHZ.10#1_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426327" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="334.71" y="-1293.08">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426327" ObjectName="RuiHZ.10#1_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426328" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="334.71" y="-1266.08">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426328" ObjectName="RuiHZ.10#1_Uab"/></metadata>
    </g>
    <g MeasureType="Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426329" prefix="Uo ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="334.71" y="-1239.08">Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426329" ObjectName="RuiHZ.10#1_U0"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426311" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="407.919" y="-1633.28">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426311" ObjectName="RuiHZ.110#1_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426312" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="407.919" y="-1606.28">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426312" ObjectName="RuiHZ.110#1_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426313" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="407.919" y="-1579.28">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426313" ObjectName="RuiHZ.110#1_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426314" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="407.919" y="-1552.28">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426314" ObjectName="RuiHZ.110#1_Uab"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426315" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1508.43" y="-1739.26">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426315" ObjectName="RuiHZ.110#2_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426316" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1508.43" y="-1712.26">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426316" ObjectName="RuiHZ.110#2_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426317" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1508.43" y="-1685.26">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426317" ObjectName="RuiHZ.110#2_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426318" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1508.43" y="-1658.26">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426318" ObjectName="RuiHZ.110#2_Uab"/></metadata>
    </g>
    <g MeasureType="Tap" PreSymbol="0" align="1" appendix="" decimal="0" id="ME-426275" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="971.147" y="-1402.39">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426275" ObjectName="RuiHZ.1T_Tap"/></metadata>
    </g>
    <g MeasureType="Tmp1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-426276" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="971.147" y="-1375.39">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426276" ObjectName="RuiHZ.1T_Tmp1"/></metadata>
    </g>
    <g MeasureType="Tmp2" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-426277" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="971.147" y="-1348.39">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426277" ObjectName="RuiHZ.1T_Tmp2"/></metadata>
    </g>
    <g MeasureType="WT1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-428926" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="971.147" y="-1321.39">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-428926" ObjectName="RuiHZ.1T_WT1"/></metadata>
    </g>
    <g MeasureType="Tap" PreSymbol="0" align="1" appendix="" decimal="0" id="ME-426308" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1374.15" y="-1434.28">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426308" ObjectName="RuiHZ.2T_Tap"/></metadata>
    </g>
    <g MeasureType="Tmp1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-426309" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1374.15" y="-1407.28">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426309" ObjectName="RuiHZ.2T_Tmp1"/></metadata>
    </g>
    <g MeasureType="Tmp2" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-426310" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1374.15" y="-1380.28">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426310" ObjectName="RuiHZ.2T_Tmp2"/></metadata>
    </g>
    <g MeasureType="WT1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-428927" prefix="">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="21" stroke="rgb(0,255,0)" x="1374.15" y="-1353.28">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-428927" ObjectName="RuiHZ.2T_WT1"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426278" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="1147.25" y="-1564.83">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426278" ObjectName="RuiHZ.1044SW_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426281" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="1147.25" y="-1546.83">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426281" ObjectName="RuiHZ.1044SW_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426284" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="1147.25" y="-1528.83">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426284" ObjectName="RuiHZ.1044SW_Ia"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426296" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="1147.25" y="-1510.83">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426296" ObjectName="RuiHZ.1044SW_Uab"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426255" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="623.646" y="-1500.37">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426255" ObjectName="RuiHZ.1034SW_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426257" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="623.646" y="-1482.37">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426257" ObjectName="RuiHZ.1034SW_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426259" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="623.646" y="-1464.37">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426259" ObjectName="RuiHZ.1034SW_Ia"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-426268" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="15" stroke="rgb(0,255,0)" x="623.646" y="-1446.37">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-426268" ObjectName="RuiHZ.1034SW_Uab"/></metadata>
    </g>
  </g>
  <g id="ConnectiveNodeClass">
    <g>
      <path class="NFkV10" d="M 920,-1245.48 L 920,-1260.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75763@1" ObjectIDND1="75762@0"/></metadata>
    <path d="M 920,-1245.48 L 920,-1260.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 920,-1193 L 920,-1211.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75764@0" ObjectIDND1="75762@1"/></metadata>
    <path d="M 920,-1193 L 920,-1211.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 920,-1276.77 L 920,-1305" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75751@-1" ObjectIDND1="75763@0"/></metadata>
    <path d="M 920,-1276.77 L 920,-1305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 823.279,-1358 L 823.279,-1332 L 869,-1332 L 869,-1141 L 920,-1141 L 920,-1175" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="75764@1" ObjectIDND1="76013@1"/></metadata>
    <path d="M 823.279,-1358 L 823.279,-1332 L 869,-1332 L 869,-1141 L 920,-1141 L 920,-1175" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 823.279,-1370.2 L 823.279,-1358" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="75764@1" ObjectIDND1="76013@1"/></metadata>
    <path d="M 823.279,-1370.2 L 823.279,-1358" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 842,-1358 L 823.279,-1358" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="xcswitch" ObjectIDND0="76013@1" ObjectIDND1="75764@1"/></metadata>
    <path d="M 842,-1358 L 823.279,-1358" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1484.29,-1340.35 L 1483.81,-1383.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="xcswitch" DevType2="xcswitch" ObjectIDND0="76014@1" ObjectIDND1="75767@1" ObjectIDND2="75770@1"/></metadata>
    <path d="M 1484.29,-1340.35 L 1483.81,-1383.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 541.033,-595.622 L 541.033,-761.519 L 2741.72,-760.899 L 2739.41,-1023.95" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75928@1" ObjectIDND1="75930@1"/></metadata>
    <path d="M 541.033,-595.622 L 541.033,-761.519 L 2741.72,-760.899 L 2739.41,-1023.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 479.166,-617.638 L 479.088,-593.966" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75927@1" ObjectIDND1="75926@0"/></metadata>
    <path d="M 479.166,-617.638 L 479.088,-593.966" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 479.088,-563.125 L 479.166,-545.342" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75928@0" ObjectIDND1="75926@1"/></metadata>
    <path d="M 479.088,-563.125 L 479.166,-545.342" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2739.9,-1253.96 L 2739.9,-1176.84" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75930@0" ObjectIDND1="75929@1"/></metadata>
    <path d="M 2739.9,-1253.96 L 2739.9,-1176.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1117.66 L 1712.52,-1104.66" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75957@1" ObjectIDND1="75958@0"/></metadata>
    <path d="M 1712.52,-1117.66 L 1712.52,-1104.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1139.66 L 1712.52,-1117.66" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75957@1" ObjectIDND1="75958@0"/></metadata>
    <path d="M 1712.52,-1139.66 L 1712.52,-1117.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1162.66 L 1712.52,-1139.66" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75957@1" ObjectIDND1="75958@0"/></metadata>
    <path d="M 1712.52,-1162.66 L 1712.52,-1139.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1139.66 L 1730.52,-1139.66" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="75958@0" ObjectIDND1="75957@1"/></metadata>
    <path d="M 1712.52,-1139.66 L 1730.52,-1139.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1198.3 L 1712.52,-1180.66" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75957@0" ObjectIDND1="75955@1"/></metadata>
    <path d="M 1712.52,-1198.3 L 1712.52,-1180.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1245.96 L 1712.52,-1232.14" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75956@1" ObjectIDND1="75955@0"/></metadata>
    <path d="M 1712.52,-1245.96 L 1712.52,-1232.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.52,-1305 L 1712.52,-1262.43" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75956@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1712.52,-1305 L 1712.52,-1262.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1200.71 L 2608.74,-1182.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75843@0" ObjectIDND1="75841@1"/></metadata>
    <path d="M 2608.74,-1200.71 L 2608.74,-1182.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1234.55 L 2608.74,-1249.37" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75842@1" ObjectIDND1="75841@0"/></metadata>
    <path d="M 2608.74,-1234.55 L 2608.74,-1249.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1200.71 L 2506.88,-1181.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75839@0" ObjectIDND1="75837@1"/></metadata>
    <path d="M 2506.88,-1200.71 L 2506.88,-1181.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1248.37 L 2506.88,-1234.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75838@1" ObjectIDND1="75837@0"/></metadata>
    <path d="M 2506.88,-1248.37 L 2506.88,-1234.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1143.07 L 2608.74,-1108.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75995@0" ObjectIDND1="75843@1" ObjectIDND2="75844@0"/></metadata>
    <path d="M 2608.74,-1143.07 L 2608.74,-1108.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1164.07 L 2608.74,-1143.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75843@1" ObjectIDND1="75995@0" ObjectIDND2="75844@0"/></metadata>
    <path d="M 2608.74,-1164.07 L 2608.74,-1143.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1143.07 L 2626.74,-1143.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75844@0" ObjectIDND1="75995@0" ObjectIDND2="75843@1"/></metadata>
    <path d="M 2608.74,-1143.07 L 2626.74,-1143.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2608.74,-1305 L 2608.74,-1265.84" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75842@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2608.74,-1305 L 2608.74,-1265.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1142.07 L 2506.88,-1107.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75994@0" ObjectIDND1="75839@1" ObjectIDND2="75840@0"/></metadata>
    <path d="M 2506.88,-1142.07 L 2506.88,-1107.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1163.07 L 2506.88,-1142.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75839@1" ObjectIDND1="75994@0" ObjectIDND2="75840@0"/></metadata>
    <path d="M 2506.88,-1163.07 L 2506.88,-1142.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1142.07 L 2524.88,-1142.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75840@0" ObjectIDND1="75994@0" ObjectIDND2="75839@1"/></metadata>
    <path d="M 2506.88,-1142.07 L 2524.88,-1142.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2506.88,-1305 L 2506.88,-1264.84" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75838@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2506.88,-1305 L 2506.88,-1264.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1416 L 1171,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75761@0" ObjectIDND1="76014@2"/></metadata>
    <path d="M 1171,-1416 L 1171,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1367 L 1171,-1386" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75761@0" ObjectIDND1="76014@2"/></metadata>
    <path d="M 1171,-1367 L 1171,-1386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1419 L 724,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 724,-1419 L 724,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1373 L 724,-1389" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 724,-1373 L 724,-1389" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1430.83 L 691,-1430.83 L 691,-1414" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 724,-1430.83 L 691,-1430.83 L 691,-1414" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 751,-1430.83 L 724,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 751,-1430.83 L 724,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1154 L 599,-1137.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75935@0" ObjectIDND1="75933@1" ObjectIDND2="75934@0"/></metadata>
    <path d="M 599,-1154 L 599,-1137.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1176 L 599,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="75933@1" ObjectIDND1="75935@0" ObjectIDND2="75934@0"/></metadata>
    <path d="M 599,-1176 L 599,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 617,-1154 L 599,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="75934@0" ObjectIDND1="75935@0" ObjectIDND2="75933@1"/></metadata>
    <path d="M 617,-1154 L 599,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1536 L 821.826,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75754@0" ObjectIDND1="75756@0" ObjectIDND2="75749@-1" ObjectIDND3="75905@1"/></metadata>
    <path d="M 822,-1536 L 821.826,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1522.13 L 822,-1536" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75754@0" ObjectIDND1="75756@0" ObjectIDND2="75749@-1" ObjectIDND3="75905@1"/></metadata>
    <path d="M 822,-1522.13 L 822,-1536" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 857,-1536 L 822,-1536" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75756@0" ObjectIDND1="75754@0" ObjectIDND2="75749@-1" ObjectIDND3="75905@1"/></metadata>
    <path d="M 857,-1536 L 822,-1536" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 694,-1814 L 729,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75774@0" ObjectIDND1="75777@0"/></metadata>
    <path d="M 694,-1814 L 729,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 657,-1768 L 657,-1814 L 694,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75774@0" ObjectIDND1="75777@0"/></metadata>
    <path d="M 657,-1768 L 657,-1814 L 694,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 694,-1770 L 694,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75774@0" ObjectIDND1="75777@0"/></metadata>
    <path d="M 694,-1770 L 694,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 729,-1814 L 746.361,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75774@0" ObjectIDND1="75777@0"/></metadata>
    <path d="M 729,-1814 L 746.361,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 729,-1774 L 729,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75777@0" ObjectIDND1="75774@0"/></metadata>
    <path d="M 729,-1774 L 729,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1222,-1810 L 1239.36,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75781@0" ObjectIDND1="75784@0"/></metadata>
    <path d="M 1222,-1810 L 1239.36,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1810 L 1222,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75781@0" ObjectIDND1="75784@0"/></metadata>
    <path d="M 1187,-1810 L 1222,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1222,-1772 L 1222,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75784@0" ObjectIDND1="75781@0"/></metadata>
    <path d="M 1222,-1772 L 1222,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1672 L 1298,-1687.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" ObjectIDND0="75778@1" ObjectIDND1="75779@0"/></metadata>
    <path d="M 1298,-1672 L 1298,-1687.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1657.13 L 1298,-1672" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" ObjectIDND0="75779@0" ObjectIDND1="75778@1"/></metadata>
    <path d="M 1298,-1657.13 L 1298,-1672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1329,-1672 L 1298,-1672" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" ObjectIDND0="75778@1" ObjectIDND1="75779@0"/></metadata>
    <path d="M 1329,-1672 L 1298,-1672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1738 L 1298,-1752.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="75780@1" ObjectIDND1="75778@0" ObjectIDND2="75783@0"/></metadata>
    <path d="M 1298,-1738 L 1298,-1752.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1721.48 L 1298,-1738" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="75778@0" ObjectIDND1="75780@1" ObjectIDND2="75783@0"/></metadata>
    <path d="M 1298,-1721.48 L 1298,-1738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1329,-1738 L 1298,-1738" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="75783@0" ObjectIDND1="75780@1" ObjectIDND2="75778@0"/></metadata>
    <path d="M 1329,-1738 L 1298,-1738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1276.86,-1810 L 1298,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="75781@1" ObjectIDND1="75780@0" ObjectIDND2="75782@0" ObjectIDND3="76566@0"/></metadata>
    <path d="M 1276.86,-1810 L 1298,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1810 L 1298,-1837" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="75781@1" ObjectIDND1="75780@0" ObjectIDND2="75782@0" ObjectIDND3="76566@0"/></metadata>
    <path d="M 1298,-1810 L 1298,-1837" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1786.13 L 1298,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="75780@0" ObjectIDND1="75781@1" ObjectIDND2="75782@0" ObjectIDND3="76566@0"/></metadata>
    <path d="M 1298,-1786.13 L 1298,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1327,-1810 L 1298,-1810" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="75782@0" ObjectIDND1="75781@1" ObjectIDND2="75780@0" ObjectIDND3="76566@0"/></metadata>
    <path d="M 1327,-1810 L 1298,-1810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 783.861,-1814 L 804,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" ObjectIDND0="75774@1" ObjectIDND1="75773@0" ObjectIDND2="75775@0"/></metadata>
    <path d="M 783.861,-1814 L 804,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1814 L 804,-1832" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" ObjectIDND0="75774@1" ObjectIDND1="75773@0" ObjectIDND2="75775@0"/></metadata>
    <path d="M 804,-1814 L 804,-1832" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1788.13 L 804,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" ObjectIDND0="75773@0" ObjectIDND1="75774@1" ObjectIDND2="75775@0"/></metadata>
    <path d="M 804,-1788.13 L 804,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 834,-1814 L 804,-1814" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" ObjectIDND0="75775@0" ObjectIDND1="75774@1" ObjectIDND2="75773@0"/></metadata>
    <path d="M 834,-1814 L 804,-1814" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1739 L 804,-1754.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="75773@1" ObjectIDND1="75771@0" ObjectIDND2="75776@0"/></metadata>
    <path d="M 804,-1739 L 804,-1754.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1724.48 L 804,-1739" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="75771@0" ObjectIDND1="75773@1" ObjectIDND2="75776@0"/></metadata>
    <path d="M 804,-1724.48 L 804,-1739" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 835,-1739 L 804,-1739" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="75776@0" ObjectIDND1="75773@1" ObjectIDND2="75771@0"/></metadata>
    <path d="M 835,-1739 L 804,-1739" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1673 L 804,-1690.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" ObjectIDND0="75771@1" ObjectIDND1="75772@0"/></metadata>
    <path d="M 804,-1673 L 804,-1690.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1658.13 L 804,-1673" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" ObjectIDND0="75772@0" ObjectIDND1="75771@1"/></metadata>
    <path d="M 804,-1658.13 L 804,-1673" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 835,-1673 L 804,-1673" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" ObjectIDND0="75771@1" ObjectIDND1="75772@0"/></metadata>
    <path d="M 835,-1673 L 804,-1673" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1624.73 L 804,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="75749@-1" ObjectIDND1="75772@1"/></metadata>
    <path d="M 804,-1624.73 L 804,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1071,-1601 L 1082.36,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="75911@0" ObjectIDND1="75909@1" ObjectIDND2="75912@0"/></metadata>
    <path d="M 1071,-1601 L 1082.36,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1057.32,-1601 L 1071,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="75909@1" ObjectIDND1="75911@0" ObjectIDND2="75912@0"/></metadata>
    <path d="M 1057.32,-1601 L 1071,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1071,-1566 L 1071,-1602" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="75912@0" ObjectIDND1="75911@0" ObjectIDND2="75909@1"/></metadata>
    <path d="M 1071,-1566 L 1071,-1602" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1119.86,-1601 L 1148,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="75750@-1" ObjectIDND1="75911@1"/></metadata>
    <path d="M 1119.86,-1601 L 1148,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 959.361,-1601 L 925,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="75749@-1" ObjectIDND1="75910@0"/></metadata>
    <path d="M 959.361,-1601 L 925,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1187.19 L 2291.23,-1168.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75831@0" ObjectIDND1="75829@1"/></metadata>
    <path d="M 2291.23,-1187.19 L 2291.23,-1168.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1221.03 L 2291.23,-1235.85" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75830@1" ObjectIDND1="75829@0"/></metadata>
    <path d="M 2291.23,-1221.03 L 2291.23,-1235.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1187.19 L 2201.23,-1167.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75827@0" ObjectIDND1="75825@1"/></metadata>
    <path d="M 2201.23,-1187.19 L 2201.23,-1167.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1234.85 L 2201.23,-1221.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75826@1" ObjectIDND1="75825@0"/></metadata>
    <path d="M 2201.23,-1234.85 L 2201.23,-1221.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1203.5 L 1579.47,-1181.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75811@0" ObjectIDND1="75809@1"/></metadata>
    <path d="M 1579.47,-1203.5 L 1579.47,-1181.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1249.16 L 1579.47,-1237.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75810@1" ObjectIDND1="75809@0"/></metadata>
    <path d="M 1579.47,-1249.16 L 1579.47,-1237.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1891.87,-558.773 L 1891.87,-539.133" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75767@0" ObjectIDND1="75765@1"/></metadata>
    <path d="M 1891.87,-558.773 L 1891.87,-539.133" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1891.87,-606.43 L 1891.87,-592.613" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75766@1" ObjectIDND1="75765@0"/></metadata>
    <path d="M 1891.87,-606.43 L 1891.87,-592.613" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1891.87,-658 L 1891.87,-622.899" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75766@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1891.87,-658 L 1891.87,-622.899" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-502.577 L 944.704,-467.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75998@0" ObjectIDND1="75855@1" ObjectIDND2="75856@0"/></metadata>
    <path d="M 944.704,-502.577 L 944.704,-467.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-525.577 L 944.704,-502.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75855@1" ObjectIDND1="75998@0" ObjectIDND2="75856@0"/></metadata>
    <path d="M 944.704,-525.577 L 944.704,-502.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-502.577 L 962.704,-502.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75998@0" ObjectIDND1="75855@1" ObjectIDND2="75856@0"/></metadata>
    <path d="M 944.704,-502.577 L 962.704,-502.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-561.217 L 944.704,-543.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75855@0" ObjectIDND1="75853@1"/></metadata>
    <path d="M 944.704,-561.217 L 944.704,-543.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-608.874 L 944.704,-595.057" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75854@1" ObjectIDND1="75853@0"/></metadata>
    <path d="M 944.704,-608.874 L 944.704,-595.057" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 944.704,-658 L 944.704,-625.343" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75854@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 944.704,-658 L 944.704,-625.343" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-503.577 L 852.704,-468.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75997@0" ObjectIDND1="75851@1" ObjectIDND2="75852@0"/></metadata>
    <path d="M 852.704,-503.577 L 852.704,-468.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-526.577 L 852.704,-503.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75851@1" ObjectIDND1="75997@0" ObjectIDND2="75852@0"/></metadata>
    <path d="M 852.704,-526.577 L 852.704,-503.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-503.577 L 870.704,-503.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75852@0" ObjectIDND1="75997@0" ObjectIDND2="75851@1"/></metadata>
    <path d="M 852.704,-503.577 L 870.704,-503.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-562.217 L 852.704,-544.577" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75851@0" ObjectIDND1="75849@1"/></metadata>
    <path d="M 852.704,-562.217 L 852.704,-544.577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-609.874 L 852.704,-596.057" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75850@1" ObjectIDND1="75849@0"/></metadata>
    <path d="M 852.704,-609.874 L 852.704,-596.057" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 852.704,-658 L 852.704,-626.343" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75850@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 852.704,-658 L 852.704,-626.343" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-501.775 L 746.795,-466.775" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75996@0" ObjectIDND1="75847@1" ObjectIDND2="75848@0"/></metadata>
    <path d="M 746.795,-501.775 L 746.795,-466.775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-524.775 L 746.795,-501.775" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75847@1" ObjectIDND1="75996@0" ObjectIDND2="75848@0"/></metadata>
    <path d="M 746.795,-524.775 L 746.795,-501.775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-501.775 L 764.795,-501.775" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75848@0" ObjectIDND1="75996@0" ObjectIDND2="75847@1"/></metadata>
    <path d="M 746.795,-501.775 L 764.795,-501.775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-560.415 L 746.795,-542.775" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75847@0" ObjectIDND1="75845@1"/></metadata>
    <path d="M 746.795,-560.415 L 746.795,-542.775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-608.072 L 746.795,-594.255" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75846@1" ObjectIDND1="75845@0"/></metadata>
    <path d="M 746.795,-608.072 L 746.795,-594.255" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.795,-658 L 746.795,-624.541" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75846@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 746.795,-658 L 746.795,-624.541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.91,-507.089 L 1582.91,-472.089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76003@0" ObjectIDND1="75875@1" ObjectIDND2="75876@0"/></metadata>
    <path d="M 1582.91,-507.089 L 1582.91,-472.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.91,-530.089 L 1582.91,-507.089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75875@1" ObjectIDND1="76003@0" ObjectIDND2="75876@0"/></metadata>
    <path d="M 1582.91,-530.089 L 1582.91,-507.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.91,-507.089 L 1600.91,-507.089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75876@0" ObjectIDND1="76003@0" ObjectIDND2="75875@1"/></metadata>
    <path d="M 1582.91,-507.089 L 1600.91,-507.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.1,-564.921 L 1582.91,-548.089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75875@0" ObjectIDND1="75873@1"/></metadata>
    <path d="M 1582.1,-564.921 L 1582.91,-548.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.91,-613.386 L 1582.1,-598.761" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75874@1" ObjectIDND1="75873@0"/></metadata>
    <path d="M 1582.91,-613.386 L 1582.1,-598.761" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1582.91,-658 L 1582.91,-629.855" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75874@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1582.91,-658 L 1582.91,-629.855" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-501.711 L 1048.47,-466.711" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75999@0" ObjectIDND1="75859@1" ObjectIDND2="75860@0"/></metadata>
    <path d="M 1048.47,-501.711 L 1048.47,-466.711" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-524.711 L 1048.47,-501.711" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75859@1" ObjectIDND1="75999@0" ObjectIDND2="75860@0"/></metadata>
    <path d="M 1048.47,-524.711 L 1048.47,-501.711" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-501.711 L 1066.47,-501.711" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75860@0" ObjectIDND1="75999@0" ObjectIDND2="75859@1"/></metadata>
    <path d="M 1048.47,-501.711 L 1066.47,-501.711" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-560.351 L 1048.47,-542.711" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75859@0" ObjectIDND1="75857@1"/></metadata>
    <path d="M 1048.47,-560.351 L 1048.47,-542.711" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-608.008 L 1048.47,-594.191" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75858@1" ObjectIDND1="75857@0"/></metadata>
    <path d="M 1048.47,-608.008 L 1048.47,-594.191" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1048.47,-658 L 1048.47,-624.477" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75858@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1048.47,-658 L 1048.47,-624.477" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1473 L 1271.01,-1483.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="75758@1" ObjectIDND1="75759@0" ObjectIDND2="76014@0"/></metadata>
    <path d="M 1271.01,-1473 L 1271.01,-1483.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1308,-1473 L 1271.01,-1473" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="75759@0" ObjectIDND1="75758@1" ObjectIDND2="76014@0"/></metadata>
    <path d="M 1308,-1473 L 1271.01,-1473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1473 L 1271.01,-1447.58" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" DevType2="switch" ObjectIDND0="76014@0" ObjectIDND1="75758@1" ObjectIDND2="75759@0"/></metadata>
    <path d="M 1271.01,-1473 L 1271.01,-1447.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1623.73 L 1298,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="75750@-1" ObjectIDND1="75779@1"/></metadata>
    <path d="M 1298,-1623.73 L 1298,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1472 L 856,-1472" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="75755@0" ObjectIDND1="75754@1" ObjectIDND2="76013@0"/></metadata>
    <path d="M 822,-1472 L 856,-1472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1488.73 L 822,-1472" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="75754@1" ObjectIDND1="75755@0" ObjectIDND2="76013@0"/></metadata>
    <path d="M 822,-1488.73 L 822,-1472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1472 L 822,-1448.4" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="75755@0" ObjectIDND1="75754@1" ObjectIDND2="76013@0"/></metadata>
    <path d="M 822,-1472 L 822,-1448.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 484.654,-1103.1 L 484.654,-1124.1" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 484.654,-1103.1 L 484.654,-1124.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1810 L 1187,-1768" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75781@0" ObjectIDND1="75784@0"/></metadata>
    <path d="M 1187,-1810 L 1187,-1768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1810 L 1150,-1810 L 1150,-1766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75781@0" ObjectIDND1="75784@0"/></metadata>
    <path d="M 1187,-1810 L 1150,-1810 L 1150,-1766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1089 L 599.005,-1074.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="capacitor" DevType2="switch" ObjectIDND0="75935@1" ObjectIDND1="76072@0" ObjectIDND2="75936@0"/></metadata>
    <path d="M 599,-1089 L 599.005,-1074.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1103.73 L 599,-1089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 599,-1103.73 L 599,-1089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1089 L 628.701,-1089.02" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 599,-1089 L 628.701,-1089.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1530 L 1271.01,-1517.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75758@0" ObjectIDND1="75760@0" ObjectIDND2="75750@-1" ObjectIDND3="75907@0"/></metadata>
    <path d="M 1271.01,-1530 L 1271.01,-1517.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1270.14,-1601 L 1271.01,-1530" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75758@0" ObjectIDND1="75760@0" ObjectIDND2="75750@-1" ObjectIDND3="75907@0"/></metadata>
    <path d="M 1270.14,-1601 L 1271.01,-1530" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1530 L 1304,-1530" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75760@0" ObjectIDND1="75758@0" ObjectIDND2="75750@-1" ObjectIDND3="75907@0"/></metadata>
    <path d="M 1271.01,-1530 L 1304,-1530" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1175 L 485,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75965@1" ObjectIDND1="75966@0"/></metadata>
    <path d="M 485,-1175 L 485,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1152 L 503,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="75966@0" ObjectIDND1="75965@1"/></metadata>
    <path d="M 485,-1152 L 503,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1210.64 L 485,-1193" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75965@0" ObjectIDND1="75963@1"/></metadata>
    <path d="M 485,-1210.64 L 485,-1193" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1258.3 L 485,-1244.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75964@1" ObjectIDND1="75963@0"/></metadata>
    <path d="M 485,-1258.3 L 485,-1244.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1305 L 485,-1274.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75751@-1" ObjectIDND1="75964@0"/></metadata>
    <path d="M 485,-1305 L 485,-1274.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1211.76 L 599,-1194" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75933@0" ObjectIDND1="75931@1"/></metadata>
    <path d="M 599,-1211.76 L 599,-1194" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1259.3 L 599,-1246.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75932@1" ObjectIDND1="75931@0"/></metadata>
    <path d="M 599,-1259.3 L 599,-1246.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1305 L 599,-1275.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75751@-1" ObjectIDND1="75932@0"/></metadata>
    <path d="M 599,-1305 L 599,-1275.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1142.86 L 1579.47,-1107.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75987@0" ObjectIDND1="75811@1" ObjectIDND2="75812@0"/></metadata>
    <path d="M 1579.47,-1142.86 L 1579.47,-1107.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1163.86 L 1579.47,-1142.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75811@1" ObjectIDND1="75987@0" ObjectIDND2="75812@0"/></metadata>
    <path d="M 1579.47,-1163.86 L 1579.47,-1142.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1142.86 L 1597.47,-1142.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75812@0" ObjectIDND1="75987@0" ObjectIDND2="75811@1"/></metadata>
    <path d="M 1579.47,-1142.86 L 1597.47,-1142.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1579.47,-1305 L 1579.47,-1265.63" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75810@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1579.47,-1305 L 1579.47,-1265.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1144.86 L 1493.47,-1109.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75986@0" ObjectIDND1="75807@1" ObjectIDND2="75808@0"/></metadata>
    <path d="M 1493.47,-1144.86 L 1493.47,-1109.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1167.86 L 1493.47,-1144.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75807@1" ObjectIDND1="75986@0" ObjectIDND2="75808@0"/></metadata>
    <path d="M 1493.47,-1167.86 L 1493.47,-1144.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1144.86 L 1511.47,-1144.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75808@0" ObjectIDND1="75986@0" ObjectIDND2="75807@1"/></metadata>
    <path d="M 1493.47,-1144.86 L 1511.47,-1144.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1203.5 L 1493.47,-1185.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75807@0" ObjectIDND1="75805@1"/></metadata>
    <path d="M 1493.47,-1203.5 L 1493.47,-1185.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1251.16 L 1493.47,-1237.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75806@1" ObjectIDND1="75805@0"/></metadata>
    <path d="M 1493.47,-1251.16 L 1493.47,-1237.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1493.47,-1305 L 1493.47,-1267.63" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75806@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1493.47,-1305 L 1493.47,-1267.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1142.47 L 1379.63,-1107.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75985@0" ObjectIDND1="75803@1" ObjectIDND2="75804@0"/></metadata>
    <path d="M 1379.63,-1142.47 L 1379.63,-1107.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1165.47 L 1379.63,-1142.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75803@1" ObjectIDND1="75985@0" ObjectIDND2="75804@0"/></metadata>
    <path d="M 1379.63,-1165.47 L 1379.63,-1142.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1142.47 L 1397.63,-1142.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75804@0" ObjectIDND1="75985@0" ObjectIDND2="75803@1"/></metadata>
    <path d="M 1379.63,-1142.47 L 1397.63,-1142.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1201.11 L 1379.63,-1183.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75803@0" ObjectIDND1="75801@1"/></metadata>
    <path d="M 1379.63,-1201.11 L 1379.63,-1183.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1248.77 L 1379.63,-1234.95" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75802@1" ObjectIDND1="75801@0"/></metadata>
    <path d="M 1379.63,-1248.77 L 1379.63,-1234.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1379.63,-1305 L 1379.63,-1265.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75802@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1379.63,-1305 L 1379.63,-1265.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1143.47 L 1290.63,-1108.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75984@0" ObjectIDND1="75799@1" ObjectIDND2="75800@0"/></metadata>
    <path d="M 1290.63,-1143.47 L 1290.63,-1108.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1166.47 L 1290.63,-1143.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75799@1" ObjectIDND1="75984@0" ObjectIDND2="75800@0"/></metadata>
    <path d="M 1290.63,-1166.47 L 1290.63,-1143.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1143.47 L 1308.63,-1143.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75800@0" ObjectIDND1="75984@0" ObjectIDND2="75799@1"/></metadata>
    <path d="M 1290.63,-1143.47 L 1308.63,-1143.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1202.11 L 1290.63,-1184.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75799@0" ObjectIDND1="75797@1"/></metadata>
    <path d="M 1290.63,-1202.11 L 1290.63,-1184.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1249.77 L 1290.63,-1235.95" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75798@1" ObjectIDND1="75797@0"/></metadata>
    <path d="M 1290.63,-1249.77 L 1290.63,-1235.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1290.63,-1305 L 1290.63,-1266.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75798@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1290.63,-1305 L 1290.63,-1266.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1142.47 L 1204.63,-1107.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75983@0" ObjectIDND1="75795@1" ObjectIDND2="75796@0"/></metadata>
    <path d="M 1204.63,-1142.47 L 1204.63,-1107.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1165.47 L 1204.63,-1142.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75795@1" ObjectIDND1="75983@0" ObjectIDND2="75796@0"/></metadata>
    <path d="M 1204.63,-1165.47 L 1204.63,-1142.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1142.47 L 1222.63,-1142.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75796@0" ObjectIDND1="75983@0" ObjectIDND2="75795@1"/></metadata>
    <path d="M 1204.63,-1142.47 L 1222.63,-1142.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1201.11 L 1204.63,-1183.47" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75795@0" ObjectIDND1="75793@1"/></metadata>
    <path d="M 1204.63,-1201.11 L 1204.63,-1183.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1248.77 L 1204.63,-1234.95" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75794@1" ObjectIDND1="75793@0"/></metadata>
    <path d="M 1204.63,-1248.77 L 1204.63,-1234.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1204.63,-1305 L 1204.63,-1265.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75794@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1204.63,-1305 L 1204.63,-1265.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1140.34 L 1093.59,-1105.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75982@0" ObjectIDND1="75791@1" ObjectIDND2="75792@0"/></metadata>
    <path d="M 1093.59,-1140.34 L 1093.59,-1105.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1163.34 L 1093.59,-1140.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75791@1" ObjectIDND1="75982@0" ObjectIDND2="75792@0"/></metadata>
    <path d="M 1093.59,-1163.34 L 1093.59,-1140.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1140.34 L 1111.59,-1140.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75792@0" ObjectIDND1="75982@0" ObjectIDND2="75791@1"/></metadata>
    <path d="M 1093.59,-1140.34 L 1111.59,-1140.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1198.98 L 1093.59,-1181.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75791@0" ObjectIDND1="75789@1"/></metadata>
    <path d="M 1093.59,-1198.98 L 1093.59,-1181.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1246.64 L 1093.59,-1232.82" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75790@1" ObjectIDND1="75789@0"/></metadata>
    <path d="M 1093.59,-1246.64 L 1093.59,-1232.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1093.59,-1305 L 1093.59,-1263.11" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75790@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1093.59,-1305 L 1093.59,-1263.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1140.34 L 1009.59,-1105.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75981@0" ObjectIDND1="75787@1" ObjectIDND2="75788@0"/></metadata>
    <path d="M 1009.59,-1140.34 L 1009.59,-1105.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1163.34 L 1009.59,-1140.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75787@1" ObjectIDND1="75981@0" ObjectIDND2="75788@0"/></metadata>
    <path d="M 1009.59,-1163.34 L 1009.59,-1140.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1140.34 L 1027.59,-1140.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75788@0" ObjectIDND1="75981@0" ObjectIDND2="75787@1"/></metadata>
    <path d="M 1009.59,-1140.34 L 1027.59,-1140.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1198.98 L 1009.59,-1181.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75787@0" ObjectIDND1="75785@1"/></metadata>
    <path d="M 1009.59,-1198.98 L 1009.59,-1181.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1246.64 L 1009.59,-1232.82" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75786@1" ObjectIDND1="75785@0"/></metadata>
    <path d="M 1009.59,-1246.64 L 1009.59,-1232.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1009.59,-1305 L 1009.59,-1263.11" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75786@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1009.59,-1305 L 1009.59,-1263.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1430.71 L 1141,-1430.71 L 1141,-1413" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75761@0" ObjectIDND1="76014@2"/></metadata>
    <path d="M 1171,-1430.71 L 1141,-1430.71 L 1141,-1413" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1201,-1430.71 L 1171,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75761@0" ObjectIDND1="76014@2"/></metadata>
    <path d="M 1201,-1430.71 L 1171,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2401.55,-1134.55 L 2401.55,-1099.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75993@0" ObjectIDND1="75835@1" ObjectIDND2="75836@0"/></metadata>
    <path d="M 2401.55,-1134.55 L 2401.55,-1099.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2401.55,-1157.55 L 2401.55,-1134.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75835@1" ObjectIDND1="75993@0" ObjectIDND2="75836@0"/></metadata>
    <path d="M 2401.55,-1157.55 L 2401.55,-1134.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2401.55,-1134.55 L 2418.1,-1134.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75836@0" ObjectIDND1="75993@0" ObjectIDND2="75835@1"/></metadata>
    <path d="M 2401.55,-1134.55 L 2418.1,-1134.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2401.55,-1197.19 L 2401.55,-1175.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75835@0" ObjectIDND1="75833@1"/></metadata>
    <path d="M 2401.55,-1197.19 L 2401.55,-1175.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2400.1,-1240.85 L 2401.55,-1231.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75834@1" ObjectIDND1="75833@0"/></metadata>
    <path d="M 2400.1,-1240.85 L 2401.55,-1231.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2400.1,-1305 L 2400.1,-1257.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75834@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2400.1,-1305 L 2400.1,-1257.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1129.55 L 2291.23,-1094.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75992@0" ObjectIDND1="75831@1" ObjectIDND2="75832@0"/></metadata>
    <path d="M 2291.23,-1129.55 L 2291.23,-1094.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1150.55 L 2291.23,-1129.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75831@1" ObjectIDND1="75992@0" ObjectIDND2="75832@0"/></metadata>
    <path d="M 2291.23,-1150.55 L 2291.23,-1129.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1129.55 L 2309.23,-1129.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75832@0" ObjectIDND1="75992@0" ObjectIDND2="75831@1"/></metadata>
    <path d="M 2291.23,-1129.55 L 2309.23,-1129.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2291.23,-1305 L 2291.23,-1252.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75830@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2291.23,-1305 L 2291.23,-1252.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1128.55 L 2201.23,-1093.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75991@0" ObjectIDND1="75827@1" ObjectIDND2="75828@0"/></metadata>
    <path d="M 2201.23,-1128.55 L 2201.23,-1093.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1149.55 L 2201.23,-1128.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75827@1" ObjectIDND1="75991@0" ObjectIDND2="75828@0"/></metadata>
    <path d="M 2201.23,-1149.55 L 2201.23,-1128.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1128.55 L 2219.23,-1128.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75828@0" ObjectIDND1="75991@0" ObjectIDND2="75827@1"/></metadata>
    <path d="M 2201.23,-1128.55 L 2219.23,-1128.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2201.23,-1305 L 2201.23,-1251.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75826@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2201.23,-1305 L 2201.23,-1251.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1131.55 L 2117.23,-1096.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="switch" DevType2="xcswitch" ObjectIDND0="75990@0" ObjectIDND1="75824@0" ObjectIDND2="75823@1"/></metadata>
    <path d="M 2117.23,-1131.55 L 2117.23,-1096.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1190.19 L 2117.23,-1172.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="75821@1" ObjectIDND1="75823@0"/></metadata>
    <path d="M 2117.23,-1190.19 L 2117.23,-1172.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1154.55 L 2117.23,-1131.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="switch" DevType2="xcswitch" ObjectIDND0="75990@0" ObjectIDND1="75824@0" ObjectIDND2="75823@1"/></metadata>
    <path d="M 2117.23,-1154.55 L 2117.23,-1131.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1131.55 L 2135.23,-1131.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75824@0" ObjectIDND1="75990@0" ObjectIDND2="75823@1"/></metadata>
    <path d="M 2117.23,-1131.55 L 2135.23,-1131.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1237.85 L 2117.23,-1224.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75822@1" ObjectIDND1="75821@0"/></metadata>
    <path d="M 2117.23,-1237.85 L 2117.23,-1224.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.23,-1305 L 2117.23,-1254.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75822@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2117.23,-1305 L 2117.23,-1254.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1131.55 L 2031.23,-1096.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75989@0" ObjectIDND1="75819@1" ObjectIDND2="75820@0"/></metadata>
    <path d="M 2031.23,-1131.55 L 2031.23,-1096.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1154.55 L 2031.23,-1131.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75819@1" ObjectIDND1="75989@0" ObjectIDND2="75820@0"/></metadata>
    <path d="M 2031.23,-1154.55 L 2031.23,-1131.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1131.55 L 2049.23,-1131.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75820@0" ObjectIDND1="75989@0" ObjectIDND2="75819@1"/></metadata>
    <path d="M 2031.23,-1131.55 L 2049.23,-1131.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1190.19 L 2031.23,-1172.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75819@0" ObjectIDND1="75817@1"/></metadata>
    <path d="M 2031.23,-1190.19 L 2031.23,-1172.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1237.85 L 2031.23,-1224.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75818@1" ObjectIDND1="75817@0"/></metadata>
    <path d="M 2031.23,-1237.85 L 2031.23,-1224.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2031.23,-1305 L 2031.23,-1254.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75818@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2031.23,-1305 L 2031.23,-1254.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1130.55 L 1941.23,-1095.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75988@0" ObjectIDND1="75815@1" ObjectIDND2="75816@0"/></metadata>
    <path d="M 1941.23,-1130.55 L 1941.23,-1095.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1153.55 L 1941.23,-1130.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="75815@1" ObjectIDND1="75988@0" ObjectIDND2="75816@0"/></metadata>
    <path d="M 1941.23,-1153.55 L 1941.23,-1130.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1130.55 L 1959.23,-1130.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="75816@0" ObjectIDND1="75988@0" ObjectIDND2="75815@1"/></metadata>
    <path d="M 1941.23,-1130.55 L 1959.23,-1130.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1189.19 L 1941.23,-1171.55" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75815@0" ObjectIDND1="75813@1"/></metadata>
    <path d="M 1941.23,-1189.19 L 1941.23,-1171.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1236.85 L 1941.23,-1223.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75814@1" ObjectIDND1="75813@0"/></metadata>
    <path d="M 1941.23,-1236.85 L 1941.23,-1223.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1941.23,-1305 L 1941.23,-1253.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75814@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1941.23,-1305 L 1941.23,-1253.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1012,-1601 L 1022.76,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="75909@0" ObjectIDND1="75910@1" ObjectIDND2="75913@0"/></metadata>
    <path d="M 1012,-1601 L 1022.76,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 996.861,-1601 L 1012,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="75910@1" ObjectIDND1="75909@0" ObjectIDND2="75913@0"/></metadata>
    <path d="M 996.861,-1601 L 1012,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1012,-1602 L 1012,-1567" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="75913@0" ObjectIDND1="75909@0" ObjectIDND2="75910@1"/></metadata>
    <path d="M 1012,-1602 L 1012,-1567" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1430.71 L 1201,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" ObjectIDND0="76014@2" ObjectIDND1="75761@0"/></metadata>
    <path d="M 1271.01,-1430.71 L 1201,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1201,-1430.71 L 1201,-1411" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75761@0" ObjectIDND1="76014@2"/></metadata>
    <path d="M 1201,-1430.71 L 1201,-1411" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1430.83 L 751,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 822,-1430.83 L 751,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 751,-1430.83 L 751,-1410" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="75757@0" ObjectIDND1="76013@2"/></metadata>
    <path d="M 751,-1430.83 L 751,-1410" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 479.166,-634.107 L 479.166,-658" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75927@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 479.166,-634.107 L 479.166,-658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 541.033,-595.622 L 541.033,-503.42 L 479.166,-503.42 L 479.166,-527.342" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75930@1" ObjectIDND1="75928@1"/></metadata>
    <path d="M 541.033,-595.622 L 541.033,-503.42 L 479.166,-503.42 L 479.166,-527.342" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2740.49,-1268.61 L 2740.49,-1305" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75929@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 2740.49,-1268.61 L 2740.49,-1305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2739.41,-1023.95 L 2739.9,-1158.84" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75930@1" ObjectIDND1="75928@1"/></metadata>
    <path d="M 2739.41,-1023.95 L 2739.9,-1158.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1601.17,-1601.26 L 1568,-1601" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" ObjectIDND0="75750@1"/></metadata>
    <path d="M 1601.17,-1601.26 L 1568,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 1653.8,-1601.15 L 1653.8,-1567.26" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 1653.8,-1601.15 L 1653.8,-1567.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 1653.8,-1601.15 L 1638.67,-1601.26" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 1653.8,-1601.15 L 1638.67,-1601.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 700.514,-1527.56 L 700.514,-1573.56 L 738.263,-1573.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75906@0" ObjectIDND1="75905@0"/></metadata>
    <path d="M 700.514,-1527.56 L 700.514,-1573.56 L 738.263,-1573.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 738.263,-1573.24 L 757.156,-1573.81" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75906@0" ObjectIDND1="75905@0"/></metadata>
    <path d="M 738.263,-1573.24 L 757.156,-1573.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 794.648,-1574.4 L 821.897,-1574.58" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75754@0" ObjectIDND1="75756@0" ObjectIDND2="75749@-1" ObjectIDND3="75905@1"/></metadata>
    <path d="M 794.648,-1574.4 L 821.897,-1574.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 738.38,-1533.99 L 738.263,-1573.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75906@0" ObjectIDND1="75905@0"/></metadata>
    <path d="M 738.38,-1533.99 L 738.263,-1573.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1427.17,-1555.89 L 1427.05,-1571.51 L 1389.3,-1571.19" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75907@1" ObjectIDND1="75908@0"/></metadata>
    <path d="M 1427.17,-1555.89 L 1427.05,-1571.51 L 1389.3,-1571.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1270.5,-1571.11 L 1306.81,-1570.82" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" DevType3="switch" ObjectIDND0="75758@0" ObjectIDND1="75760@0" ObjectIDND2="75750@-1" ObjectIDND3="75907@0"/></metadata>
    <path d="M 1270.5,-1571.11 L 1306.81,-1570.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1344.31,-1570.82 L 1389.3,-1571.19" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75907@1" ObjectIDND1="75908@0"/></metadata>
    <path d="M 1344.31,-1570.82 L 1389.3,-1571.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1390.65,-1550.36 L 1389.3,-1571.19" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="75907@1" ObjectIDND1="75908@0"/></metadata>
    <path d="M 1390.65,-1550.36 L 1389.3,-1571.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1150.28 L 767.783,-1133.41" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75941@0" ObjectIDND1="75939@1" ObjectIDND2="75940@0"/></metadata>
    <path d="M 767.783,-1150.28 L 767.783,-1133.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1172.28 L 767.783,-1150.28" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75941@0" ObjectIDND1="75939@1" ObjectIDND2="75940@0"/></metadata>
    <path d="M 767.783,-1172.28 L 767.783,-1150.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 785.783,-1150.28 L 767.783,-1150.28" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75941@0" ObjectIDND1="75939@1" ObjectIDND2="75940@0"/></metadata>
    <path d="M 785.783,-1150.28 L 767.783,-1150.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1085.28 L 769.036,-1069.16" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76073@0" ObjectIDND1="75941@1" ObjectIDND2="75942@0"/></metadata>
    <path d="M 767.783,-1085.28 L 769.036,-1069.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1100.01 L 767.783,-1085.28" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 767.783,-1100.01 L 767.783,-1085.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1085.28 L 797.485,-1085.29" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 767.783,-1085.28 L 797.485,-1085.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.784,-1208.04 L 767.783,-1190.28" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75939@0" ObjectIDND1="75937@1"/></metadata>
    <path d="M 767.784,-1208.04 L 767.783,-1190.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1255.57 L 767.784,-1242.6" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75938@1" ObjectIDND1="75937@0"/></metadata>
    <path d="M 767.783,-1255.57 L 767.784,-1242.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 767.783,-1305 L 767.783,-1272.04" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75938@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 767.783,-1305 L 767.783,-1272.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 1671.56,-1072.9 L 1711.44,-1089.9" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 1671.56,-1072.9 L 1711.44,-1089.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712.23,-1042.37 L 1712.52,-1061.46" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75957@1" ObjectIDND1="75958@0"/></metadata>
    <path d="M 1712.23,-1042.37 L 1712.52,-1061.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1711.93,-999.03 L 1712.23,-1018.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75957@1" ObjectIDND1="75958@0"/></metadata>
    <path d="M 1711.93,-999.03 L 1712.23,-1018.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-498.98 L 622.169,-482.111" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75947@0" ObjectIDND1="75945@1" ObjectIDND2="75946@0"/></metadata>
    <path d="M 622.169,-498.98 L 622.169,-482.111" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-520.98 L 622.169,-498.98" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75947@0" ObjectIDND1="75945@1" ObjectIDND2="75946@0"/></metadata>
    <path d="M 622.169,-520.98 L 622.169,-498.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 640.169,-498.98 L 622.169,-498.98" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75947@0" ObjectIDND1="75945@1" ObjectIDND2="75946@0"/></metadata>
    <path d="M 640.169,-498.98 L 622.169,-498.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-433.98 L 622.169,-418.98" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.169,-433.98 L 622.169,-418.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-448.712 L 622.169,-433.98" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.169,-448.712 L 622.169,-433.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-433.98 L 651.87,-433.997" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.169,-433.98 L 651.87,-433.997" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-556.74 L 622.169,-538.98" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75945@0" ObjectIDND1="75943@1"/></metadata>
    <path d="M 622.169,-556.74 L 622.169,-538.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-604.276 L 622.169,-591.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75944@1" ObjectIDND1="75943@0"/></metadata>
    <path d="M 622.169,-604.276 L 622.169,-591.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-658 L 622.169,-620.745" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75944@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 622.169,-658 L 622.169,-620.745" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.169,-374.98 L 622.169,-363.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75947@1" ObjectIDND1="75948@0" ObjectIDND2="76074@0"/></metadata>
    <path d="M 622.169,-374.98 L 622.169,-363.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.029,-333.934 L 621.885,-319.995" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.029,-333.934 L 621.885,-319.995" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 589.975,-366.065 L 622.169,-366.065" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75947@1" ObjectIDND1="75948@0" ObjectIDND2="76074@0"/></metadata>
    <path d="M 589.975,-366.065 L 622.169,-366.065" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599.716,-350.949 L 622.029,-350.759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 599.716,-350.949 L 622.029,-350.759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.122,-310.712 L 622.492,-295.993 L 640.725,-295.993 L 641.387,-423.884 L 641.174,-433.991" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.122,-310.712 L 622.492,-295.993 L 640.725,-295.993 L 641.387,-423.884 L 641.174,-433.991" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-498.374 L 1240.52,-463.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76001@0" ObjectIDND1="75867@1" ObjectIDND2="75868@0"/></metadata>
    <path d="M 1240.52,-498.374 L 1240.52,-463.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-521.374 L 1240.52,-498.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76001@0" ObjectIDND1="75867@1" ObjectIDND2="75868@0"/></metadata>
    <path d="M 1240.52,-521.374 L 1240.52,-498.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-498.374 L 1258.52,-498.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76001@0" ObjectIDND1="75867@1" ObjectIDND2="75868@0"/></metadata>
    <path d="M 1240.52,-498.374 L 1258.52,-498.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-557.014 L 1240.52,-539.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75867@0" ObjectIDND1="75865@1"/></metadata>
    <path d="M 1240.52,-557.014 L 1240.52,-539.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-604.671 L 1240.52,-590.854" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75866@1" ObjectIDND1="75865@0"/></metadata>
    <path d="M 1240.52,-604.671 L 1240.52,-590.854" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1240.52,-658 L 1240.52,-621.14" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75866@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1240.52,-658 L 1240.52,-621.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-499.374 L 1148.52,-464.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76000@0" ObjectIDND1="75863@1" ObjectIDND2="75864@0"/></metadata>
    <path d="M 1148.52,-499.374 L 1148.52,-464.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-522.374 L 1148.52,-499.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76000@0" ObjectIDND1="75863@1" ObjectIDND2="75864@0"/></metadata>
    <path d="M 1148.52,-522.374 L 1148.52,-499.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-499.374 L 1166.52,-499.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76000@0" ObjectIDND1="75863@1" ObjectIDND2="75864@0"/></metadata>
    <path d="M 1148.52,-499.374 L 1166.52,-499.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-558.014 L 1148.52,-540.374" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75863@0" ObjectIDND1="75861@1"/></metadata>
    <path d="M 1148.52,-558.014 L 1148.52,-540.374" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-605.671 L 1148.52,-591.854" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75862@1" ObjectIDND1="75861@0"/></metadata>
    <path d="M 1148.52,-605.671 L 1148.52,-591.854" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1148.52,-658 L 1148.52,-622.14" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75862@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1148.52,-658 L 1148.52,-622.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-497.508 L 1344.28,-462.508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76002@0" ObjectIDND1="75871@1" ObjectIDND2="75872@0"/></metadata>
    <path d="M 1344.28,-497.508 L 1344.28,-462.508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-520.508 L 1344.28,-497.508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76002@0" ObjectIDND1="75871@1" ObjectIDND2="75872@0"/></metadata>
    <path d="M 1344.28,-520.508 L 1344.28,-497.508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-497.508 L 1362.28,-497.508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76002@0" ObjectIDND1="75871@1" ObjectIDND2="75872@0"/></metadata>
    <path d="M 1344.28,-497.508 L 1362.28,-497.508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-556.148 L 1344.28,-538.508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75871@0" ObjectIDND1="75869@1"/></metadata>
    <path d="M 1344.28,-556.148 L 1344.28,-538.508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-603.805 L 1344.28,-589.988" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75870@1" ObjectIDND1="75869@0"/></metadata>
    <path d="M 1344.28,-603.805 L 1344.28,-589.988" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1344.28,-658 L 1344.28,-620.274" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75870@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1344.28,-658 L 1344.28,-620.274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-469.419 L 1467.85,-456.419" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.85,-469.419 L 1467.85,-456.419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-491.419 L 1467.85,-469.419" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.85,-491.419 L 1467.85,-469.419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-514.419 L 1467.85,-491.419" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.85,-514.419 L 1467.85,-491.419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-491.419 L 1485.85,-491.419" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.85,-491.419 L 1485.85,-491.419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.84,-550.059 L 1467.85,-532.419" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75961@0" ObjectIDND1="75959@1"/></metadata>
    <path d="M 1467.84,-550.059 L 1467.85,-532.419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-597.716 L 1467.84,-583.899" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75960@1" ObjectIDND1="75959@0"/></metadata>
    <path d="M 1467.85,-597.716 L 1467.84,-583.899" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.85,-658 L 1467.85,-614.185" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75960@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1467.85,-658 L 1467.85,-614.185" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 1426.88,-424.652 L 1466.76,-441.659" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 1426.88,-424.652 L 1466.76,-441.659" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.55,-394.124 L 1467.85,-413.219" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.55,-394.124 L 1467.85,-413.219" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1467.26,-350.787 L 1467.55,-369.882" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75961@1" ObjectIDND1="75962@0"/></metadata>
    <path d="M 1467.26,-350.787 L 1467.55,-369.882" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1710.43,-507.74 L 1710.56,-486.595" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75919@1"/></metadata>
    <path d="M 1710.43,-507.74 L 1710.56,-486.595" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1710.56,-454.595 L 1710.7,-433.03" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75919@1"/></metadata>
    <path d="M 1710.56,-454.595 L 1710.7,-433.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1710.43,-596.037 L 1710.43,-525.74" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75919@0" ObjectIDND1="75918@1"/></metadata>
    <path d="M 1710.43,-596.037 L 1710.43,-525.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1710.43,-658 L 1710.43,-612.506" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75918@0" ObjectIDND1="75752@-1"/></metadata>
    <path d="M 1710.43,-658 L 1710.43,-612.506" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1710.5,-497.202 L 1741.83,-497.202" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75919@1"/></metadata>
    <path d="M 1710.5,-497.202 L 1741.83,-497.202" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1272.24,-1372.47 L 1272.24,-1340.35 L 2841.98,-1340.35 L 2841.98,-727.478 L 1829.46,-730.298 L 1827.81,-553.706 L 1826.98,-500.069 L 1892.17,-499.243 L 1891.87,-521.133" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="xcswitch" DevType2="xcswitch" ObjectIDND0="76014@1" ObjectIDND1="75767@1" ObjectIDND2="75770@1"/></metadata>
    <path d="M 1272.24,-1372.47 L 1272.24,-1340.35 L 2841.98,-1340.35 L 2841.98,-727.478 L 1829.46,-730.298 L 1827.81,-553.706 L 1826.98,-500.069 L 1892.17,-499.243 L 1891.87,-521.133" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2130.25,-617.793 L 2130.25,-597.619" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="75768@0" ObjectIDND1="75769@1"/></metadata>
    <path d="M 2130.25,-617.793 L 2130.25,-597.619" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2130.25,-563.779 L 2130.25,-545.496" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="75768@1" ObjectIDND1="75770@0"/></metadata>
    <path d="M 2130.25,-563.779 L 2130.25,-545.496" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-498.063 L 2469.75,-463.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76006@0" ObjectIDND1="75887@1" ObjectIDND2="75888@0"/></metadata>
    <path d="M 2469.75,-498.063 L 2469.75,-463.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-521.063 L 2469.75,-498.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76006@0" ObjectIDND1="75887@1" ObjectIDND2="75888@0"/></metadata>
    <path d="M 2469.75,-521.063 L 2469.75,-498.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-498.063 L 2487.75,-498.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76006@0" ObjectIDND1="75887@1" ObjectIDND2="75888@0"/></metadata>
    <path d="M 2469.75,-498.063 L 2487.75,-498.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-556.703 L 2469.75,-539.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75887@0" ObjectIDND1="75885@1"/></metadata>
    <path d="M 2469.75,-556.703 L 2469.75,-539.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-604.359 L 2469.75,-590.543" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75886@1" ObjectIDND1="75885@0"/></metadata>
    <path d="M 2469.75,-604.359 L 2469.75,-590.543" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2469.75,-658.154 L 2469.75,-620.828" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75886@0"/></metadata>
    <path d="M 2469.75,-658.154 L 2469.75,-620.828" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-499.063 L 2377.75,-464.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76005@0" ObjectIDND1="75883@1" ObjectIDND2="75884@0"/></metadata>
    <path d="M 2377.75,-499.063 L 2377.75,-464.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-522.063 L 2377.75,-499.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76005@0" ObjectIDND1="75883@1" ObjectIDND2="75884@0"/></metadata>
    <path d="M 2377.75,-522.063 L 2377.75,-499.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-499.063 L 2395.75,-499.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76005@0" ObjectIDND1="75883@1" ObjectIDND2="75884@0"/></metadata>
    <path d="M 2377.75,-499.063 L 2395.75,-499.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-557.703 L 2377.75,-540.063" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75883@0" ObjectIDND1="75881@1"/></metadata>
    <path d="M 2377.75,-557.703 L 2377.75,-540.063" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-605.359 L 2377.75,-591.543" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75882@1" ObjectIDND1="75881@0"/></metadata>
    <path d="M 2377.75,-605.359 L 2377.75,-591.543" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2377.75,-658.154 L 2377.75,-621.828" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75882@0"/></metadata>
    <path d="M 2377.75,-658.154 L 2377.75,-621.828" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-497.261 L 2271.84,-462.261" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76004@0" ObjectIDND1="75879@1" ObjectIDND2="75880@0"/></metadata>
    <path d="M 2271.84,-497.261 L 2271.84,-462.261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-520.261 L 2271.84,-497.261" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76004@0" ObjectIDND1="75879@1" ObjectIDND2="75880@0"/></metadata>
    <path d="M 2271.84,-520.261 L 2271.84,-497.261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-497.261 L 2289.84,-497.261" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76004@0" ObjectIDND1="75879@1" ObjectIDND2="75880@0"/></metadata>
    <path d="M 2271.84,-497.261 L 2289.84,-497.261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-555.901 L 2271.84,-538.261" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75879@0" ObjectIDND1="75877@1"/></metadata>
    <path d="M 2271.84,-555.901 L 2271.84,-538.261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-603.558 L 2271.84,-589.741" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75878@1" ObjectIDND1="75877@0"/></metadata>
    <path d="M 2271.84,-603.558 L 2271.84,-589.741" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2271.84,-658.154 L 2271.84,-620.027" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75878@0"/></metadata>
    <path d="M 2271.84,-658.154 L 2271.84,-620.027" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-497.197 L 2573.51,-462.197" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76007@0" ObjectIDND1="75891@1" ObjectIDND2="75892@0"/></metadata>
    <path d="M 2573.51,-497.197 L 2573.51,-462.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-520.197 L 2573.51,-497.197" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76007@0" ObjectIDND1="75891@1" ObjectIDND2="75892@0"/></metadata>
    <path d="M 2573.51,-520.197 L 2573.51,-497.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-497.197 L 2591.51,-497.197" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76007@0" ObjectIDND1="75891@1" ObjectIDND2="75892@0"/></metadata>
    <path d="M 2573.51,-497.197 L 2591.51,-497.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-555.837 L 2573.51,-538.197" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75891@0" ObjectIDND1="75889@1"/></metadata>
    <path d="M 2573.51,-555.837 L 2573.51,-538.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-603.494 L 2573.51,-589.677" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75890@1" ObjectIDND1="75889@0"/></metadata>
    <path d="M 2573.51,-603.494 L 2573.51,-589.677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2573.51,-658.154 L 2573.51,-619.962" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75890@0"/></metadata>
    <path d="M 2573.51,-658.154 L 2573.51,-619.962" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2130.25,-634.262 L 2130.25,-658.154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75769@0" ObjectIDND1="75753@-1"/></metadata>
    <path d="M 2130.25,-634.262 L 2130.25,-658.154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2192.67,-729.287 L 2192.11,-503.574 L 2130.25,-503.574 L 2130.25,-527.496" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" DevType2="xcswitch" ObjectIDND0="75770@1" ObjectIDND1="76014@1" ObjectIDND2="75767@1"/></metadata>
    <path d="M 2192.67,-729.287 L 2192.11,-503.574 L 2130.25,-503.574 L 2130.25,-527.496" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-489.066 L 3126.34,-472.197" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75953@0" ObjectIDND1="75951@1" ObjectIDND2="75952@0"/></metadata>
    <path d="M 3126.34,-489.066 L 3126.34,-472.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-511.066 L 3126.34,-489.066" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75953@0" ObjectIDND1="75951@1" ObjectIDND2="75952@0"/></metadata>
    <path d="M 3126.34,-511.066 L 3126.34,-489.066" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3144.34,-489.066 L 3126.34,-489.066" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="75953@0" ObjectIDND1="75951@1" ObjectIDND2="75952@0"/></metadata>
    <path d="M 3144.34,-489.066 L 3126.34,-489.066" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-424.066 L 3126.5,-409.846" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76075@0" ObjectIDND1="75953@1" ObjectIDND2="75954@0"/></metadata>
    <path d="M 3126.34,-424.066 L 3126.5,-409.846" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-438.799 L 3126.34,-424.066" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.34,-438.799 L 3126.34,-424.066" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-424.066 L 3156.04,-424.084" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.34,-424.066 L 3156.04,-424.084" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-546.826 L 3126.34,-529.066" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75951@0" ObjectIDND1="75949@1"/></metadata>
    <path d="M 3126.34,-546.826 L 3126.34,-529.066" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-594.363 L 3126.34,-581.386" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75950@1" ObjectIDND1="75949@0"/></metadata>
    <path d="M 3126.34,-594.363 L 3126.34,-581.386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.34,-658.154 L 3126.34,-610.832" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75950@0" ObjectIDND1="75753@-1"/></metadata>
    <path d="M 3126.34,-658.154 L 3126.34,-610.832" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-493.86 L 2765.56,-458.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76009@0" ObjectIDND1="75899@1" ObjectIDND2="75900@0"/></metadata>
    <path d="M 2765.56,-493.86 L 2765.56,-458.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-516.86 L 2765.56,-493.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76009@0" ObjectIDND1="75899@1" ObjectIDND2="75900@0"/></metadata>
    <path d="M 2765.56,-516.86 L 2765.56,-493.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-493.86 L 2783.56,-493.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76009@0" ObjectIDND1="75899@1" ObjectIDND2="75900@0"/></metadata>
    <path d="M 2765.56,-493.86 L 2783.56,-493.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-552.5 L 2765.56,-534.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75899@0" ObjectIDND1="75897@1"/></metadata>
    <path d="M 2765.56,-552.5 L 2765.56,-534.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-600.157 L 2765.56,-586.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75898@1" ObjectIDND1="75897@0"/></metadata>
    <path d="M 2765.56,-600.157 L 2765.56,-586.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2765.56,-658.154 L 2765.56,-616.626" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75898@0"/></metadata>
    <path d="M 2765.56,-658.154 L 2765.56,-616.626" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-494.86 L 2673.56,-459.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76008@0" ObjectIDND1="75895@1" ObjectIDND2="75896@0"/></metadata>
    <path d="M 2673.56,-494.86 L 2673.56,-459.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-517.86 L 2673.56,-494.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76008@0" ObjectIDND1="75895@1" ObjectIDND2="75896@0"/></metadata>
    <path d="M 2673.56,-517.86 L 2673.56,-494.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-494.86 L 2691.56,-494.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76008@0" ObjectIDND1="75895@1" ObjectIDND2="75896@0"/></metadata>
    <path d="M 2673.56,-494.86 L 2691.56,-494.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-553.5 L 2673.56,-535.86" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75895@0" ObjectIDND1="75893@1"/></metadata>
    <path d="M 2673.56,-553.5 L 2673.56,-535.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-601.157 L 2673.56,-587.34" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75894@1" ObjectIDND1="75893@0"/></metadata>
    <path d="M 2673.56,-601.157 L 2673.56,-587.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2673.56,-658.154 L 2673.56,-617.626" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75894@0"/></metadata>
    <path d="M 2673.56,-658.154 L 2673.56,-617.626" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-492.994 L 2869.32,-457.994" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76010@0" ObjectIDND1="75903@1" ObjectIDND2="75904@0"/></metadata>
    <path d="M 2869.32,-492.994 L 2869.32,-457.994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-515.994 L 2869.32,-492.994" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76010@0" ObjectIDND1="75903@1" ObjectIDND2="75904@0"/></metadata>
    <path d="M 2869.32,-515.994 L 2869.32,-492.994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-492.994 L 2887.32,-492.994" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="76010@0" ObjectIDND1="75903@1" ObjectIDND2="75904@0"/></metadata>
    <path d="M 2869.32,-492.994 L 2887.32,-492.994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-551.634 L 2869.32,-533.994" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75903@0" ObjectIDND1="75901@1"/></metadata>
    <path d="M 2869.32,-551.634 L 2869.32,-533.994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-599.291 L 2869.32,-585.474" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75902@1" ObjectIDND1="75901@0"/></metadata>
    <path d="M 2869.32,-599.291 L 2869.32,-585.474" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2869.32,-658.154 L 2869.32,-615.76" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="75753@-1" ObjectIDND1="75902@0"/></metadata>
    <path d="M 2869.32,-658.154 L 2869.32,-615.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3374.69,-495.505 L 3374.83,-474.36" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75923@1"/></metadata>
    <path d="M 3374.69,-495.505 L 3374.83,-474.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3374.83,-442.36 L 3374.97,-420.794" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75923@1"/></metadata>
    <path d="M 3374.83,-442.36 L 3374.97,-420.794" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3374.69,-583.802 L 3374.69,-513.505" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75923@0" ObjectIDND1="75922@1"/></metadata>
    <path d="M 3374.69,-583.802 L 3374.69,-513.505" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3374.69,-658.154 L 3374.69,-600.27" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75922@0" ObjectIDND1="75753@-1"/></metadata>
    <path d="M 3374.69,-658.154 L 3374.69,-600.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3374.76,-484.966 L 3406.09,-484.966" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75923@1"/></metadata>
    <path d="M 3374.76,-484.966 L 3406.09,-484.966" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 2986.67,-599.205 L 2986.67,-522.078" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 2986.67,-599.205 L 2986.67,-522.078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2986.67,-615.674 L 2986.67,-658.154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" ObjectIDND0="75753@-1"/></metadata>
    <path d="M 2986.67,-615.674 L 2986.67,-658.154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 2986.18,-369.188 L 2986.67,-504.078" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 2986.18,-369.188 L 2986.67,-504.078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 3250.07,-449.656 L 3250.07,-470.656" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 3250.07,-449.656 L 3250.07,-470.656" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3250.42,-521.558 L 3250.42,-498.559" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="75969@1" ObjectIDND1="75970@0"/></metadata>
    <path d="M 3250.42,-521.558 L 3250.42,-498.559" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3250.42,-498.559 L 3268.42,-498.558" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="75970@0" ObjectIDND1="75969@1"/></metadata>
    <path d="M 3250.42,-498.559 L 3268.42,-498.558" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3250.42,-557.198 L 3250.42,-539.558" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75969@0" ObjectIDND1="75967@1"/></metadata>
    <path d="M 3250.42,-557.198 L 3250.42,-539.558" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3250.42,-604.855 L 3250.42,-591.038" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="75968@1" ObjectIDND1="75967@0"/></metadata>
    <path d="M 3250.42,-604.855 L 3250.42,-591.038" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3250.42,-658.154 L 3250.42,-621.323" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75968@0" ObjectIDND1="75753@-1"/></metadata>
    <path d="M 3250.42,-658.154 L 3250.42,-621.323" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1822.15,-1155.3 L 1822.28,-1134.16" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75915@1"/></metadata>
    <path d="M 1822.15,-1155.3 L 1822.28,-1134.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1822.28,-1102.16 L 1822.42,-1080.59" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75915@1"/></metadata>
    <path d="M 1822.28,-1102.16 L 1822.42,-1080.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1822.15,-1243.6 L 1822.15,-1173.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="75915@0" ObjectIDND1="75914@1"/></metadata>
    <path d="M 1822.15,-1243.6 L 1822.15,-1173.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1822.15,-1305 L 1822.15,-1260.07" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="75914@0" ObjectIDND1="75751@-1"/></metadata>
    <path d="M 1822.15,-1305 L 1822.15,-1260.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1822.21,-1144.76 L 1853.54,-1144.76" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="75915@1"/></metadata>
    <path d="M 1822.21,-1144.76 L 1853.54,-1144.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 622.122,-310.712 L 621.885,-319.995 L 599.716,-320.155" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="76074@0" ObjectIDND1="75947@1" ObjectIDND2="75948@0"/></metadata>
    <path d="M 622.122,-310.712 L 621.885,-319.995 L 599.716,-320.155" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 769.036,-1025.16 L 769.036,-1013.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 769.036,-1025.16 L 769.036,-1013.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 768.896,-984.113 L 768.752,-970.174" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 768.896,-984.113 L 768.752,-970.174" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 736.842,-1016.24 L 769.036,-1016.24" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 736.842,-1016.24 L 769.036,-1016.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 746.583,-1001.13 L 768.896,-1000.94" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 746.583,-1001.13 L 768.896,-1000.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 768.989,-960.891 L 769.359,-946.172 L 787.592,-946.172 L 788.254,-1074.06 L 788.017,-1085.29" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 768.989,-960.891 L 769.359,-946.172 L 787.592,-946.172 L 788.254,-1074.06 L 788.017,-1085.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 768.989,-960.891 L 768.752,-970.174 L 746.583,-970.334" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75941@1" ObjectIDND1="75942@0" ObjectIDND2="76073@0"/></metadata>
    <path d="M 768.989,-960.891 L 768.752,-970.174 L 746.583,-970.334" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.5,-365.846 L 3126.5,-353.929" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.5,-365.846 L 3126.5,-353.929" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.36,-324.801 L 3126.22,-310.861" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.36,-324.801 L 3126.22,-310.861" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3094.31,-356.931 L 3126.5,-356.931" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3094.31,-356.931 L 3126.5,-356.931" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3104.05,-341.815 L 3126.36,-341.625" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3104.05,-341.815 L 3126.36,-341.625" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.45,-301.578 L 3126.82,-286.859 L 3145.06,-286.859 L 3145.72,-414.75 L 3145.52,-424.078" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.45,-301.578 L 3126.82,-286.859 L 3145.06,-286.859 L 3145.72,-414.75 L 3145.52,-424.078" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 3126.45,-301.578 L 3126.22,-310.861 L 3104.05,-311.021" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75953@1" ObjectIDND1="75954@0" ObjectIDND2="76075@0"/></metadata>
    <path d="M 3126.45,-301.578 L 3126.22,-310.861 L 3104.05,-311.021" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599.005,-1030.03 L 599.005,-1018.11" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 599.005,-1030.03 L 599.005,-1018.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 598.866,-988.986 L 598.721,-975.047" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 598.866,-988.986 L 598.721,-975.047" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 566.811,-1021.12 L 599.005,-1021.12" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 566.811,-1021.12 L 599.005,-1021.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 576.552,-1006 L 598.866,-1005.81" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 576.552,-1006 L 598.866,-1005.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 598.958,-965.764 L 599.328,-951.045 L 617.561,-951.045 L 618.223,-1078.94 L 618.011,-1089.01" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 598.958,-965.764 L 599.328,-951.045 L 617.561,-951.045 L 618.223,-1078.94 L 618.011,-1089.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 598.958,-965.764 L 598.721,-975.047 L 576.552,-975.207" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="75935@1" ObjectIDND1="75936@0" ObjectIDND2="76072@0"/></metadata>
    <path d="M 598.958,-965.764 L 598.721,-975.047 L 576.552,-975.207" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="BusbarSectionClass">
    <g id="RuiHZ.110#2">
      <path class="NFkV110" d="M 1139,-1601 L 1568,-1601" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="75750" ObjectName="RuiHZ.110#2"/><cge:TPSR_Ref TObjectID="75750"/></metadata>
    <path d="M 1139,-1601 L 1568,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="RuiHZ.10#1">
      <path class="NFkV10" d="M 466,-1305 L 2803.97,-1305" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="75751" ObjectName="RuiHZ.10#1"/><cge:TPSR_Ref TObjectID="75751"/></metadata>
    <path d="M 466,-1305 L 2803.97,-1305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="RuiHZ.110#1">
      <path class="NFkV110" d="M 524,-1601 L 931,-1601" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="75749" ObjectName="RuiHZ.110#1"/><cge:TPSR_Ref TObjectID="75749"/></metadata>
    <path d="M 524,-1601 L 931,-1601" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="RuiHZ.10#2A">
      <path class="NFkV10" d="M 458,-658 L 1955.76,-658" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="75752" ObjectName="RuiHZ.10#2A"/><cge:TPSR_Ref TObjectID="75752"/></metadata>
    <path d="M 458,-658 L 1955.76,-658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="RuiHZ.10#2B">
      <path class="NFkV10" d="M 2109.08,-658.154 L 3454.39,-658.154" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="75753" ObjectName="RuiHZ.10#2B"/><cge:TPSR_Ref TObjectID="75753"/></metadata>
    <path d="M 2109.08,-658.154 L 3454.39,-658.154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="ACLineSegmentClass">
    <g beginPointId="257808" endPointId="0" id="Llinrui" runFlow="0">
      <path class="NFkV110" d="M 1298,-1837 L 1298,-1866" stroke-width="4"/>
      <metadata><cge:PSR_Ref ObjectID="76566" ObjectName="76566"/><cge:TPSR_Ref TObjectID="76566_SS-401"/></metadata>
    <path d="M 1298,-1837 L 1298,-1866" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g beginPointId="257796" endPointId="0" id="" runFlow="0">
      <path class="NFBuDaiDian" d="M 804,-1832 L 804,-1865.11" stroke-width="4"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName="0"/></metadata>
    <path d="M 804,-1832 L 804,-1865.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="PowerTransformer2Class">
    <g id="RuiHZ.1T" primType="transformer2">
      <g id="WD-0">
        <use class="kV10" height="91.0628" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" xlink:href="#Transformer2:shape12_0" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="76013-L"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV110" height="91.0628" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" xlink:href="#Transformer2:shape12_1" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="76013-H"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="76013" ObjectName="RuiHZ.1T"/><cge:TPSR_Ref TObjectID="76013"/></metadata>
    <rect fill="white" height="91.0628" opacity="0" stroke="white" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" y="-91"/></g>
    <g id="RuiHZ.2T" primType="transformer2">
      <g id="WD-0">
        <use class="kV10" height="91" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" xlink:href="#Transformer2:shape12_0" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="76014-L"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV110" height="91" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" xlink:href="#Transformer2:shape12_1" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="76014-H"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="76014" ObjectName="RuiHZ.2T"/><cge:TPSR_Ref TObjectID="76014"/></metadata>
    <rect fill="white" height="91" opacity="0" stroke="white" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" y="-91"/></g>
  </g>
  <g id="BreakerClass">
    <g id="428279" primType="breaker">
      <use class="kV110" height="27" transform="matrix(0.96,0,0,0.62069,1017,-1591.07)" width="48" x="0" xlink:href="#Breaker:shape3_0" y="-29"/>
      <metadata><cge:PSR_Ref ObjectID="75909" ObjectName="RuiHZ.100BK"/>
        <cge:Meas_Ref ObjectID="ME-428279"/><cge:TPSR_Ref TObjectID="75909"/></metadata>
    <rect fill="white" height="27" opacity="0" stroke="white" transform="matrix(0.96,0,0,0.62069,1017,-1591.07)" width="48" x="0" y="-29"/></g>
    <g id="427232" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1934.78,-1183.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75813" ObjectName="RuiHZ.918BK"/>
        <cge:Meas_Ref ObjectID="ME-427232"/><cge:TPSR_Ref TObjectID="75813"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1934.78,-1183.55)" width="27" x="-4" y="-48"/></g>
    <g id="427277" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2024.78,-1184.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75817" ObjectName="RuiHZ.919BK"/>
        <cge:Meas_Ref ObjectID="ME-427277"/><cge:TPSR_Ref TObjectID="75817"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2024.78,-1184.55)" width="27" x="-4" y="-48"/></g>
    <g id="427322" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2110.78,-1184.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75821" ObjectName="RuiHZ.920BK"/>
        <cge:Meas_Ref ObjectID="ME-427322"/><cge:TPSR_Ref TObjectID="75821"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2110.78,-1184.55)" width="27" x="-4" y="-48"/></g>
    <g id="427367" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2194.78,-1181.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75825" ObjectName="RuiHZ.921BK"/>
        <cge:Meas_Ref ObjectID="ME-427367"/><cge:TPSR_Ref TObjectID="75825"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2194.78,-1181.55)" width="27" x="-4" y="-48"/></g>
    <g id="427457" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2395.1,-1191.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75833" ObjectName="RuiHZ.923BK"/>
        <cge:Meas_Ref ObjectID="ME-427457"/><cge:TPSR_Ref TObjectID="75833"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2395.1,-1191.55)" width="27" x="-4" y="-48"/></g>
    <g id="426917" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1003.15,-1193.34)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75785" ObjectName="RuiHZ.911BK"/>
        <cge:Meas_Ref ObjectID="ME-426917"/><cge:TPSR_Ref TObjectID="75785"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1003.15,-1193.34)" width="27" x="-4" y="-48"/></g>
    <g id="426962" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1087.15,-1193.34)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75789" ObjectName="RuiHZ.912BK"/>
        <cge:Meas_Ref ObjectID="ME-426962"/><cge:TPSR_Ref TObjectID="75789"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1087.15,-1193.34)" width="27" x="-4" y="-48"/></g>
    <g id="427007" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1198.18,-1195.47)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75793" ObjectName="RuiHZ.913BK"/>
        <cge:Meas_Ref ObjectID="ME-427007"/><cge:TPSR_Ref TObjectID="75793"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1198.18,-1195.47)" width="27" x="-4" y="-48"/></g>
    <g id="427052" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1284.18,-1196.47)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75797" ObjectName="RuiHZ.914BK"/>
        <cge:Meas_Ref ObjectID="ME-427052"/><cge:TPSR_Ref TObjectID="75797"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1284.18,-1196.47)" width="27" x="-4" y="-48"/></g>
    <g id="427097" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1373.18,-1195.47)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75801" ObjectName="RuiHZ.915BK"/>
        <cge:Meas_Ref ObjectID="ME-427097"/><cge:TPSR_Ref TObjectID="75801"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1373.18,-1195.47)" width="27" x="-4" y="-48"/></g>
    <g id="427142" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1487.02,-1197.86)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75805" ObjectName="RuiHZ.916BK"/>
        <cge:Meas_Ref ObjectID="ME-427142"/><cge:TPSR_Ref TObjectID="75805"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1487.02,-1197.86)" width="27" x="-4" y="-48"/></g>
    <g id="427187" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1573.02,-1197.86)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75809" ObjectName="RuiHZ.917BK"/>
        <cge:Meas_Ref ObjectID="ME-427187"/><cge:TPSR_Ref TObjectID="75809"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1573.02,-1197.86)" width="27" x="-4" y="-48"/></g>
    <g id="428353" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,592.552,-1206)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75931" ObjectName="RuiHZ.C01BK"/>
        <cge:Meas_Ref ObjectID="ME-428353"/><cge:TPSR_Ref TObjectID="75931"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,592.552,-1206)" width="27" x="-4" y="-48"/></g>
    <g id="428469" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,478.552,-1205)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75963" ObjectName="RuiHZ.993BK"/>
        <cge:Meas_Ref ObjectID="ME-428469"/><cge:TPSR_Ref TObjectID="75963"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,478.552,-1205)" width="27" x="-4" y="-48"/></g>
    <g id="426806" primType="breaker">
      <use class="kV110" height="48" transform="matrix(0.586207,0,0,0.94,797.552,-1685)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75771" ObjectName="RuiHZ.103BK"/>
        <cge:Meas_Ref ObjectID="ME-426806"/><cge:TPSR_Ref TObjectID="75771"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,797.552,-1685)" width="27" x="-4" y="-48"/></g>
    <g id="426865" primType="breaker">
      <use class="kV110" height="48" transform="matrix(0.62069,0,0,0.94,1291.17,-1682)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75778" ObjectName="RuiHZ.104BK"/>
        <cge:Meas_Ref ObjectID="ME-426865"/><cge:TPSR_Ref TObjectID="75778"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.62069,0,0,0.94,1291.17,-1682)" width="27" x="-4" y="-48"/></g>
    <g id="427727" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1042.02,-554.711)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75857" ObjectName="RuiHZ.929BK"/>
        <cge:Meas_Ref ObjectID="ME-427727"/><cge:TPSR_Ref TObjectID="75857"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1042.02,-554.711)" width="27" x="-4" y="-48"/></g>
    <g id="427907" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1575.65,-559.281)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75873" ObjectName="RuiHZ.933BK"/>
        <cge:Meas_Ref ObjectID="ME-427907"/><cge:TPSR_Ref TObjectID="75873"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1575.65,-559.281)" width="27" x="-4" y="-48"/></g>
    <g id="427592" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,740.347,-554.775)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75845" ObjectName="RuiHZ.926BK"/>
        <cge:Meas_Ref ObjectID="ME-427592"/><cge:TPSR_Ref TObjectID="75845"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,740.347,-554.775)" width="27" x="-4" y="-48"/></g>
    <g id="427637" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,846.256,-556.577)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75849" ObjectName="RuiHZ.927BK"/>
        <cge:Meas_Ref ObjectID="ME-427637"/><cge:TPSR_Ref TObjectID="75849"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,846.256,-556.577)" width="27" x="-4" y="-48"/></g>
    <g id="427682" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,938.256,-555.577)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75853" ObjectName="RuiHZ.928BK"/>
        <cge:Meas_Ref ObjectID="ME-427682"/><cge:TPSR_Ref TObjectID="75853"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,938.256,-555.577)" width="27" x="-4" y="-48"/></g>
    <g id="426733" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1885.42,-553.133)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75765" ObjectName="RuiHZ.902BK"/>
        <cge:Meas_Ref ObjectID="ME-426733"/><cge:TPSR_Ref TObjectID="75765"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1885.42,-553.133)" width="27" x="-4" y="-48"/></g>
    <g id="427412" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2284.78,-1181.55)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75829" ObjectName="RuiHZ.922BK"/>
        <cge:Meas_Ref ObjectID="ME-427412"/><cge:TPSR_Ref TObjectID="75829"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2284.78,-1181.55)" width="27" x="-4" y="-48"/></g>
    <g id="427502" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2500.43,-1195.07)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75837" ObjectName="RuiHZ.924BK"/>
        <cge:Meas_Ref ObjectID="ME-427502"/><cge:TPSR_Ref TObjectID="75837"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2500.43,-1195.07)" width="27" x="-4" y="-48"/></g>
    <g id="427547" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2602.29,-1195.07)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75841" ObjectName="RuiHZ.925BK"/>
        <cge:Meas_Ref ObjectID="ME-427547"/><cge:TPSR_Ref TObjectID="75841"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2602.29,-1195.07)" width="27" x="-4" y="-48"/></g>
    <g id="428434" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1706.07,-1192.66)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75955" ObjectName="RuiHZ.991BK"/>
        <cge:Meas_Ref ObjectID="ME-428434"/><cge:TPSR_Ref TObjectID="75955"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1706.07,-1192.66)" width="27" x="-4" y="-48"/></g>
    <g id="426711" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,913.552,-1206)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75762" ObjectName="RuiHZ.901BK"/>
        <cge:Meas_Ref ObjectID="ME-426711"/><cge:TPSR_Ref TObjectID="75762"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,913.552,-1206)" width="27" x="-4" y="-48"/></g>
    <g id="428934" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,761.335,-1202.28)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75937" ObjectName="RuiHZ.C02BK"/>
        <cge:Meas_Ref ObjectID="ME-428934"/><cge:TPSR_Ref TObjectID="75937"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,761.335,-1202.28)" width="27" x="-4" y="-48"/></g>
    <g id="428335" primType="breaker">
      <use class="kV10" height="43.7461" transform="matrix(0.586207,0,0,0.94,472.718,-557.985)" width="24.6072" x="-2.8036" xlink:href="#Breaker:shape2_0" y="-43.7461"/>
      <metadata><cge:PSR_Ref ObjectID="75926" ObjectName="RuiHZ.9001BK"/>
        <cge:Meas_Ref ObjectID="ME-428335"/><cge:TPSR_Ref TObjectID="75926"/></metadata>
    <rect fill="white" height="43.7461" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,472.718,-557.985)" width="24.6072" x="-2.8036" y="-43.7461"/></g>
    <g id="428392" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,615.721,-550.98)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75943" ObjectName="RuiHZ.C03BK"/>
        <cge:Meas_Ref ObjectID="ME-428392"/><cge:TPSR_Ref TObjectID="75943"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,615.721,-550.98)" width="27" x="-4" y="-48"/></g>
    <g id="427862" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1337.83,-550.508)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75869" ObjectName="RuiHZ.932BK"/>
        <cge:Meas_Ref ObjectID="ME-427862"/><cge:TPSR_Ref TObjectID="75869"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1337.83,-550.508)" width="27" x="-4" y="-48"/></g>
    <g id="427772" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1142.07,-552.374)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75861" ObjectName="RuiHZ.930BK"/>
        <cge:Meas_Ref ObjectID="ME-427772"/><cge:TPSR_Ref TObjectID="75861"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1142.07,-552.374)" width="27" x="-4" y="-48"/></g>
    <g id="427817" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1234.07,-551.374)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75865" ObjectName="RuiHZ.931BK"/>
        <cge:Meas_Ref ObjectID="ME-427817"/><cge:TPSR_Ref TObjectID="75865"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1234.07,-551.374)" width="27" x="-4" y="-48"/></g>
    <g id="428452" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1461.4,-544.419)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75959" ObjectName="RuiHZ.992BK"/>
        <cge:Meas_Ref ObjectID="ME-428452"/><cge:TPSR_Ref TObjectID="75959"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1461.4,-544.419)" width="27" x="-4" y="-48"/></g>
    <g id="428087" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2567.06,-550.197)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75889" ObjectName="RuiHZ.937BK"/>
        <cge:Meas_Ref ObjectID="ME-428087"/><cge:TPSR_Ref TObjectID="75889"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2567.06,-550.197)" width="27" x="-4" y="-48"/></g>
    <g id="427952" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2265.39,-550.261)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75877" ObjectName="RuiHZ.934BK"/>
        <cge:Meas_Ref ObjectID="ME-427952"/><cge:TPSR_Ref TObjectID="75877"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2265.39,-550.261)" width="27" x="-4" y="-48"/></g>
    <g id="427997" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2371.3,-552.063)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75881" ObjectName="RuiHZ.935BK"/>
        <cge:Meas_Ref ObjectID="ME-427997"/><cge:TPSR_Ref TObjectID="75881"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2371.3,-552.063)" width="27" x="-4" y="-48"/></g>
    <g id="428042" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2463.3,-551.063)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75885" ObjectName="RuiHZ.936BK"/>
        <cge:Meas_Ref ObjectID="ME-428042"/><cge:TPSR_Ref TObjectID="75885"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2463.3,-551.063)" width="27" x="-4" y="-48"/></g>
    <g id="426755" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2123.8,-558.139)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75768" ObjectName="RuiHZ.905BK"/>
        <cge:Meas_Ref ObjectID="ME-426755"/><cge:TPSR_Ref TObjectID="75768"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2123.8,-558.139)" width="27" x="-4" y="-48"/></g>
    <g id="428413" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,3119.89,-541.066)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75949" ObjectName="RuiHZ.C04BK"/>
        <cge:Meas_Ref ObjectID="ME-428413"/><cge:TPSR_Ref TObjectID="75949"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,3119.89,-541.066)" width="27" x="-4" y="-48"/></g>
    <g id="428222" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2862.87,-545.994)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75901" ObjectName="RuiHZ.940BK"/>
        <cge:Meas_Ref ObjectID="ME-428222"/><cge:TPSR_Ref TObjectID="75901"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2862.87,-545.994)" width="27" x="-4" y="-48"/></g>
    <g id="428132" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2667.11,-547.86)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75893" ObjectName="RuiHZ.938BK"/>
        <cge:Meas_Ref ObjectID="ME-428132"/><cge:TPSR_Ref TObjectID="75893"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2667.11,-547.86)" width="27" x="-4" y="-48"/></g>
    <g id="428177" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2759.11,-546.86)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75897" ObjectName="RuiHZ.939BK"/>
        <cge:Meas_Ref ObjectID="ME-428177"/><cge:TPSR_Ref TObjectID="75897"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2759.11,-546.86)" width="27" x="-4" y="-48"/></g>
    <g id="428487" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,3243.97,-551.558)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="75967" ObjectName="RuiHZ.994BK"/>
        <cge:Meas_Ref ObjectID="ME-428487"/><cge:TPSR_Ref TObjectID="75967"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,3243.97,-551.558)" width="27" x="-4" y="-48"/></g>
  </g>
  <g id="DisconnectorClass">
    <g id="426662" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,807.187,-1461)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75754" ObjectName="RuiHZ.1034SW"/>
        <cge:Meas_Ref ObjectID="ME-426662"/><cge:TPSR_Ref TObjectID="75754"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,807.187,-1461)" width="16" x="5" y="-63"/></g>
    <g id="426664" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,850.946,-1465.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75755" ObjectName="RuiHZ.10348SW"/>
        <cge:Meas_Ref ObjectID="ME-426664"/><cge:TPSR_Ref TObjectID="75755"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,850.946,-1465.7)" width="60" x="-1" y="-23"/></g>
    <g id="426666" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,679.965,-1324.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75757" ObjectName="RuiHZ.1010SW"/>
        <cge:Meas_Ref ObjectID="ME-426666"/><cge:TPSR_Ref TObjectID="75757"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,679.965,-1324.92)" width="25" x="3" y="-100"/></g>
    <g id="426686" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1256.2,-1456)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75758" ObjectName="RuiHZ.1044SW"/>
        <cge:Meas_Ref ObjectID="ME-426686"/><cge:TPSR_Ref TObjectID="75758"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1256.2,-1456)" width="16" x="5" y="-63"/></g>
    <g id="426688" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1302.95,-1466.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75759" ObjectName="RuiHZ.10448SW"/>
        <cge:Meas_Ref ObjectID="ME-426688"/><cge:TPSR_Ref TObjectID="75759"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1302.95,-1466.7)" width="60" x="-1" y="-23"/></g>
    <g id="426690" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1129.96,-1323.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75761" ObjectName="RuiHZ.1020SW"/>
        <cge:Meas_Ref ObjectID="ME-426690"/><cge:TPSR_Ref TObjectID="75761"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1129.96,-1323.92)" width="25" x="3" y="-100"/></g>
    <g id="428281" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,952,-1596.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75910" ObjectName="RuiHZ.1001SW"/>
        <cge:Meas_Ref ObjectID="ME-428281"/><cge:TPSR_Ref TObjectID="75910"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,952,-1596.34)" width="45" x="1" y="-15"/></g>
    <g id="428283" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,1075,-1596.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75911" ObjectName="RuiHZ.1002SW"/>
        <cge:Meas_Ref ObjectID="ME-428283"/><cge:TPSR_Ref TObjectID="75911"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1075,-1596.34)" width="45" x="1" y="-15"/></g>
    <g id="428286" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1000.96,-1477.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75913" ObjectName="RuiHZ.10017SW"/>
        <cge:Meas_Ref ObjectID="ME-428286"/><cge:TPSR_Ref TObjectID="75913"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1000.96,-1477.92)" width="25" x="3" y="-100"/></g>
    <g id="428285" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1059.96,-1476.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75912" ObjectName="RuiHZ.10027SW"/>
        <cge:Meas_Ref ObjectID="ME-428285"/><cge:TPSR_Ref TObjectID="75912"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1059.96,-1476.92)" width="25" x="3" y="-100"/></g>
    <g id="427234" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1931.23,-1230.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75814" ObjectName="RuiHZ.918XC"/>
        <cge:Meas_Ref ObjectID="ME-427234"/><cge:TPSR_Ref TObjectID="75814"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1931.23,-1230.55)" width="22" x="-1" y="-30"/></g>
    <g id="427234" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1930.23" xlink:href="#Disconnector:shape3_0" y="-1177.55"/>
      <metadata><cge:PSR_Ref ObjectID="75815" ObjectName="RuiHZ.918XC1"/>
        <cge:Meas_Ref ObjectID="ME-427234"/><cge:TPSR_Ref TObjectID="75815"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1930.23" y="-1177.55"/></g>
    <g id="427235" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1954.18,-1124.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75816" ObjectName="RuiHZ.91838SW"/>
        <cge:Meas_Ref ObjectID="ME-427235"/><cge:TPSR_Ref TObjectID="75816"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1954.18,-1124.25)" width="60" x="-1" y="-23"/></g>
    <g id="427279" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2021.23,-1231.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75818" ObjectName="RuiHZ.919XC"/>
        <cge:Meas_Ref ObjectID="ME-427279"/><cge:TPSR_Ref TObjectID="75818"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2021.23,-1231.55)" width="22" x="-1" y="-30"/></g>
    <g id="427279" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2020.23" xlink:href="#Disconnector:shape3_0" y="-1178.55"/>
      <metadata><cge:PSR_Ref ObjectID="75819" ObjectName="RuiHZ.919XC1"/>
        <cge:Meas_Ref ObjectID="ME-427279"/><cge:TPSR_Ref TObjectID="75819"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2020.23" y="-1178.55"/></g>
    <g id="427280" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2044.18,-1125.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75820" ObjectName="RuiHZ.91938SW"/>
        <cge:Meas_Ref ObjectID="ME-427280"/><cge:TPSR_Ref TObjectID="75820"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2044.18,-1125.25)" width="60" x="-1" y="-23"/></g>
    <g id="427324" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2107.23,-1231.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75822" ObjectName="RuiHZ.920XC"/>
        <cge:Meas_Ref ObjectID="ME-427324"/><cge:TPSR_Ref TObjectID="75822"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2107.23,-1231.55)" width="22" x="-1" y="-30"/></g>
    <g id="427324" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2106.23" xlink:href="#Disconnector:shape3_0" y="-1178.55"/>
      <metadata><cge:PSR_Ref ObjectID="75823" ObjectName="RuiHZ.920XC1"/>
        <cge:Meas_Ref ObjectID="ME-427324"/><cge:TPSR_Ref TObjectID="75823"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2106.23" y="-1178.55"/></g>
    <g id="427325" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2130.18,-1125.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75824" ObjectName="RuiHZ.92038SW"/>
        <cge:Meas_Ref ObjectID="ME-427325"/><cge:TPSR_Ref TObjectID="75824"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2130.18,-1125.25)" width="60" x="-1" y="-23"/></g>
    <g id="427369" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2191.23,-1228.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75826" ObjectName="RuiHZ.921XC"/>
        <cge:Meas_Ref ObjectID="ME-427369"/><cge:TPSR_Ref TObjectID="75826"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2191.23,-1228.55)" width="22" x="-1" y="-30"/></g>
    <g id="427369" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2190.23" xlink:href="#Disconnector:shape3_0" y="-1173.55"/>
      <metadata><cge:PSR_Ref ObjectID="75827" ObjectName="RuiHZ.921XC1"/>
        <cge:Meas_Ref ObjectID="ME-427369"/><cge:TPSR_Ref TObjectID="75827"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2190.23" y="-1173.55"/></g>
    <g id="427370" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2214.18,-1122.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75828" ObjectName="RuiHZ.92138SW"/>
        <cge:Meas_Ref ObjectID="ME-427370"/><cge:TPSR_Ref TObjectID="75828"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2214.18,-1122.25)" width="60" x="-1" y="-23"/></g>
    <g id="427414" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2281.23,-1229.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75830" ObjectName="RuiHZ.922XC"/>
        <cge:Meas_Ref ObjectID="ME-427414"/><cge:TPSR_Ref TObjectID="75830"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2281.23,-1229.55)" width="22" x="-1" y="-30"/></g>
    <g id="427414" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2280.23" xlink:href="#Disconnector:shape3_0" y="-1174.55"/>
      <metadata><cge:PSR_Ref ObjectID="75831" ObjectName="RuiHZ.922XC1"/>
        <cge:Meas_Ref ObjectID="ME-427414"/><cge:TPSR_Ref TObjectID="75831"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2280.23" y="-1174.55"/></g>
    <g id="427415" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2304.18,-1123.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75832" ObjectName="RuiHZ.92238SW"/>
        <cge:Meas_Ref ObjectID="ME-427415"/><cge:TPSR_Ref TObjectID="75832"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2304.18,-1123.25)" width="60" x="-1" y="-23"/></g>
    <g id="427459" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2390.1,-1234.55)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75834" ObjectName="RuiHZ.923XC"/>
        <cge:Meas_Ref ObjectID="ME-427459"/><cge:TPSR_Ref TObjectID="75834"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2390.1,-1234.55)" width="22" x="-1" y="-30"/></g>
    <g id="427459" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2390.55" xlink:href="#Disconnector:shape3_0" y="-1181.55"/>
      <metadata><cge:PSR_Ref ObjectID="75835" ObjectName="RuiHZ.923XC1"/>
        <cge:Meas_Ref ObjectID="ME-427459"/><cge:TPSR_Ref TObjectID="75835"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2390.55" y="-1181.55"/></g>
    <g id="427460" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2413.05,-1128.25)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75836" ObjectName="RuiHZ.92338SW"/>
        <cge:Meas_Ref ObjectID="ME-427460"/><cge:TPSR_Ref TObjectID="75836"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2413.05,-1128.25)" width="60" x="-1" y="-23"/></g>
    <g id="426665" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,851.946,-1529.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75756" ObjectName="RuiHZ.10347SW"/>
        <cge:Meas_Ref ObjectID="ME-426665"/><cge:TPSR_Ref TObjectID="75756"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,851.946,-1529.7)" width="60" x="-1" y="-23"/></g>
    <g id="426919" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,999.593,-1240.34)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75786" ObjectName="RuiHZ.911XC"/>
        <cge:Meas_Ref ObjectID="ME-426919"/><cge:TPSR_Ref TObjectID="75786"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,999.593,-1240.34)" width="22" x="-1" y="-30"/></g>
    <g id="426919" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="998.593" xlink:href="#Disconnector:shape3_0" y="-1187.34"/>
      <metadata><cge:PSR_Ref ObjectID="75787" ObjectName="RuiHZ.911XC1"/>
        <cge:Meas_Ref ObjectID="ME-426919"/><cge:TPSR_Ref TObjectID="75787"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="998.593" y="-1187.34"/></g>
    <g id="426920" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1022.54,-1134.04)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75788" ObjectName="RuiHZ.91138SW"/>
        <cge:Meas_Ref ObjectID="ME-426920"/><cge:TPSR_Ref TObjectID="75788"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1022.54,-1134.04)" width="60" x="-1" y="-23"/></g>
    <g id="426964" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1083.59,-1240.34)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75790" ObjectName="RuiHZ.912XC"/>
        <cge:Meas_Ref ObjectID="ME-426964"/><cge:TPSR_Ref TObjectID="75790"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1083.59,-1240.34)" width="22" x="-1" y="-30"/></g>
    <g id="426964" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1082.59" xlink:href="#Disconnector:shape3_0" y="-1187.34"/>
      <metadata><cge:PSR_Ref ObjectID="75791" ObjectName="RuiHZ.912XC1"/>
        <cge:Meas_Ref ObjectID="ME-426964"/><cge:TPSR_Ref TObjectID="75791"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1082.59" y="-1187.34"/></g>
    <g id="426965" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1106.54,-1134.04)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75792" ObjectName="RuiHZ.91238SW"/>
        <cge:Meas_Ref ObjectID="ME-426965"/><cge:TPSR_Ref TObjectID="75792"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1106.54,-1134.04)" width="60" x="-1" y="-23"/></g>
    <g id="427009" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1194.63,-1242.47)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75794" ObjectName="RuiHZ.913XC"/>
        <cge:Meas_Ref ObjectID="ME-427009"/><cge:TPSR_Ref TObjectID="75794"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1194.63,-1242.47)" width="22" x="-1" y="-30"/></g>
    <g id="427009" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1193.63" xlink:href="#Disconnector:shape3_0" y="-1189.47"/>
      <metadata><cge:PSR_Ref ObjectID="75795" ObjectName="RuiHZ.913XC1"/>
        <cge:Meas_Ref ObjectID="ME-427009"/><cge:TPSR_Ref TObjectID="75795"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1193.63" y="-1189.47"/></g>
    <g id="427010" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1217.58,-1136.17)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75796" ObjectName="RuiHZ.91338SW"/>
        <cge:Meas_Ref ObjectID="ME-427010"/><cge:TPSR_Ref TObjectID="75796"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1217.58,-1136.17)" width="60" x="-1" y="-23"/></g>
    <g id="427054" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1280.63,-1243.47)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75798" ObjectName="RuiHZ.914XC"/>
        <cge:Meas_Ref ObjectID="ME-427054"/><cge:TPSR_Ref TObjectID="75798"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1280.63,-1243.47)" width="22" x="-1" y="-30"/></g>
    <g id="427054" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1279.63" xlink:href="#Disconnector:shape3_0" y="-1190.47"/>
      <metadata><cge:PSR_Ref ObjectID="75799" ObjectName="RuiHZ.914XC1"/>
        <cge:Meas_Ref ObjectID="ME-427054"/><cge:TPSR_Ref TObjectID="75799"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1279.63" y="-1190.47"/></g>
    <g id="427055" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1303.58,-1137.17)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75800" ObjectName="RuiHZ.91438SW"/>
        <cge:Meas_Ref ObjectID="ME-427055"/><cge:TPSR_Ref TObjectID="75800"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1303.58,-1137.17)" width="60" x="-1" y="-23"/></g>
    <g id="427099" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1369.63,-1242.47)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75802" ObjectName="RuiHZ.915XC"/>
        <cge:Meas_Ref ObjectID="ME-427099"/><cge:TPSR_Ref TObjectID="75802"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1369.63,-1242.47)" width="22" x="-1" y="-30"/></g>
    <g id="427099" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1368.63" xlink:href="#Disconnector:shape3_0" y="-1189.47"/>
      <metadata><cge:PSR_Ref ObjectID="75803" ObjectName="RuiHZ.915XC1"/>
        <cge:Meas_Ref ObjectID="ME-427099"/><cge:TPSR_Ref TObjectID="75803"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1368.63" y="-1189.47"/></g>
    <g id="427100" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1392.58,-1136.17)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75804" ObjectName="RuiHZ.91538SW"/>
        <cge:Meas_Ref ObjectID="ME-427100"/><cge:TPSR_Ref TObjectID="75804"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1392.58,-1136.17)" width="60" x="-1" y="-23"/></g>
    <g id="427144" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1483.47,-1244.86)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75806" ObjectName="RuiHZ.916XC"/>
        <cge:Meas_Ref ObjectID="ME-427144"/><cge:TPSR_Ref TObjectID="75806"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1483.47,-1244.86)" width="22" x="-1" y="-30"/></g>
    <g id="427144" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1482.47" xlink:href="#Disconnector:shape3_0" y="-1191.86"/>
      <metadata><cge:PSR_Ref ObjectID="75807" ObjectName="RuiHZ.916XC1"/>
        <cge:Meas_Ref ObjectID="ME-427144"/><cge:TPSR_Ref TObjectID="75807"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1482.47" y="-1191.86"/></g>
    <g id="427145" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1506.42,-1138.56)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75808" ObjectName="RuiHZ.91638SW"/>
        <cge:Meas_Ref ObjectID="ME-427145"/><cge:TPSR_Ref TObjectID="75808"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1506.42,-1138.56)" width="60" x="-1" y="-23"/></g>
    <g id="427189" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1569.47,-1242.86)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75810" ObjectName="RuiHZ.917XC"/>
        <cge:Meas_Ref ObjectID="ME-427189"/><cge:TPSR_Ref TObjectID="75810"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1569.47,-1242.86)" width="22" x="-1" y="-30"/></g>
    <g id="427189" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1568.47" xlink:href="#Disconnector:shape3_0" y="-1187.86"/>
      <metadata><cge:PSR_Ref ObjectID="75811" ObjectName="RuiHZ.917XC1"/>
        <cge:Meas_Ref ObjectID="ME-427189"/><cge:TPSR_Ref TObjectID="75811"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1568.47" y="-1187.86"/></g>
    <g id="427190" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1592.42,-1136.56)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75812" ObjectName="RuiHZ.91738SW"/>
        <cge:Meas_Ref ObjectID="ME-427190"/><cge:TPSR_Ref TObjectID="75812"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1592.42,-1136.56)" width="60" x="-1" y="-23"/></g>
    <g id="428355" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,589,-1253)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75932" ObjectName="RuiHZ.C01XC"/>
        <cge:Meas_Ref ObjectID="ME-428355"/><cge:TPSR_Ref TObjectID="75932"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,589,-1253)" width="22" x="-1" y="-30"/></g>
    <g id="428355" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="588" xlink:href="#Disconnector:shape3_0" y="-1200"/>
      <metadata><cge:PSR_Ref ObjectID="75933" ObjectName="RuiHZ.C01XC1"/>
        <cge:Meas_Ref ObjectID="ME-428355"/><cge:TPSR_Ref TObjectID="75933"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="588" y="-1200"/></g>
    <g id="428356" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,611.946,-1147.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75934" ObjectName="RuiHZ.C0168SW"/>
        <cge:Meas_Ref ObjectID="ME-428356"/><cge:TPSR_Ref TObjectID="75934"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,611.946,-1147.7)" width="60" x="-1" y="-23"/></g>
    <g id="428357" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,584.187,-1076)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75935" ObjectName="RuiHZ.C016SW"/>
        <cge:Meas_Ref ObjectID="ME-428357"/><cge:TPSR_Ref TObjectID="75935"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,584.187,-1076)" width="16" x="5" y="-63"/></g>
    <g id="428471" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,475,-1252)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75964" ObjectName="RuiHZ.993XC"/>
        <cge:Meas_Ref ObjectID="ME-428471"/><cge:TPSR_Ref TObjectID="75964"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,475,-1252)" width="22" x="-1" y="-30"/></g>
    <g id="428471" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="474" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="75965" ObjectName="RuiHZ.993XC1"/>
        <cge:Meas_Ref ObjectID="ME-428471"/><cge:TPSR_Ref TObjectID="75965"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="474" y="-1199"/></g>
    <g id="428472" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,497.946,-1145.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75966" ObjectName="RuiHZ.99338SW"/>
        <cge:Meas_Ref ObjectID="ME-428472"/><cge:TPSR_Ref TObjectID="75966"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,497.946,-1145.7)" width="60" x="-1" y="-23"/></g>
    <g id="426713" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,910,-1254)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75763" ObjectName="RuiHZ.901XC"/>
        <cge:Meas_Ref ObjectID="ME-426713"/><cge:TPSR_Ref TObjectID="75763"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,910,-1254)" width="22" x="-1" y="-30"/></g>
    <g id="426713" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="909" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="75764" ObjectName="RuiHZ.901XC1"/>
        <cge:Meas_Ref ObjectID="ME-426713"/><cge:TPSR_Ref TObjectID="75764"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="909" y="-1199"/></g>
    <g id="426810" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,789.187,-1727)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75773" ObjectName="RuiHZ.1033SW"/>
        <cge:Meas_Ref ObjectID="ME-426810"/><cge:TPSR_Ref TObjectID="75773"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,789.187,-1727)" width="16" x="5" y="-63"/></g>
    <g id="426808" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,789.187,-1597)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75772" ObjectName="RuiHZ.1031SW"/>
        <cge:Meas_Ref ObjectID="ME-426808"/><cge:TPSR_Ref TObjectID="75772"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,789.187,-1597)" width="16" x="5" y="-63"/></g>
    <g id="426814" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,828.946,-1807.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75775" ObjectName="RuiHZ.10338SW"/>
        <cge:Meas_Ref ObjectID="ME-426814"/><cge:TPSR_Ref TObjectID="75775"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,828.946,-1807.7)" width="60" x="-1" y="-23"/></g>
    <g id="426815" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,829.946,-1732.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75776" ObjectName="RuiHZ.10337SW"/>
        <cge:Meas_Ref ObjectID="ME-426815"/><cge:TPSR_Ref TObjectID="75776"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,829.946,-1732.7)" width="60" x="-1" y="-23"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="25" transform="matrix(0.95082,0,0,0.666667,829.946,-1666.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,829.946,-1666.7)" width="60" x="-1" y="-23"/></g>
    <g id="426869" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1283.19,-1725)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75780" ObjectName="RuiHZ.1043SW"/>
        <cge:Meas_Ref ObjectID="ME-426869"/><cge:TPSR_Ref TObjectID="75780"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1283.19,-1725)" width="16" x="5" y="-63"/></g>
    <g id="426867" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1283.19,-1596)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75779" ObjectName="RuiHZ.1041SW"/>
        <cge:Meas_Ref ObjectID="ME-426867"/><cge:TPSR_Ref TObjectID="75779"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1283.19,-1596)" width="16" x="5" y="-63"/></g>
    <g id="426873" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1321.95,-1803.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75782" ObjectName="RuiHZ.10438SW"/>
        <cge:Meas_Ref ObjectID="ME-426873"/><cge:TPSR_Ref TObjectID="75782"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1321.95,-1803.7)" width="60" x="-1" y="-23"/></g>
    <g id="426874" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1323.95,-1731.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75783" ObjectName="RuiHZ.10437SW"/>
        <cge:Meas_Ref ObjectID="ME-426874"/><cge:TPSR_Ref TObjectID="75783"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1323.95,-1731.7)" width="60" x="-1" y="-23"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="25" transform="matrix(0.95082,0,0,0.666667,1323.95,-1665.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1323.95,-1665.7)" width="60" x="-1" y="-23"/></g>
    <g id="426689" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1298.95,-1523.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75760" ObjectName="RuiHZ.10447SW"/>
        <cge:Meas_Ref ObjectID="ME-426689"/><cge:TPSR_Ref TObjectID="75760"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1298.95,-1523.7)" width="60" x="-1" y="-23"/></g>
    <g id="428359" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,623.647,-1082.72)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75936" ObjectName="RuiHZ.C0167SW"/>
        <cge:Meas_Ref ObjectID="ME-428359"/><cge:TPSR_Ref TObjectID="75936"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,623.647,-1082.72)" width="60" x="-1" y="-23"/></g>
    <g id="426875" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1210.96,-1682.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75784" ObjectName="RuiHZ.01047SW"/>
        <cge:Meas_Ref ObjectID="ME-426875"/><cge:TPSR_Ref TObjectID="75784"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1210.96,-1682.92)" width="25" x="3" y="-100"/></g>
    <g id="426871" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,1232,-1805.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75781" ObjectName="RuiHZ.0104SW"/>
        <cge:Meas_Ref ObjectID="ME-426871"/><cge:TPSR_Ref TObjectID="75781"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1232,-1805.34)" width="45" x="1" y="-15"/></g>
    <g id="426816" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,717.965,-1684.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75777" ObjectName="RuiHZ.01037SW"/>
        <cge:Meas_Ref ObjectID="ME-426816"/><cge:TPSR_Ref TObjectID="75777"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,717.965,-1684.92)" width="25" x="3" y="-100"/></g>
    <g id="426812" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,739,-1809.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75774" ObjectName="RuiHZ.0103SW"/>
        <cge:Meas_Ref ObjectID="ME-426812"/><cge:TPSR_Ref TObjectID="75774"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,739,-1809.34)" width="45" x="1" y="-15"/></g>
    <g id="427729" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1038.47,-601.711)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75858" ObjectName="RuiHZ.929XC"/>
        <cge:Meas_Ref ObjectID="ME-427729"/><cge:TPSR_Ref TObjectID="75858"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1038.47,-601.711)" width="22" x="-1" y="-30"/></g>
    <g id="427729" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1037.47" xlink:href="#Disconnector:shape3_0" y="-548.711"/>
      <metadata><cge:PSR_Ref ObjectID="75859" ObjectName="RuiHZ.929XC1"/>
        <cge:Meas_Ref ObjectID="ME-427729"/><cge:TPSR_Ref TObjectID="75859"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1037.47" y="-548.711"/></g>
    <g id="427730" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1061.42,-495.806)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75860" ObjectName="RuiHZ.92938SW"/>
        <cge:Meas_Ref ObjectID="ME-427730"/><cge:TPSR_Ref TObjectID="75860"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1061.42,-495.806)" width="60" x="-1" y="-23"/></g>
    <g id="427909" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1572.91,-607.089)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75874" ObjectName="RuiHZ.933XC"/>
        <cge:Meas_Ref ObjectID="ME-427909"/><cge:TPSR_Ref TObjectID="75874"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1572.91,-607.089)" width="22" x="-1" y="-30"/></g>
    <g id="427909" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1571.91" xlink:href="#Disconnector:shape3_0" y="-554.089"/>
      <metadata><cge:PSR_Ref ObjectID="75875" ObjectName="RuiHZ.933XC1"/>
        <cge:Meas_Ref ObjectID="ME-427909"/><cge:TPSR_Ref TObjectID="75875"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1571.91" y="-554.089"/></g>
    <g id="427594" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,736.795,-601.775)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75846" ObjectName="RuiHZ.926XC"/>
        <cge:Meas_Ref ObjectID="ME-427594"/><cge:TPSR_Ref TObjectID="75846"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,736.795,-601.775)" width="22" x="-1" y="-30"/></g>
    <g id="427594" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="735.795" xlink:href="#Disconnector:shape3_0" y="-548.775"/>
      <metadata><cge:PSR_Ref ObjectID="75847" ObjectName="RuiHZ.926XC1"/>
        <cge:Meas_Ref ObjectID="ME-427594"/><cge:TPSR_Ref TObjectID="75847"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="735.795" y="-548.775"/></g>
    <g id="427595" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,759.741,-495.87)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75848" ObjectName="RuiHZ.92638SW"/>
        <cge:Meas_Ref ObjectID="ME-427595"/><cge:TPSR_Ref TObjectID="75848"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,759.741,-495.87)" width="60" x="-1" y="-23"/></g>
    <g id="427639" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,842.704,-603.577)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75850" ObjectName="RuiHZ.927XC"/>
        <cge:Meas_Ref ObjectID="ME-427639"/><cge:TPSR_Ref TObjectID="75850"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,842.704,-603.577)" width="22" x="-1" y="-30"/></g>
    <g id="427639" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="841.704" xlink:href="#Disconnector:shape3_0" y="-550.577"/>
      <metadata><cge:PSR_Ref ObjectID="75851" ObjectName="RuiHZ.927XC1"/>
        <cge:Meas_Ref ObjectID="ME-427639"/><cge:TPSR_Ref TObjectID="75851"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="841.704" y="-550.577"/></g>
    <g id="427640" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,865.65,-497.672)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75852" ObjectName="RuiHZ.92738SW"/>
        <cge:Meas_Ref ObjectID="ME-427640"/><cge:TPSR_Ref TObjectID="75852"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,865.65,-497.672)" width="60" x="-1" y="-23"/></g>
    <g id="427684" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,934.704,-602.577)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75854" ObjectName="RuiHZ.928XC"/>
        <cge:Meas_Ref ObjectID="ME-427684"/><cge:TPSR_Ref TObjectID="75854"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,934.704,-602.577)" width="22" x="-1" y="-30"/></g>
    <g id="427684" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="933.704" xlink:href="#Disconnector:shape3_0" y="-549.577"/>
      <metadata><cge:PSR_Ref ObjectID="75855" ObjectName="RuiHZ.928XC1"/>
        <cge:Meas_Ref ObjectID="ME-427684"/><cge:TPSR_Ref TObjectID="75855"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="933.704" y="-549.577"/></g>
    <g id="426735" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1881.87,-600.133)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75766" ObjectName="RuiHZ.902XC"/>
        <cge:Meas_Ref ObjectID="ME-426735"/><cge:TPSR_Ref TObjectID="75766"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1881.87,-600.133)" width="22" x="-1" y="-30"/></g>
    <g id="426735" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1880.87" xlink:href="#Disconnector:shape3_0" y="-545.133"/>
      <metadata><cge:PSR_Ref ObjectID="75767" ObjectName="RuiHZ.902XC1"/>
        <cge:Meas_Ref ObjectID="ME-426735"/><cge:TPSR_Ref TObjectID="75767"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1880.87" y="-545.133"/></g>
    <g id="427910" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1595.86,-501.184)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75876" ObjectName="RuiHZ.93338SW"/>
        <cge:Meas_Ref ObjectID="ME-427910"/><cge:TPSR_Ref TObjectID="75876"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1595.86,-501.184)" width="60" x="-1" y="-23"/></g>
    <g id="427504" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2496.88,-1242.07)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75838" ObjectName="RuiHZ.924XC"/>
        <cge:Meas_Ref ObjectID="ME-427504"/><cge:TPSR_Ref TObjectID="75838"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2496.88,-1242.07)" width="22" x="-1" y="-30"/></g>
    <g id="427504" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2495.88" xlink:href="#Disconnector:shape3_0" y="-1187.07"/>
      <metadata><cge:PSR_Ref ObjectID="75839" ObjectName="RuiHZ.924XC1"/>
        <cge:Meas_Ref ObjectID="ME-427504"/><cge:TPSR_Ref TObjectID="75839"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2495.88" y="-1187.07"/></g>
    <g id="427505" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2519.83,-1135.77)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75840" ObjectName="RuiHZ.92438SW"/>
        <cge:Meas_Ref ObjectID="ME-427505"/><cge:TPSR_Ref TObjectID="75840"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2519.83,-1135.77)" width="60" x="-1" y="-23"/></g>
    <g id="427549" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2598.74,-1243.07)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75842" ObjectName="RuiHZ.925XC"/>
        <cge:Meas_Ref ObjectID="ME-427549"/><cge:TPSR_Ref TObjectID="75842"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2598.74,-1243.07)" width="22" x="-1" y="-30"/></g>
    <g id="427549" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2597.74" xlink:href="#Disconnector:shape3_0" y="-1188.07"/>
      <metadata><cge:PSR_Ref ObjectID="75843" ObjectName="RuiHZ.925XC1"/>
        <cge:Meas_Ref ObjectID="ME-427549"/><cge:TPSR_Ref TObjectID="75843"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2597.74" y="-1188.07"/></g>
    <g id="427550" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2621.69,-1136.77)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75844" ObjectName="RuiHZ.92538SW"/>
        <cge:Meas_Ref ObjectID="ME-427550"/><cge:TPSR_Ref TObjectID="75844"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2621.69,-1136.77)" width="60" x="-1" y="-23"/></g>
    <g id="428436" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1702.52,-1239.66)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75956" ObjectName="RuiHZ.991XC"/>
        <cge:Meas_Ref ObjectID="ME-428436"/><cge:TPSR_Ref TObjectID="75956"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1702.52,-1239.66)" width="22" x="-1" y="-30"/></g>
    <g id="428436" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1701.52" xlink:href="#Disconnector:shape3_0" y="-1186.66"/>
      <metadata><cge:PSR_Ref ObjectID="75957" ObjectName="RuiHZ.991XC1"/>
        <cge:Meas_Ref ObjectID="ME-428436"/><cge:TPSR_Ref TObjectID="75957"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1701.52" y="-1186.66"/></g>
    <g id="428437" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1725.47,-1133.36)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75958" ObjectName="RuiHZ.99138SW"/>
        <cge:Meas_Ref ObjectID="ME-428437"/><cge:TPSR_Ref TObjectID="75958"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1725.47,-1133.36)" width="60" x="-1" y="-23"/></g>
    <g id="428338" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2729.9,-1247.67)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75929" ObjectName="RuiHZ.90011XC"/>
        <cge:Meas_Ref ObjectID="ME-428338"/><cge:TPSR_Ref TObjectID="75929"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2729.9,-1247.67)" width="22" x="-1" y="-30"/></g>
    <g id="428338" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2728.9" xlink:href="#Disconnector:shape3_0" y="-1182.84"/>
      <metadata><cge:PSR_Ref ObjectID="75930" ObjectName="RuiHZ.90011XC1"/>
        <cge:Meas_Ref ObjectID="ME-428338"/><cge:TPSR_Ref TObjectID="75930"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2728.9" y="-1182.84"/></g>
    <g id="428337" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,469.166,-611.342)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75927" ObjectName="RuiHZ.9001XC"/>
        <cge:Meas_Ref ObjectID="ME-428337"/><cge:TPSR_Ref TObjectID="75927"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,469.166,-611.342)" width="22" x="-1" y="-30"/></g>
    <g id="428337" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="468.166" xlink:href="#Disconnector:shape3_0" y="-551.342"/>
      <metadata><cge:PSR_Ref ObjectID="75928" ObjectName="RuiHZ.9001XC1"/>
        <cge:Meas_Ref ObjectID="ME-428337"/><cge:TPSR_Ref TObjectID="75928"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="468.166" y="-551.342"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="16" transform="matrix(1.11111,0,0,1,1593.81,-1596.6)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1593.81,-1596.6)" width="45" x="1" y="-15"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="60" transform="matrix(0.666667,0,0,0.95082,1642.77,-1478.18)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1642.77,-1478.18)" width="25" x="3" y="-100"/></g>
    <g id="428237" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,727.345,-1444.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75906" ObjectName="RuiHZ.01517SW"/>
        <cge:Meas_Ref ObjectID="ME-428237"/><cge:TPSR_Ref TObjectID="75906"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,727.345,-1444.92)" width="25" x="3" y="-100"/></g>
    <g id="428235" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,749.794,-1569.15)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75905" ObjectName="RuiHZ.0151SW"/>
        <cge:Meas_Ref ObjectID="ME-428235"/><cge:TPSR_Ref TObjectID="75905"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,749.794,-1569.15)" width="45" x="1" y="-15"/></g>
    <g id="428245" primType="switch">
      <use class="kV110" height="46.0268" transform="matrix(0.666667,0,0,0.95082,1376.82,-1459.89)" width="16.1497" x="11.8503" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="75908" ObjectName="RuiHZ.01527SW"/>
        <cge:Meas_Ref ObjectID="ME-428245"/><cge:TPSR_Ref TObjectID="75908"/></metadata>
    <rect fill="white" height="46.0268" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1376.82,-1459.89)" width="16.1497" x="11.8503" y="-100"/></g>
    <g id="428243" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,1299.44,-1566.16)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="75907" ObjectName="RuiHZ.0152SW"/>
        <cge:Meas_Ref ObjectID="ME-428243"/><cge:TPSR_Ref TObjectID="75907"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1299.44,-1566.16)" width="45" x="1" y="-15"/></g>
    <g id="428374" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,757.783,-1249.28)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75938" ObjectName="RuiHZ.C02XC"/>
        <cge:Meas_Ref ObjectID="ME-428374"/><cge:TPSR_Ref TObjectID="75938"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,757.783,-1249.28)" width="22" x="-1" y="-30"/></g>
    <g id="428374" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="756.783" xlink:href="#Disconnector:shape3_0" y="-1196.28"/>
      <metadata><cge:PSR_Ref ObjectID="75939" ObjectName="RuiHZ.C02XC1"/>
        <cge:Meas_Ref ObjectID="ME-428374"/><cge:TPSR_Ref TObjectID="75939"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="756.783" y="-1196.28"/></g>
    <g id="428375" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,780.729,-1143.98)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75940" ObjectName="RuiHZ.C0268SW"/>
        <cge:Meas_Ref ObjectID="ME-428375"/><cge:TPSR_Ref TObjectID="75940"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,780.729,-1143.98)" width="60" x="-1" y="-23"/></g>
    <g id="428376" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,752.97,-1072.28)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75941" ObjectName="RuiHZ.C026SW"/>
        <cge:Meas_Ref ObjectID="ME-428376"/><cge:TPSR_Ref TObjectID="75941"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,752.97,-1072.28)" width="16" x="5" y="-63"/></g>
    <g id="428378" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,792.43,-1078.99)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75942" ObjectName="RuiHZ.C0267SW"/>
        <cge:Meas_Ref ObjectID="ME-428378"/><cge:TPSR_Ref TObjectID="75942"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,792.43,-1078.99)" width="60" x="-1" y="-23"/></g>
    <g id="428394" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,612.169,-597.98)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75944" ObjectName="RuiHZ.C03XC"/>
        <cge:Meas_Ref ObjectID="ME-428394"/><cge:TPSR_Ref TObjectID="75944"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,612.169,-597.98)" width="22" x="-1" y="-30"/></g>
    <g id="428394" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="611.169" xlink:href="#Disconnector:shape3_0" y="-544.98"/>
      <metadata><cge:PSR_Ref ObjectID="75945" ObjectName="RuiHZ.C03XC1"/>
        <cge:Meas_Ref ObjectID="ME-428394"/><cge:TPSR_Ref TObjectID="75945"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="611.169" y="-544.98"/></g>
    <g id="428395" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,635.115,-492.681)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75946" ObjectName="RuiHZ.C0368SW"/>
        <cge:Meas_Ref ObjectID="ME-428395"/><cge:TPSR_Ref TObjectID="75946"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,635.115,-492.681)" width="60" x="-1" y="-23"/></g>
    <g id="428396" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,607.356,-420.98)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75947" ObjectName="RuiHZ.C036SW"/>
        <cge:Meas_Ref ObjectID="ME-428396"/><cge:TPSR_Ref TObjectID="75947"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,607.356,-420.98)" width="16" x="5" y="-63"/></g>
    <g id="428398" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,646.816,-427.699)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75948" ObjectName="RuiHZ.C0367SW"/>
        <cge:Meas_Ref ObjectID="ME-428398"/><cge:TPSR_Ref TObjectID="75948"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,646.816,-427.699)" width="60" x="-1" y="-23"/></g>
    <g id="427685" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,957.65,-496.672)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75856" ObjectName="RuiHZ.92838SW"/>
        <cge:Meas_Ref ObjectID="ME-427685"/><cge:TPSR_Ref TObjectID="75856"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,957.65,-496.672)" width="60" x="-1" y="-23"/></g>
    <g id="427864" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1334.28,-597.508)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75870" ObjectName="RuiHZ.932XC"/>
        <cge:Meas_Ref ObjectID="ME-427864"/><cge:TPSR_Ref TObjectID="75870"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1334.28,-597.508)" width="22" x="-1" y="-30"/></g>
    <g id="427864" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1333.28" xlink:href="#Disconnector:shape3_0" y="-544.508"/>
      <metadata><cge:PSR_Ref ObjectID="75871" ObjectName="RuiHZ.932XC1"/>
        <cge:Meas_Ref ObjectID="ME-427864"/><cge:TPSR_Ref TObjectID="75871"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1333.28" y="-544.508"/></g>
    <g id="427865" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1357.23,-491.604)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75872" ObjectName="RuiHZ.93238SW"/>
        <cge:Meas_Ref ObjectID="ME-427865"/><cge:TPSR_Ref TObjectID="75872"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1357.23,-491.604)" width="60" x="-1" y="-23"/></g>
    <g id="427774" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1138.52,-599.374)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75862" ObjectName="RuiHZ.930XC"/>
        <cge:Meas_Ref ObjectID="ME-427774"/><cge:TPSR_Ref TObjectID="75862"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1138.52,-599.374)" width="22" x="-1" y="-30"/></g>
    <g id="427774" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1137.52" xlink:href="#Disconnector:shape3_0" y="-546.374"/>
      <metadata><cge:PSR_Ref ObjectID="75863" ObjectName="RuiHZ.930XC1"/>
        <cge:Meas_Ref ObjectID="ME-427774"/><cge:TPSR_Ref TObjectID="75863"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1137.52" y="-546.374"/></g>
    <g id="427819" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1230.52,-598.374)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75866" ObjectName="RuiHZ.931XC"/>
        <cge:Meas_Ref ObjectID="ME-427819"/><cge:TPSR_Ref TObjectID="75866"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1230.52,-598.374)" width="22" x="-1" y="-30"/></g>
    <g id="427819" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1229.52" xlink:href="#Disconnector:shape3_0" y="-545.374"/>
      <metadata><cge:PSR_Ref ObjectID="75867" ObjectName="RuiHZ.931XC1"/>
        <cge:Meas_Ref ObjectID="ME-427819"/><cge:TPSR_Ref TObjectID="75867"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1229.52" y="-545.374"/></g>
    <g id="427775" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1161.47,-493.469)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75864" ObjectName="RuiHZ.93038SW"/>
        <cge:Meas_Ref ObjectID="ME-427775"/><cge:TPSR_Ref TObjectID="75864"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1161.47,-493.469)" width="60" x="-1" y="-23"/></g>
    <g id="427820" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1253.47,-492.469)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75868" ObjectName="RuiHZ.93138SW"/>
        <cge:Meas_Ref ObjectID="ME-427820"/><cge:TPSR_Ref TObjectID="75868"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1253.47,-492.469)" width="60" x="-1" y="-23"/></g>
    <g id="428454" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1457.85,-591.419)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75960" ObjectName="RuiHZ.992XC"/>
        <cge:Meas_Ref ObjectID="ME-428454"/><cge:TPSR_Ref TObjectID="75960"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1457.85,-591.419)" width="22" x="-1" y="-30"/></g>
    <g id="428454" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1456.85" xlink:href="#Disconnector:shape3_0" y="-538.419"/>
      <metadata><cge:PSR_Ref ObjectID="75961" ObjectName="RuiHZ.992XC1"/>
        <cge:Meas_Ref ObjectID="ME-428454"/><cge:TPSR_Ref TObjectID="75961"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1456.85" y="-538.419"/></g>
    <g id="428455" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1480.79,-485.121)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75962" ObjectName="RuiHZ.99238SW"/>
        <cge:Meas_Ref ObjectID="ME-428455"/><cge:TPSR_Ref TObjectID="75962"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1480.79,-485.121)" width="60" x="-1" y="-23"/></g>
    <g id="428307" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1700.43,-589.74)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75918" ObjectName="RuiHZ.0952XC"/>
        <cge:Meas_Ref ObjectID="ME-428307"/><cge:TPSR_Ref TObjectID="75918"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1700.43,-589.74)" width="22" x="-1" y="-30"/></g>
    <g id="428307" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1699.43" xlink:href="#Disconnector:shape3_0" y="-531.74"/>
      <metadata><cge:PSR_Ref ObjectID="75919" ObjectName="RuiHZ.0952XC1"/>
        <cge:Meas_Ref ObjectID="ME-428307"/><cge:TPSR_Ref TObjectID="75919"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1699.43" y="-531.74"/></g>
    <g id="428089" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2563.51,-597.197)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75890" ObjectName="RuiHZ.937XC"/>
        <cge:Meas_Ref ObjectID="ME-428089"/><cge:TPSR_Ref TObjectID="75890"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2563.51,-597.197)" width="22" x="-1" y="-30"/></g>
    <g id="428089" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2562.51" xlink:href="#Disconnector:shape3_0" y="-544.197"/>
      <metadata><cge:PSR_Ref ObjectID="75891" ObjectName="RuiHZ.937XC1"/>
        <cge:Meas_Ref ObjectID="ME-428089"/><cge:TPSR_Ref TObjectID="75891"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2562.51" y="-544.197"/></g>
    <g id="428090" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2586.45,-491.292)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75892" ObjectName="RuiHZ.93738SW"/>
        <cge:Meas_Ref ObjectID="ME-428090"/><cge:TPSR_Ref TObjectID="75892"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2586.45,-491.292)" width="60" x="-1" y="-23"/></g>
    <g id="427954" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2261.84,-597.261)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75878" ObjectName="RuiHZ.934XC"/>
        <cge:Meas_Ref ObjectID="ME-427954"/><cge:TPSR_Ref TObjectID="75878"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2261.84,-597.261)" width="22" x="-1" y="-30"/></g>
    <g id="427954" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2260.84" xlink:href="#Disconnector:shape3_0" y="-544.261"/>
      <metadata><cge:PSR_Ref ObjectID="75879" ObjectName="RuiHZ.934XC1"/>
        <cge:Meas_Ref ObjectID="ME-427954"/><cge:TPSR_Ref TObjectID="75879"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2260.84" y="-544.261"/></g>
    <g id="427955" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2284.78,-491.357)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75880" ObjectName="RuiHZ.93438SW"/>
        <cge:Meas_Ref ObjectID="ME-427955"/><cge:TPSR_Ref TObjectID="75880"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2284.78,-491.357)" width="60" x="-1" y="-23"/></g>
    <g id="427999" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2367.75,-599.063)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75882" ObjectName="RuiHZ.935XC"/>
        <cge:Meas_Ref ObjectID="ME-427999"/><cge:TPSR_Ref TObjectID="75882"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2367.75,-599.063)" width="22" x="-1" y="-30"/></g>
    <g id="427999" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2366.75" xlink:href="#Disconnector:shape3_0" y="-546.063"/>
      <metadata><cge:PSR_Ref ObjectID="75883" ObjectName="RuiHZ.935XC1"/>
        <cge:Meas_Ref ObjectID="ME-427999"/><cge:TPSR_Ref TObjectID="75883"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2366.75" y="-546.063"/></g>
    <g id="428000" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2390.69,-493.158)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75884" ObjectName="RuiHZ.93538SW"/>
        <cge:Meas_Ref ObjectID="ME-428000"/><cge:TPSR_Ref TObjectID="75884"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2390.69,-493.158)" width="60" x="-1" y="-23"/></g>
    <g id="428044" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2459.75,-598.063)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75886" ObjectName="RuiHZ.936XC"/>
        <cge:Meas_Ref ObjectID="ME-428044"/><cge:TPSR_Ref TObjectID="75886"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2459.75,-598.063)" width="22" x="-1" y="-30"/></g>
    <g id="428044" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2458.75" xlink:href="#Disconnector:shape3_0" y="-545.063"/>
      <metadata><cge:PSR_Ref ObjectID="75887" ObjectName="RuiHZ.936XC1"/>
        <cge:Meas_Ref ObjectID="ME-428044"/><cge:TPSR_Ref TObjectID="75887"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2458.75" y="-545.063"/></g>
    <g id="426757" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2120.25,-611.496)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75769" ObjectName="RuiHZ.905XC"/>
        <cge:Meas_Ref ObjectID="ME-426757"/><cge:TPSR_Ref TObjectID="75769"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2120.25,-611.496)" width="22" x="-1" y="-30"/></g>
    <g id="426757" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2119.25" xlink:href="#Disconnector:shape3_0" y="-551.496"/>
      <metadata><cge:PSR_Ref ObjectID="75770" ObjectName="RuiHZ.905XC1"/>
        <cge:Meas_Ref ObjectID="ME-426757"/><cge:TPSR_Ref TObjectID="75770"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2119.25" y="-551.496"/></g>
    <g id="428415" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,3116.34,-588.066)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75950" ObjectName="RuiHZ.C04XC"/>
        <cge:Meas_Ref ObjectID="ME-428415"/><cge:TPSR_Ref TObjectID="75950"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,3116.34,-588.066)" width="22" x="-1" y="-30"/></g>
    <g id="428415" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="3115.34" xlink:href="#Disconnector:shape3_0" y="-535.066"/>
      <metadata><cge:PSR_Ref ObjectID="75951" ObjectName="RuiHZ.C04XC1"/>
        <cge:Meas_Ref ObjectID="ME-428415"/><cge:TPSR_Ref TObjectID="75951"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="3115.34" y="-535.066"/></g>
    <g id="428416" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,3139.28,-482.768)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75952" ObjectName="RuiHZ.C0468SW"/>
        <cge:Meas_Ref ObjectID="ME-428416"/><cge:TPSR_Ref TObjectID="75952"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,3139.28,-482.768)" width="60" x="-1" y="-23"/></g>
    <g id="428417" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,3111.53,-411.066)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="75953" ObjectName="RuiHZ.C046SW"/>
        <cge:Meas_Ref ObjectID="ME-428417"/><cge:TPSR_Ref TObjectID="75953"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,3111.53,-411.066)" width="16" x="5" y="-63"/></g>
    <g id="428419" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,3150.98,-417.786)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75954" ObjectName="RuiHZ.C0467SW"/>
        <cge:Meas_Ref ObjectID="ME-428419"/><cge:TPSR_Ref TObjectID="75954"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,3150.98,-417.786)" width="60" x="-1" y="-23"/></g>
    <g id="428045" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2482.69,-492.158)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75888" ObjectName="RuiHZ.93638SW"/>
        <cge:Meas_Ref ObjectID="ME-428045"/><cge:TPSR_Ref TObjectID="75888"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2482.69,-492.158)" width="60" x="-1" y="-23"/></g>
    <g id="428224" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2859.32,-592.994)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75902" ObjectName="RuiHZ.940XC"/>
        <cge:Meas_Ref ObjectID="ME-428224"/><cge:TPSR_Ref TObjectID="75902"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2859.32,-592.994)" width="22" x="-1" y="-30"/></g>
    <g id="428224" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2858.32" xlink:href="#Disconnector:shape3_0" y="-539.994"/>
      <metadata><cge:PSR_Ref ObjectID="75903" ObjectName="RuiHZ.940XC1"/>
        <cge:Meas_Ref ObjectID="ME-428224"/><cge:TPSR_Ref TObjectID="75903"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2858.32" y="-539.994"/></g>
    <g id="428225" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2882.27,-487.09)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75904" ObjectName="RuiHZ.94038SW"/>
        <cge:Meas_Ref ObjectID="ME-428225"/><cge:TPSR_Ref TObjectID="75904"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2882.27,-487.09)" width="60" x="-1" y="-23"/></g>
    <g id="428134" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2663.56,-594.86)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75894" ObjectName="RuiHZ.938XC"/>
        <cge:Meas_Ref ObjectID="ME-428134"/><cge:TPSR_Ref TObjectID="75894"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2663.56,-594.86)" width="22" x="-1" y="-30"/></g>
    <g id="428134" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2662.56" xlink:href="#Disconnector:shape3_0" y="-541.86"/>
      <metadata><cge:PSR_Ref ObjectID="75895" ObjectName="RuiHZ.938XC1"/>
        <cge:Meas_Ref ObjectID="ME-428134"/><cge:TPSR_Ref TObjectID="75895"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2662.56" y="-541.86"/></g>
    <g id="428135" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2686.51,-488.955)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75896" ObjectName="RuiHZ.93838SW"/>
        <cge:Meas_Ref ObjectID="ME-428135"/><cge:TPSR_Ref TObjectID="75896"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2686.51,-488.955)" width="60" x="-1" y="-23"/></g>
    <g id="428179" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2755.56,-593.86)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75898" ObjectName="RuiHZ.939XC"/>
        <cge:Meas_Ref ObjectID="ME-428179"/><cge:TPSR_Ref TObjectID="75898"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2755.56,-593.86)" width="22" x="-1" y="-30"/></g>
    <g id="428179" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2754.56" xlink:href="#Disconnector:shape3_0" y="-540.86"/>
      <metadata><cge:PSR_Ref ObjectID="75899" ObjectName="RuiHZ.939XC1"/>
        <cge:Meas_Ref ObjectID="ME-428179"/><cge:TPSR_Ref TObjectID="75899"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2754.56" y="-540.86"/></g>
    <g id="428180" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,2778.51,-487.955)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75900" ObjectName="RuiHZ.93938SW"/>
        <cge:Meas_Ref ObjectID="ME-428180"/><cge:TPSR_Ref TObjectID="75900"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,2778.51,-487.955)" width="60" x="-1" y="-23"/></g>
    <g id="428317" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,3364.69,-577.505)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75922" ObjectName="RuiHZ.0953XC"/>
        <cge:Meas_Ref ObjectID="ME-428317"/><cge:TPSR_Ref TObjectID="75922"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,3364.69,-577.505)" width="22" x="-1" y="-30"/></g>
    <g id="428317" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="3363.69" xlink:href="#Disconnector:shape3_0" y="-519.505"/>
      <metadata><cge:PSR_Ref ObjectID="75923" ObjectName="RuiHZ.0953XC1"/>
        <cge:Meas_Ref ObjectID="ME-428317"/><cge:TPSR_Ref TObjectID="75923"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="3363.69" y="-519.505"/></g>
    <g id="0" primType="xcswitch">
      <use class="BuDaiDian" height="30" transform="matrix(1,0,0,0.96875,2976.67,-592.908)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2976.67,-592.908)" width="22" x="-1" y="-30"/></g>
    <g id="0" primType="xcswitch">
      <use class="BuDaiDian" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2975.67" xlink:href="#Disconnector:shape3_0" y="-528.078"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2975.67" y="-528.078"/></g>
    <g id="428489" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,3240.42,-598.558)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75968" ObjectName="RuiHZ.994XC"/>
        <cge:Meas_Ref ObjectID="ME-428489"/><cge:TPSR_Ref TObjectID="75968"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,3240.42,-598.558)" width="22" x="-1" y="-30"/></g>
    <g id="428489" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="3239.42" xlink:href="#Disconnector:shape3_0" y="-545.558"/>
      <metadata><cge:PSR_Ref ObjectID="75969" ObjectName="RuiHZ.994XC1"/>
        <cge:Meas_Ref ObjectID="ME-428489"/><cge:TPSR_Ref TObjectID="75969"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="3239.42" y="-545.558"/></g>
    <g id="428490" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,3263.37,-492.26)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="75970" ObjectName="RuiHZ.99438SW"/>
        <cge:Meas_Ref ObjectID="ME-428490"/><cge:TPSR_Ref TObjectID="75970"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,3263.37,-492.26)" width="60" x="-1" y="-23"/></g>
    <g id="428297" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1812.15,-1237.3)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="75914" ObjectName="RuiHZ.0951XC"/>
        <cge:Meas_Ref ObjectID="ME-428297"/><cge:TPSR_Ref TObjectID="75914"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1812.15,-1237.3)" width="22" x="-1" y="-30"/></g>
    <g id="428297" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1811.15" xlink:href="#Disconnector:shape3_0" y="-1179.3"/>
      <metadata><cge:PSR_Ref ObjectID="75915" ObjectName="RuiHZ.0951XC1"/>
        <cge:Meas_Ref ObjectID="ME-428297"/><cge:TPSR_Ref TObjectID="75915"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1811.15" y="-1179.3"/></g>
  </g>
  <g id="DynamicPointClass">
    <g id="0" primType="dynp_usual">
      <use height="62" transform="matrix(0.807692,0,0,0.983051,52.4145,-1567.28)" width="237" x="-2" xlink:href="#DynamicPoint:shape48_0" y="-61"/>
    <metadata/><rect fill="white" height="62" opacity="0" stroke="white" transform="matrix(0.807692,0,0,0.983051,52.4145,-1567.28)" width="237" x="-2" y="-61"/></g>
    <g id="401" primType="dynp_station">
      <use height="72.1282" transform="matrix(1,0,0,1,0,0)" width="139.104" x="81.7267" xlink:href="#DynamicPoint:shape47_0" y="-1633.07"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-401"/></metadata>
    <rect fill="white" height="72.1282" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="139.104" x="81.7267" y="-1633.07"/></g>
  </g>
  <g id="EnergyConsumerClass">
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="999.593" xlink:href="#EnergyConsumer:shape1_0" y="-1111.34"/>
      <metadata><cge:PSR_Ref ObjectID="75981" ObjectName="RuiHZ.911Ld"/><cge:TPSR_Ref TObjectID="75981"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="999.593" y="-1111.34"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1083.59" xlink:href="#EnergyConsumer:shape1_0" y="-1111.34"/>
      <metadata><cge:PSR_Ref ObjectID="75982" ObjectName="RuiHZ.912Ld"/><cge:TPSR_Ref TObjectID="75982"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1083.59" y="-1111.34"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1194.63" xlink:href="#EnergyConsumer:shape1_0" y="-1113.47"/>
      <metadata><cge:PSR_Ref ObjectID="75983" ObjectName="RuiHZ.913Ld"/><cge:TPSR_Ref TObjectID="75983"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1194.63" y="-1113.47"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1280.63" xlink:href="#EnergyConsumer:shape1_0" y="-1114.47"/>
      <metadata><cge:PSR_Ref ObjectID="75984" ObjectName="RuiHZ.914Ld"/><cge:TPSR_Ref TObjectID="75984"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1280.63" y="-1114.47"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1369.63" xlink:href="#EnergyConsumer:shape1_0" y="-1113.47"/>
      <metadata><cge:PSR_Ref ObjectID="75985" ObjectName="RuiHZ.915Ld"/><cge:TPSR_Ref TObjectID="75985"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1369.63" y="-1113.47"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1483.47" xlink:href="#EnergyConsumer:shape1_0" y="-1115.86"/>
      <metadata><cge:PSR_Ref ObjectID="75986" ObjectName="RuiHZ.916Ld"/><cge:TPSR_Ref TObjectID="75986"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1483.47" y="-1115.86"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1569.47" xlink:href="#EnergyConsumer:shape1_0" y="-1113.86"/>
      <metadata><cge:PSR_Ref ObjectID="75987" ObjectName="RuiHZ.917Ld"/><cge:TPSR_Ref TObjectID="75987"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1569.47" y="-1113.86"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="842.704" xlink:href="#EnergyConsumer:shape1_0" y="-474.577"/>
      <metadata><cge:PSR_Ref ObjectID="75997" ObjectName="RuiHZ.927Ld"/><cge:TPSR_Ref TObjectID="75997"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="842.704" y="-474.577"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="934.704" xlink:href="#EnergyConsumer:shape1_0" y="-473.577"/>
      <metadata><cge:PSR_Ref ObjectID="75998" ObjectName="RuiHZ.928Ld"/><cge:TPSR_Ref TObjectID="75998"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="934.704" y="-473.577"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1038.47" xlink:href="#EnergyConsumer:shape1_0" y="-472.711"/>
      <metadata><cge:PSR_Ref ObjectID="75999" ObjectName="RuiHZ.929Ld"/><cge:TPSR_Ref TObjectID="75999"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1038.47" y="-472.711"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1572.91" xlink:href="#EnergyConsumer:shape1_0" y="-478.089"/>
      <metadata><cge:PSR_Ref ObjectID="76003" ObjectName="RuiHZ.933Ld"/><cge:TPSR_Ref TObjectID="76003"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1572.91" y="-478.089"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1931.23" xlink:href="#EnergyConsumer:shape1_0" y="-1101.55"/>
      <metadata><cge:PSR_Ref ObjectID="75988" ObjectName="RuiHZ.918Ld"/><cge:TPSR_Ref TObjectID="75988"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1931.23" y="-1101.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2021.23" xlink:href="#EnergyConsumer:shape1_0" y="-1102.55"/>
      <metadata><cge:PSR_Ref ObjectID="75989" ObjectName="RuiHZ.919Ld"/><cge:TPSR_Ref TObjectID="75989"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2021.23" y="-1102.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2107.23" xlink:href="#EnergyConsumer:shape1_0" y="-1102.55"/>
      <metadata><cge:PSR_Ref ObjectID="75990" ObjectName="RuiHZ.920Ld"/><cge:TPSR_Ref TObjectID="75990"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2107.23" y="-1102.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2191.23" xlink:href="#EnergyConsumer:shape1_0" y="-1099.55"/>
      <metadata><cge:PSR_Ref ObjectID="75991" ObjectName="RuiHZ.921Ld"/><cge:TPSR_Ref TObjectID="75991"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2191.23" y="-1099.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2281.23" xlink:href="#EnergyConsumer:shape1_0" y="-1100.55"/>
      <metadata><cge:PSR_Ref ObjectID="75992" ObjectName="RuiHZ.922Ld"/><cge:TPSR_Ref TObjectID="75992"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2281.23" y="-1100.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2391.55" xlink:href="#EnergyConsumer:shape1_0" y="-1105.55"/>
      <metadata><cge:PSR_Ref ObjectID="75993" ObjectName="RuiHZ.923Ld"/><cge:TPSR_Ref TObjectID="75993"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2391.55" y="-1105.55"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="736.795" xlink:href="#EnergyConsumer:shape1_0" y="-472.775"/>
      <metadata><cge:PSR_Ref ObjectID="75996" ObjectName="RuiHZ.926Ld"/><cge:TPSR_Ref TObjectID="75996"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="736.795" y="-472.775"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2496.88" xlink:href="#EnergyConsumer:shape1_0" y="-1113.07"/>
      <metadata><cge:PSR_Ref ObjectID="75994" ObjectName="RuiHZ.924Ld"/><cge:TPSR_Ref TObjectID="75994"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2496.88" y="-1113.07"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2598.74" xlink:href="#EnergyConsumer:shape1_0" y="-1114.07"/>
      <metadata><cge:PSR_Ref ObjectID="75995" ObjectName="RuiHZ.925Ld"/><cge:TPSR_Ref TObjectID="75995"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2598.74" y="-1114.07"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1138.52" xlink:href="#EnergyConsumer:shape1_0" y="-470.374"/>
      <metadata><cge:PSR_Ref ObjectID="76000" ObjectName="RuiHZ.930Ld"/><cge:TPSR_Ref TObjectID="76000"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1138.52" y="-470.374"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1230.52" xlink:href="#EnergyConsumer:shape1_0" y="-469.374"/>
      <metadata><cge:PSR_Ref ObjectID="76001" ObjectName="RuiHZ.931Ld"/><cge:TPSR_Ref TObjectID="76001"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1230.52" y="-469.374"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1334.28" xlink:href="#EnergyConsumer:shape1_0" y="-468.508"/>
      <metadata><cge:PSR_Ref ObjectID="76002" ObjectName="RuiHZ.932Ld"/><cge:TPSR_Ref TObjectID="76002"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1334.28" y="-468.508"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2367.75" xlink:href="#EnergyConsumer:shape1_0" y="-470.063"/>
      <metadata><cge:PSR_Ref ObjectID="76005" ObjectName="RuiHZ.935Ld"/><cge:TPSR_Ref TObjectID="76005"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2367.75" y="-470.063"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2459.75" xlink:href="#EnergyConsumer:shape1_0" y="-469.063"/>
      <metadata><cge:PSR_Ref ObjectID="76006" ObjectName="RuiHZ.936Ld"/><cge:TPSR_Ref TObjectID="76006"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2459.75" y="-469.063"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2563.51" xlink:href="#EnergyConsumer:shape1_0" y="-468.197"/>
      <metadata><cge:PSR_Ref ObjectID="76007" ObjectName="RuiHZ.937Ld"/><cge:TPSR_Ref TObjectID="76007"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2563.51" y="-468.197"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2261.84" xlink:href="#EnergyConsumer:shape1_0" y="-468.261"/>
      <metadata><cge:PSR_Ref ObjectID="76004" ObjectName="RuiHZ.934Ld"/><cge:TPSR_Ref TObjectID="76004"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2261.84" y="-468.261"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2663.56" xlink:href="#EnergyConsumer:shape1_0" y="-465.86"/>
      <metadata><cge:PSR_Ref ObjectID="76008" ObjectName="RuiHZ.938Ld"/><cge:TPSR_Ref TObjectID="76008"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2663.56" y="-465.86"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2755.56" xlink:href="#EnergyConsumer:shape1_0" y="-464.86"/>
      <metadata><cge:PSR_Ref ObjectID="76009" ObjectName="RuiHZ.939Ld"/><cge:TPSR_Ref TObjectID="76009"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2755.56" y="-464.86"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2859.32" xlink:href="#EnergyConsumer:shape1_0" y="-463.994"/>
      <metadata><cge:PSR_Ref ObjectID="76010" ObjectName="RuiHZ.940Ld"/><cge:TPSR_Ref TObjectID="76010"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2859.32" y="-463.994"/></g>
  </g>
  <g id="CapacitorClass">
    <g id="DEV-0">
      <use class="kV10" height="11.0625" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="591.17" xlink:href="#Capacitor:shape0_0" y="-1020.88"/>
      <metadata><cge:PSR_Ref ObjectID="76072" ObjectName="RuiHZ.C01"/><cge:TPSR_Ref TObjectID="76072"/></metadata>
    <rect fill="white" height="11.0625" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="591.17" y="-1020.88"/></g>
    <g id="DEV-0">
      <use class="kV10" height="11.0625" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="761.2" xlink:href="#Capacitor:shape0_0" y="-1016.01"/>
      <metadata><cge:PSR_Ref ObjectID="76073" ObjectName="RuiHZ.C02"/><cge:TPSR_Ref TObjectID="76073"/></metadata>
    <rect fill="white" height="11.0625" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="761.2" y="-1016.01"/></g>
    <g id="DEV-0">
      <use class="kV10" height="11.0625" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="614.333" xlink:href="#Capacitor:shape0_0" y="-365.828"/>
      <metadata><cge:PSR_Ref ObjectID="76074" ObjectName="RuiHZ.C03"/><cge:TPSR_Ref TObjectID="76074"/></metadata>
    <rect fill="white" height="11.0625" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="614.333" y="-365.828"/></g>
    <g id="DEV-0">
      <use class="kV10" height="11.0625" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="3118.67" xlink:href="#Capacitor:shape0_0" y="-356.695"/>
      <metadata><cge:PSR_Ref ObjectID="76075" ObjectName="RuiHZ.C04"/><cge:TPSR_Ref TObjectID="76075"/></metadata>
    <rect fill="white" height="11.0625" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1327" x="3118.67" y="-356.695"/></g>
  </g>
  <g id="GroundLineClass">
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="712" xlink:href="#GroundLine:shape0_0" y="-1379"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="712" y="-1379"/></g>
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="1159" xlink:href="#GroundLine:shape0_0" y="-1373"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1159" y="-1373"/></g>
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="1659.56" xlink:href="#GroundLine:shape0_0" y="-1078.9"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1659.56" y="-1078.9"/></g>
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="1414.88" xlink:href="#GroundLine:shape0_0" y="-430.652"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1414.88" y="-430.652"/></g>
  </g>
  <g id="ArresterClass">
    <g id="DEV-0">
      <use class="kV10" height="42" transform="matrix(1,0,0,1,0,0)" width="28" x="1741.58" xlink:href="#Arrester:配电柱上避雷器（左向图符）_0_0" y="-498.702"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="28" x="1741.58" y="-498.702"/></g>
    <g id="DEV-0">
      <use class="kV10" height="42" transform="matrix(1,0,0,1,0,0)" width="28" x="3405.84" xlink:href="#Arrester:配电柱上避雷器（左向图符）_0_0" y="-486.466"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="28" x="3405.84" y="-486.466"/></g>
    <g id="DEV-0">
      <use class="kV10" height="42" transform="matrix(1,0,0,1,0,0)" width="28" x="1853.29" xlink:href="#Arrester:配电柱上避雷器（左向图符）_0_0" y="-1146.26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="28" x="1853.29" y="-1146.26"/></g>
  </g>
  <g id="OtherClass">
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,477.654,-1058.46)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,477.654,-1058.46)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,1139,-1745.3)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,1139,-1745.3)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,1180,-1723.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,1180,-1723.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="18" transform="matrix(1,0,0,1,0,0)" width="52" x="836" xlink:href="#Other:shape39_0" y="-1368"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="18" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="52" x="836" y="-1368"/></g>
    <g id="DEV-0">
      <use class="kV10" height="51" transform="matrix(1,0,0,1,0,0)" width="17" x="1475.8" xlink:href="#Other:shape37_0" y="-1428.88"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="51" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17" x="1475.8" y="-1428.88"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,1194,-1366.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,1194,-1366.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,744,-1365.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,744,-1365.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,646,-1747.3)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,646,-1747.3)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,687,-1725.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,687,-1725.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="42" transform="matrix(1,0,0,1,0,0)" width="18" x="715" xlink:href="#Other:shape56_0" y="-1425"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="18" x="715" y="-1425"/></g>
    <g id="DEV-0">
      <use class="kV110" height="42" transform="matrix(1,0,0,1,0,0)" width="18" x="1162" xlink:href="#Other:shape56_0" y="-1422"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="18" x="1162" y="-1422"/></g>
    <g id="DEV-0">
      <use class="kV10" height="32.8908" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="469.604" xlink:href="#Other:shape10_0" y="-1156.2"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="32.8908" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="469.604" y="-1156.2"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,689.514,-1506.85)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,689.514,-1506.85)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,1416.16,-1535.19)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,1416.16,-1535.19)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV10" height="50.9697" transform="matrix(1,0,0,1,0,0)" width="27.3496" x="1698.85" xlink:href="#Other:shape9_0" y="-1108.39"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="50.9697" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="27.3496" x="1698.85" y="-1108.39"/></g>
    <g id="DEV-0">
      <use class="kV10" height="33.0561" transform="matrix(1,0,0,1,0,0)" width="16.1608" x="1704.15" xlink:href="#Other:shape57_0" y="-1046.77"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="33.0561" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1608" x="1704.15" y="-1046.77"/></g>
    <g id="DEV-0">
      <use class="kV10" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="610.122" xlink:href="#Other:shape52_0" y="-316.712"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="610.122" y="-316.712"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="609.169" xlink:href="#Other:shape51_0" y="-424.98"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="609.169" y="-424.98"/></g>
    <g id="DEV-0">
      <use class="kV10" height="52" transform="matrix(0,0.981132,-1,0,544.475,-372.933)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(0,0.981132,-1,0,544.475,-372.933)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="23.1341" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="616.771" xlink:href="#Other:shape4_0" y="-353.914"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="23.1341" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="616.771" y="-353.914"/></g>
    <g id="DEV-0">
      <use class="kV10" height="50.9697" transform="matrix(1,0,0,1,0,0)" width="27.3496" x="1454.17" xlink:href="#Other:shape9_0" y="-460.149"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="50.9697" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="27.3496" x="1454.17" y="-460.149"/></g>
    <g id="DEV-0">
      <use class="kV10" height="33.0561" transform="matrix(1,0,0,1,0,0)" width="16.1608" x="1459.47" xlink:href="#Other:shape57_0" y="-398.531"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="33.0561" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.1608" x="1459.47" y="-398.531"/></g>
    <g id="DEV-0">
      <use class="kV10" height="44" transform="matrix(1,0,0,1,0,0)" width="20" x="1700.56" xlink:href="#Other:shape4_0" y="-492.595"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="44" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1700.56" y="-492.595"/></g>
    <g id="DEV-0">
      <use class="kV10" height="38" transform="matrix(1,0,0,-1,1689.7,-440.03)" width="47" x="-1" xlink:href="#Other:shape59_0" y="-39"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="38" opacity="0" stroke="white" transform="matrix(1,0,0,-1,1689.7,-440.03)" width="47" x="-1" y="-39"/></g>
    <g id="DEV-0">
      <use class="kV10" height="44" transform="matrix(1,0,0,1,0,0)" width="20" x="3364.83" xlink:href="#Other:shape4_0" y="-480.36"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="44" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="3364.83" y="-480.36"/></g>
    <g id="DEV-0">
      <use class="kV10" height="38" transform="matrix(1,0,0,-1,3353.97,-427.794)" width="47" x="-1" xlink:href="#Other:shape59_0" y="-39"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="38" opacity="0" stroke="white" transform="matrix(1,0,0,-1,3353.97,-427.794)" width="47" x="-1" y="-39"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,3243.07,-405.014)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,3243.07,-405.014)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="32.8908" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="3235.02" xlink:href="#Other:shape10_0" y="-502.758"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="32.8908" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="3235.02" y="-502.758"/></g>
    <g id="DEV-0">
      <use class="kV10" height="44" transform="matrix(1,0,0,1,0,0)" width="20" x="1812.28" xlink:href="#Other:shape4_0" y="-1140.16"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="44" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1812.28" y="-1140.16"/></g>
    <g id="DEV-0">
      <use class="kV10" height="38" transform="matrix(1,0,0,-1,1801.42,-1087.59)" width="47" x="-1" xlink:href="#Other:shape59_0" y="-39"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="38" opacity="0" stroke="white" transform="matrix(1,0,0,-1,1801.42,-1087.59)" width="47" x="-1" y="-39"/></g>
    <g id="DEV-0">
      <use class="kV10" height="35.9717" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="594.266" xlink:href="#Other:shape14_0" y="-353.674"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="35.9717" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="594.266" y="-353.674"/></g>
    <g id="DEV-0">
      <use class="kV10" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="756.989" xlink:href="#Other:shape52_0" y="-966.891"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="756.989" y="-966.891"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="756.036" xlink:href="#Other:shape51_0" y="-1075.16"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="756.036" y="-1075.16"/></g>
    <g id="DEV-0">
      <use class="kV10" height="52" transform="matrix(0,0.981132,-1,0,691.342,-1023.11)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(0,0.981132,-1,0,691.342,-1023.11)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="23.1341" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="763.638" xlink:href="#Other:shape4_0" y="-1004.09"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="23.1341" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="763.638" y="-1004.09"/></g>
    <g id="DEV-0">
      <use class="kV10" height="35.9717" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="741.133" xlink:href="#Other:shape14_0" y="-1003.85"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="35.9717" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="741.133" y="-1003.85"/></g>
    <g id="DEV-0">
      <use class="kV10" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="3114.45" xlink:href="#Other:shape52_0" y="-307.578"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="3114.45" y="-307.578"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="3113.5" xlink:href="#Other:shape51_0" y="-415.846"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="3113.5" y="-415.846"/></g>
    <g id="DEV-0">
      <use class="kV10" height="52" transform="matrix(0,0.981132,-1,0,3048.81,-363.799)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(0,0.981132,-1,0,3048.81,-363.799)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="23.1341" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="3121.1" xlink:href="#Other:shape4_0" y="-344.78"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="23.1341" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="3121.1" y="-344.78"/></g>
    <g id="DEV-0">
      <use class="kV10" height="35.9717" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="3098.6" xlink:href="#Other:shape14_0" y="-344.54"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="35.9717" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="3098.6" y="-344.54"/></g>
    <g id="DEV-0">
      <use class="kV10" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="586.958" xlink:href="#Other:shape52_0" y="-971.764"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="586.958" y="-971.764"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="586.005" xlink:href="#Other:shape51_0" y="-1080.03"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="586.005" y="-1080.03"/></g>
    <g id="DEV-0">
      <use class="kV10" height="52" transform="matrix(0,0.981132,-1,0,521.311,-1027.98)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(0,0.981132,-1,0,521.311,-1027.98)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="23.1341" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="593.608" xlink:href="#Other:shape4_0" y="-1008.97"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="23.1341" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10.5155" x="593.608" y="-1008.97"/></g>
    <g id="DEV-0">
      <use class="kV10" height="35.9717" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="571.102" xlink:href="#Other:shape14_0" y="-1008.73"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="35.9717" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="8.17538" x="571.102" y="-1008.73"/></g>
  </g>
  <g id="Base_MotifButtonClass">
    <g>
      
    <metadata/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="187.636,-1867.18 187.636,-1826.18 190.636,-1829.18 190.636,-1864.18 274.636,-1864.18 277.636,-1867.18" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="277.636,-1826.18 187.636,-1826.18 190.636,-1829.18 274.636,-1829.18 274.636,-1864.18 277.636,-1867.18" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="35" stroke="rgb(248,248,255)" width="84" x="190.636" y="-1864.18"/>
    <metadata/><rect fill="white" height="35" opacity="0" stroke="white" transform="" width="84" x="190.636" y="-1864.18"/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="184.497,-1800.59 184.497,-1765.59 187.497,-1768.59 187.497,-1797.59 301.766,-1797.59 304.766,-1800.59" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="304.766,-1765.59 184.497,-1765.59 187.497,-1768.59 301.766,-1768.59 301.766,-1797.59 304.766,-1800.59" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="29" stroke="rgb(248,248,255)" width="114.269" x="187.497" y="-1797.59"/>
    <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="114.269" x="187.497" y="-1797.59"/></g>
  </g>
  <g id="TextClass">
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-191.899" y="-1701">总有功：</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="412.843" y="-679.668">10#2段A母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="469.234" y="-431.33">母</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="468.616" y="-398.891">联</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="467.852" y="-367.98">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1876.46" y="-421.906">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1871.46" y="-399.906">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1871.46" y="-377.906">主</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1871.46" y="-355.906">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="450" y="-1322">10#1段母线</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="35" stroke="rgb(0,0,0)" x="-117.111" y="-1846.56">110kV瑞花站</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-202.607" y="-1483.34">危险点说明：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-169.534" y="-880.415">联系方式：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-193" y="-1666">总无功：</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2746.09" y="-1135.52">母</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2746.09" y="-1113.52">联</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2746.09" y="-1091.52">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="871" y="-1415">63MVA</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1172" y="-1448">63MVA</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="645.017" y="-992.421">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="639.674" y="-969.549">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="638.933" y="-947.549">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="638.933" y="-925.549">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="788.755" y="-1866.74">翰北瑞</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="916.806" y="-1517.3">10347</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="914.302" y="-1463.9">10348</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="626" y="-1383">1010</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="815" y="-1700">103</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="811" y="-1631">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="812" y="-1773">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="739" y="-1742">07</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="858" y="-1825">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="856" y="-1756">37</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="862" y="-1685">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="518" y="-1620">110#1段母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1387.33" y="-1613.25">110#2段母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1300.15" y="-1500.27">10447</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1365.11" y="-1463.91">10448</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1308" y="-1704">104</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1305" y="-1630">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1343" y="-1684">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1342" y="-1749">37</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1305" y="-1759">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1337" y="-1824">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1231" y="-1740">07</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1025" y="-1615.55">100</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="974" y="-1612">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1091" y="-1613">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1021" y="-1535">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1079" y="-1534">27</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1075" y="-1383">1020</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="931" y="-1221">901</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1019.59" y="-1212.34">911</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1040.59" y="-1151.34">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1103.59" y="-1212.34">912</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1124.59" y="-1151.34">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1214.63" y="-1213.47">913</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1235.63" y="-1153.47">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1300.63" y="-1213.47">914</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1321.63" y="-1154.47">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1389.63" y="-1213.47">915</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1410.63" y="-1153.47">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1503.47" y="-1215.86">916</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1524.47" y="-1155.86">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1588.47" y="-1214.86">917</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1610.47" y="-1153.86">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="609" y="-1223">C01</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="606" y="-1110">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="642" y="-1100">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="630" y="-1165">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="495" y="-1223">993</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="516" y="-1163">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1951.23" y="-1202.55">918</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1972.23" y="-1141.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2041.23" y="-1202.55">919</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2062.23" y="-1142.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2127.23" y="-1202.55">920</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2148.23" y="-1142.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2211.23" y="-1202.55">921</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2232.23" y="-1139.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2322.23" y="-1140.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2411.1" y="-1203.55">923</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2431.1" y="-1145.55">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="756.795" y="-567.775">926</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="862.704" y="-569.577">927</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="954.704" y="-568.577">928</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="883.704" y="-514.577">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1058.47" y="-567.711">929</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1079.47" y="-512.711">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1592.1" y="-572.281">933</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1613.91" y="-518.089">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1901.87" y="-566.133">902</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="900.144" y="-1392.64">档位：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="897.538" y="-1371.2">油温1：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="896.538" y="-1346.7">油温2：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1309" y="-1430">档位：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1306.87" y="-1408.17">油温1：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1308" y="-1383.53">油温2：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="900.275" y="-1325.34">绕温：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1308.74" y="-1361.44">绕温：</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="33" stroke="rgb(0,0,0)" x="200.636" y="-1833.18">AVC</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-175.113" y="-646">图纸编号：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" transform="matrix(1.31699,0,0,1.09307,1000.48,-3441.59)" y="1646">母联甲</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="37" stroke="rgb(0,0,0)" x="430.636" y="-1826.18">GIS</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2516.88" y="-1216.07">924</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2537.88" y="-1153.07">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2639.74" y="-1154.07">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1702.9" y="-968.543">4</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1699.38" y="-946.543">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1699.38" y="-924.543">站</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1699.38" y="-902.543">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1722.52" y="-1210.66">991</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1743.52" y="-1150.66">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2749.9" y="-1213.67">90011</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="893" y="-1033">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-1008.78">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-989">主</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-967">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="480" y="-1034">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="475" y="-1007">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="475" y="-985">接</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="475" y="-963">地</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="475" y="-941">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="573.394" y="-915.376">6MVar</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1224.92" y="-1502.78">1044</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="873.254" y="-1436.25">#1主变</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="839.899" y="-1492.8">1034</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2627.47" y="-1209.78">925</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1173.8" y="-1472.15">#2主变</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2310.74" y="-1194.21">922</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1237.52" y="-1830.42">0104</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="751.423" y="-1831.23">0103</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="33" stroke="rgb(0,0,0)" x="186.015" y="-1769.23">AVC防误</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="998.891" y="-1039.79">瑞</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="998.891" y="-1014.79">桃</text>
    <text fill="rgb(0,0,128)" font-family="Serif" font-size="27" stroke="rgb(0,0,128)" x="107.425" y="-1595.13">瑞花站</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1611.31" y="-1619.7">1102</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1663.69" y="-1544.54">11027</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" transform="matrix(1.31699,0,0,1.09307,1675.83,-3398.32)" y="1646">母联乙</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="747.539" y="-1504.72">01517</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" transform="matrix(1.31699,0,0,1.09307,562.885,-3342.77)" y="1646">1号母线PT</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1408.12" y="-1500.83">01527</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" transform="matrix(1.31699,0,0,1.09307,1446.81,-3354.31)" y="1646">2号母线PT</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1084.08" y="-1038.64">瑞</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1084.08" y="-1013.64">安</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1191.17" y="-1038.64">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1191.17" y="-1013.64">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1191.17" y="-988.64">三</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1279.44" y="-1038.64">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1279.44" y="-1013.64">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1279.44" y="-988.64">四</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1367.66" y="-1038.64">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1367.66" y="-1013.64">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1367.66" y="-988.64">五</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1481.18" y="-1038.64">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1481.18" y="-1013.64">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1481.18" y="-988.64">六</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1568.08" y="-1038.64">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1568.08" y="-1013.64">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1568.08" y="-988.64">七</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1933.31" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1933.31" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1933.31" y="-979.46">八</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2023.11" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2023.11" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2023.11" y="-979.46">九</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2110.99" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2110.99" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2110.99" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2188.37" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2188.37" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2188.37" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2188.37" y="-954.46">一</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2275.3" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2275.3" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2275.3" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2275.3" y="-954.46">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2392" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2392" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2392" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2392" y="-954.46">三</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2495.5" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2495.5" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2495.5" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2495.5" y="-954.46">四</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2596.46" y="-1029.46">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2596.46" y="-1004.46">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2596.46" y="-979.46">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2596.46" y="-954.46">五</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="813.8" y="-988.696">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="807.716" y="-965.824">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="807.716" y="-943.824">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="807.716" y="-921.824">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="777.783" y="-1219.28">C02</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="774.783" y="-1106.28">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="810.783" y="-1096.28">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="798.783" y="-1161.28">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="742.178" y="-911.651">6MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="668.186" y="-337.4">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="662.102" y="-314.528">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="662.102" y="-292.528">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="662.102" y="-270.528">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="632.169" y="-567.98">C03</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="629.169" y="-454.98">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="665.169" y="-444.98">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="653.169" y="-509.98">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="596.563" y="-260.355">6MVar</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="736.519" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="736.519" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="736.519" y="-341.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="736.519" y="-316.375">六</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="839.688" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="839.688" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="839.688" y="-341.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="839.688" y="-316.375">七</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="926.617" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="926.617" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="926.617" y="-341.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="926.617" y="-316.375">八</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1034.56" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1034.56" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1034.56" y="-341.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1034.56" y="-316.375">九</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1158.52" y="-565.374">930</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1250.52" y="-564.374">931</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1178.46" y="-511.969">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1354.28" y="-563.508">932</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1375.28" y="-508.508">38</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1135.5" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1135.5" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1135.5" y="-341.375">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1135.5" y="-316.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1222.43" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1222.43" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1222.43" y="-341.375">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1222.43" y="-316.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1222.43" y="-291.375">一</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1335.15" y="-391.375">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1335.15" y="-366.375">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1335.15" y="-341.375">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1335.15" y="-316.375">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1335.15" y="-291.375">二</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1458.22" y="-320.3">5</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1454.7" y="-298.3">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1454.7" y="-276.3">站</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1454.7" y="-254.3">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1477.85" y="-562.419">992</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1498.85" y="-502.419">38</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1564.74" y="-396.27">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1564.74" y="-371.27">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1564.74" y="-346.27">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1564.74" y="-321.27">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1564.74" y="-296.27">三</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1725.43" y="-548.37">0952</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2063.92" y="-679.822">10#2段B母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2281.84" y="-563.261">934</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2387.75" y="-565.063">935</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2479.75" y="-564.063">936</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2408.75" y="-510.063">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2583.51" y="-563.197">937</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2604.51" y="-508.197">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3172.35" y="-327.487">4</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3166.27" y="-304.615">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3166.27" y="-282.615">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3166.27" y="-260.615">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3136.34" y="-558.066">C04</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3133.34" y="-445.066">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3169.34" y="-435.066">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3157.34" y="-500.066">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3100.73" y="-250.442">6MVar</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2261.56" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2261.56" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2261.56" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2261.56" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2261.56" y="-286.861">四</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2364.73" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2364.73" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2364.73" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2364.73" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2364.73" y="-286.861">五</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2451.66" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2451.66" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2451.66" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2451.66" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2451.66" y="-286.861">六</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2559.61" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2559.61" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2559.61" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2559.61" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2559.61" y="-286.861">七</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2683.56" y="-560.86">938</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2775.56" y="-559.86">939</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2704.56" y="-505.86">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2879.32" y="-558.994">940</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2900.32" y="-503.994">38</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2660.55" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2660.55" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2660.55" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2660.55" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2660.55" y="-286.861">八</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2747.48" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2747.48" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2747.48" y="-336.861">二</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2747.48" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2747.48" y="-286.861">九</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2860.2" y="-386.861">备</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2860.2" y="-361.861">用</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2860.2" y="-336.861">三</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="2860.2" y="-311.861">十</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="3389.69" y="-536.135">0953</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2149.38" y="-428.402">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2144.38" y="-406.402">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2144.38" y="-384.402">主</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2144.38" y="-362.402">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3002.15" y="-437.796">母</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3002.15" y="-415.796">联</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3002.15" y="-393.796">乙</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2996.67" y="-558.908">90022</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3260.42" y="-569.558">994</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3281.42" y="-509.558">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3245.42" y="-380.558">7</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3240.42" y="-353.558">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3240.42" y="-331.558">接</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3240.42" y="-309.558">地</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3240.42" y="-287.558">变</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1837.15" y="-1195.93">0951</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1809.87" y="-990.474">#1</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1809.87" y="-965.474">PT</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1698" y="-356.321">#2</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="1698" y="-331.321">PT</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="3359.63" y="-343.52">#3</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" x="3359.63" y="-318.52">PT</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="760.405" y="-1545.9">0151</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="1333.88" y="-1545.08">0152</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="2141.28" y="-572.699">905</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="489.499" y="-570.546">9001</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="777.823" y="-521.621">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="982.462" y="-522.799">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="1273.66" y="-517.598">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="2306.3" y="-512.645">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="2505.05" y="-513.446">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="2796.63" y="-517.901">38</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="3389.69" y="-496.505">0953</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="29" stroke="rgb(255,255,255)" x="292.343" y="-1842.14"> 备自投</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="21" stroke="rgb(255,255,255)" x="1265.5" y="-1886.36">林瑞线</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="1300.28" y="-2050.182">线</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="1260.28" y="-2051.182">程</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="1261.28" y="-2051.182">程</text>
  </g>
  <g id="LinkPointClass">
    <g href="110kV瑞花站103间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="815" y="-1700">103</text></g>
    <g href="110kV瑞花站104间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1308" y="-1704">104</text></g>
    <g href="110kV瑞花站100间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1025" y="-1615.55">100</text></g>
    <g href="110kV瑞花站_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="931" y="-1221">901</text></g>
    <g href="110kV瑞花站瑞桃911间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1019.59" y="-1212.34">911</text></g>
    <g href="110kV瑞花站瑞安912间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1103.59" y="-1212.34">912</text></g>
    <g href="110kV瑞花站913间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1214.63" y="-1213.47">913</text></g>
    <g href="110kV瑞花站914间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1300.63" y="-1213.47">914</text></g>
    <g href="110kV瑞花站915间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1389.63" y="-1213.47">915</text></g>
    <g href="110kV瑞花站916间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1503.47" y="-1215.86">916</text></g>
    <g href="110kV瑞花站917间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1588.47" y="-1214.86">917</text></g>
    <g href="110kV瑞花站C01间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="609" y="-1223">C01</text></g>
    <g href="110kV瑞花站993间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="495" y="-1223">993</text></g>
    <g href="110kV瑞花站918间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1951.23" y="-1202.55">918</text></g>
    <g href="110kV瑞花站919间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2041.23" y="-1202.55">919</text></g>
    <g href="110kV瑞花站920间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2127.23" y="-1202.55">920</text></g>
    <g href="110kV瑞花站921间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2211.23" y="-1202.55">921</text></g>
    <g href="110kV瑞花站923间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2411.1" y="-1203.55">923</text></g>
    <g href="110kV瑞花站926间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="756.795" y="-567.775">926</text></g>
    <g href="110kV瑞花站927间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="862.704" y="-569.577">927</text></g>
    <g href="110kV瑞花站928间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="954.704" y="-568.577">928</text></g>
    <g href="110kV瑞花站929间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1058.47" y="-567.711">929</text></g>
    <g href="110kV瑞花站933间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1592.1" y="-572.281">933</text></g>
    <g href="110kV瑞花站_2主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1901.87" y="-566.133">902</text></g>
    <g href="110kV瑞花站924间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2516.88" y="-1216.07">924</text></g>
    <g href="110kV瑞花站991间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1722.52" y="-1210.66">991</text></g>
    <g href="110kV文华站母联甲9001间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2749.9" y="-1213.67">90011</text></g>
    <g href="nn_一次接线索引图_110kV.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="92" stroke-width="1" width="332" x="-198.532" y="-1903.23"/></g>
    <g href="AVC_110kV瑞花站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="41" stroke-width="1" width="90" x="187.636" y="-1867.18"/></g>
    <g href="110kV瑞花站_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="873.254" y="-1436.25">#1主变</text></g>
    <g href="110kV瑞花站925间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2627.47" y="-1209.78">925</text></g>
    <g href="110kV瑞花站_2主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1173.8" y="-1472.15">#2主变</text></g>
    <g href="110kV瑞花站922间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2310.74" y="-1194.21">922</text></g>
    <g href="110kV瑞花站AVC控制防误状态监视图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="35" stroke-width="1" width="120.269" x="184.497" y="-1800.59"/></g>
    <g href="110kV瑞花站AVC控制防误状态监视图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="35" stroke-width="1" width="113.497" x="184.726" y="-1801.71"/></g>
    <g href="110kV瑞花站C02间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="777.783" y="-1219.28">C02</text></g>
    <g href="110kV瑞花站C03间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="632.169" y="-567.98">C03</text></g>
    <g href="110kV瑞花站930间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1158.52" y="-565.374">930</text></g>
    <g href="110kV瑞花站931间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1250.52" y="-564.374">931</text></g>
    <g href="110kV瑞花站932间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1354.28" y="-563.508">932</text></g>
    <g href="110kV瑞花站992间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1477.85" y="-562.419">992</text></g>
    <g href="110kV瑞花站934间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2281.84" y="-563.261">934</text></g>
    <g href="110kV瑞花站935间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2387.75" y="-565.063">935</text></g>
    <g href="110kV瑞花站936间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2479.75" y="-564.063">936</text></g>
    <g href="110kV瑞花站937间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2583.51" y="-563.197">937</text></g>
    <g href="110kV瑞花站C04间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3136.34" y="-558.066">C04</text></g>
    <g href="110kV瑞花站938间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2683.56" y="-560.86">938</text></g>
    <g href="110kV瑞花站939间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2775.56" y="-559.86">939</text></g>
    <g href="110kV瑞花站940间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2879.32" y="-558.994">940</text></g>
    <g href="110kV文华站母联甲9001间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2996.67" y="-558.908">90022</text></g>
    <g href="110kV瑞花站994间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="3260.42" y="-569.558">994</text></g>
    <g href="110kV瑞花站9001间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Serif" font-size="16" stroke="rgb(255,255,255)" x="489.499" y="-570.546">9001</text></g>
    <g href="110kV瑞花站备自投虚开关间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Serif" font-size="29" stroke="rgb(255,255,255)" x="292.343" y="-1842.14"> 备自投</text></g>
  </g>
</svg>