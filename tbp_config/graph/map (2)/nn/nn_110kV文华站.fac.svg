<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://www.cim.com" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:dfg="http://dfg.dongfang-china.com/2010/SVGExtensions/MX" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-293" id="thSvg" viewBox="-207 -2023.16 2966.24 1844.16">
  <defs>
    <style type="text/css"><![CDATA[
      .def {stroke:#a0a0a4;fill:#a0a0a4}
      .kV500 {stroke:#ff0000;fill:#ff0000}
      .kV220 {stroke:#ffffff;fill:#ffffff}
      .kV110 {stroke:#aa557f;fill:#aa557f}
      .kV1 {stroke:#cc0000;fill:#cc0000}
      .kV35 {stroke:#ffff00;fill:#ffff00}
      .kVdisp {stroke:#cc0000;fill:#cc0000}
      .kV10 {stroke:#00ff00;fill:#00ff00}
      .kV11 {stroke:#ffff00;fill:#ffff00}
      .kV6 {stroke:#5db5b9;fill:#5db5b9}
      .JieDi {stroke:#aaaa7f;fill:#aaaa7f}
      .BuDaiDian {stroke:#3c78b4;fill:#3c78b4}
      .BuQueDing {stroke:#cccccc;fill:#cccccc}
      .DianYuanDian {stroke:#d673a6;fill:#d673a6}
      .QuYuGD {stroke:#00ffe2;fill:#00ffe2}
      .AllLevel {stroke:#ccec82;fill:#ccec82}
      .LoadLine {stroke:#ae74b3;fill:#ae74b3}
      .IsLand0 {stroke:#f8e576;fill:#f8e576}
      .IsLand1 {stroke:#a5ffab;fill:#a5ffab}
      .IsLand2 {stroke:#aef2ff;fill:#aef2ff}
      .IsLand3 {stroke:#b6b1fc;fill:#b6b1fc}
      .IsLand4 {stroke:#deafff;fill:#deafff}
      .IsLand5 {stroke:#ff9ec3;fill:#ff9ec3}
      .kV20 {stroke:#ddbf1b;fill:#ddbf1b}
      .NFkV500 {stroke:#ff0000;fill:none}
      .NFkV220 {stroke:#ffffff;fill:none}
      .NFkV110 {stroke:#aa557f;fill:none}
      .NFkV1 {stroke:#cc0000;fill:none}
      .NFkV35 {stroke:#ffff00;fill:none}
      .NFkVdisp {stroke:#cc0000;fill:none}
      .NFkV10 {stroke:#00ff00;fill:none}
      .NFkV11 {stroke:#ffff00;fill:none}
      .NFkV6 {stroke:#5db5b9;fill:none}
      .NFJieDi {stroke:#aaaa7f;fill:none}
      .NFBuDaiDian {stroke:#3c78b4;fill:none}
      .NFBuQueDing {stroke:#cccccc;fill:none}
      .NFDianYuanDian {stroke:#d673a6;fill:none}
      .NFQuYuGD {stroke:#00ffe2;fill:none}
      .NFAllLevel {stroke:#ccec82;fill:none}
      .NFLoadLine {stroke:#ae74b3;fill:none}
      .NFIsLand0 {stroke:#f8e576;fill:none}
      .NFIsLand1 {stroke:#a5ffab;fill:none}
      .NFIsLand2 {stroke:#aef2ff;fill:none}
      .NFIsLand3 {stroke:#b6b1fc;fill:none}
      .NFIsLand4 {stroke:#deafff;fill:none}
      .NFIsLand5 {stroke:#ff9ec3;fill:none}
      .NFkV20 {stroke:#ddbf1b;fill:none}
    ]]></style>
    <symbol id="terminal" viewBox="-3 -3 6 6" visibility="hidden">
      <circle cx="0" cy="0" fill="none" r="2" stroke="rgb(255,255,0)"/>
    </symbol>
    <symbol dfg:desc="限电" dfg:flg="g_si" id="Tag:shape0" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-5)">限电</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="保安电" dfg:flg="g_si" id="Tag:shape1" viewBox="-2 -27 60 29">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,1,-7)">保安电</text>
      <rect fill="none" height="24" stroke="#ff0000" stroke-width="1" width="56" x="0" y="-25"/>
    </symbol>
    <symbol dfg:desc="间隔检修" dfg:flg="g_si" id="Tag:shape2" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止遥控" dfg:flg="g_si" id="Tag:shape3" viewBox="-2 -41 26 23">
      <text fill="#ff0000" font-family="SimSun" font-size="11" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.5,-33.75)" y="11">禁控</text>
      <rect fill="none" height="18" stroke="#ff0000" width="22" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="禁止告警" dfg:flg="g_si" id="Tag:shape4" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-21)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-3)">告警</text>
    </symbol>
    <symbol dfg:desc="线路检修" dfg:flg="g_si" id="Tag:shape5" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">检修</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="禁止刷新" dfg:flg="g_si" id="Tag:shape6" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-4)">刷新</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="引下线一组" dfg:flg="g_si" id="Tag:shape7" viewBox="-2 -42 46 44">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-21)">引线</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,6,-3)">下一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-40"/>
    </symbol>
    <symbol dfg:desc="小电源" dfg:flg="g_si" id="Tag:shape8" viewBox="-2 -41 24 24">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,7,-70)" y="48"/>
      <text fill="#ff0000" font-family="SimSun" font-size="12" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,-0.198305,-57.3351)" y="35">敏感</text>
      <rect fill="none" height="20" stroke="#ff0000" width="20" x="0.187167" y="-38.6676"/>
    </symbol>
    <symbol dfg:desc="注释1" dfg:flg="g_si" id="Tag:shape9" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">注释</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一级保供" dfg:flg="g_si" id="Tag:shape10" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-23)">一级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,3,-5)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="二级保供" dfg:flg="g_si" id="Tag:shape11" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-22)">二级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,4,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="退出运行" dfg:flg="g_si" id="Tag:shape12" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-22)">退出</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,7,-4)">运行</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="新设备未投运" dfg:flg="g_si" id="Tag:shape13" viewBox="-2 -38 79 40">
      <rect fill="none" height="34" stroke="#ff0000" stroke-width="1" width="75" x="0" y="-36"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-21)">新设备</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,12,-3)">未投运</text>
    </symbol>
    <symbol dfg:desc="自愈" dfg:flg="g_si" id="Tag:shape14" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,5,-21)" y="16">自愈</text>
      <rect fill="none" height="22" stroke="#ff0000" width="42" x="0.5" y="-21.5"/>
    </symbol>
    <symbol dfg:desc="禁止操作" dfg:flg="g_si" id="Tag:shape15" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">禁止</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">操作</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="站控" dfg:flg="g_si" id="Tag:shape16" viewBox="-2 -24 46 26">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-5)">站控</text>
      <rect fill="none" height="22" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-22"/>
    </symbol>
    <symbol dfg:desc="带电作业" dfg:flg="g_si" id="Tag:shape17" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">带电</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">作业</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="重合闸退出" dfg:flg="g_si" id="Tag:shape18" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">重合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="备自投退出" dfg:flg="g_si" id="Tag:shape19" viewBox="-2 -41 64 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">备自投</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">退出</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="60" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="保护退出" dfg:flg="g_si" id="Tag:shape20" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,0.75,-36.5)" y="16">配线路</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3,-19)" y="16">检 修</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-38"/>
    </symbol>
    <symbol dfg:desc="冷备用" dfg:flg="g_si" id="Tag:shape21" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)"> 冷</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">备用</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="调试一" dfg:flg="g_si" id="Tag:shape22" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-22)">调试</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,5,-4)">一</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="全站停电检修" dfg:flg="g_si" id="Tag:shape23" viewBox="-2 -41 46 43">
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">全站</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">检修</text>
    </symbol>
    <symbol dfg:desc="特级保供" dfg:flg="g_si" id="Tag:shape24" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-22)">特级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-4)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="一般保供" dfg:flg="g_si" id="Tag:shape25" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-24)">一般</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" transform="matrix(1.05556,0,0,1,2,-6)">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" stroke-width="1" width="42" x="0" y="-39"/>
    </symbol>
    <symbol dfg:desc="三级保供" dfg:flg="g_si" id="Tag:shape26" viewBox="-2 -41 46 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.48886,-39)" y="16">三级</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,3.25981,-20.7709)" y="16">保供</text>
      <rect fill="none" height="38" stroke="#ff0000" width="42" x="0" y="-39.1145"/>
    </symbol>
    <symbol dfg:desc="禁止合闸,有人工作" dfg:flg="g_si" id="Tag:shape27" viewBox="-2 -67 109 69">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">有人工作</text>
      <rect fill="none" height="34" stroke="#ff0000" width="75" x="30" y="-38"/>
      <circle cx="14" cy="-51" fill="none" r="14" stroke="#ff0000"/>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-60" y2="-35"/>
    </symbol>
    <symbol dfg:desc="禁止合闸线路有人工作" dfg:flg="g_si" id="Tag:shape28" viewBox="-2 -85 112 87">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-54)" y="15">禁止合闸</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-36)" y="15">线路有人</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,32,-18)" y="15">工作</text>
      <line fill="#00ff00" stroke="#ff0000" stroke-width="2" x1="2" x2="28" y1="-78" y2="-53"/>
      <circle cx="14" cy="-69" fill="none" r="14" stroke="#ff0000"/>
      <rect fill="none" height="55" stroke="#ff0000" width="78" x="30" y="-56"/>
    </symbol>
    <symbol dfg:desc="间隔检修2" dfg:flg="g_si" id="Tag:shape29" viewBox="-2 -41 57 43">
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-37)" y="15">间隔</text>
      <text fill="#ff0000" font-family="SimSun" font-size="17" stroke="#ff0000" stroke-width="0.1" transform="matrix(1.05556,0,0,1,6,-19)" y="15">检修2</text>
      <rect fill="none" height="38" stroke="#ff0000" width="53" x="0" y="-39"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Breaker:shape3_0" viewBox="0 -28 48 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Breaker:shape3_1" viewBox="1 -29 46 27">
      <rect fill="rgb(0,255,0)" height="23" stroke-width="2" width="32" x="8" y="-27"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Breaker:shape3_2" viewBox="0 -28 48 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <polyline fill="none" points="9,-23 41,-7" stroke-width="2"/>
      <polyline fill="none" points="41,-23 9,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Breaker:shape3_3" viewBox="2 -28 44 27">
      <rect fill="none" height="23" stroke-width="2" width="34" x="7" y="-26"/>
      <polyline fill="none" points="40,-23 8,-7" stroke-width="2"/>
      <polyline fill="none" points="8,-23 40,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-18"/>
      <use height="8" terminal-index="1" width="8" x="39" xlink:href="#terminal" y="-18"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Breaker:shape2_0" viewBox="-4 -48 27 48">
      <rect fill="none" height="34" stroke-width="2" width="23" x="-2" y="-41"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Breaker:shape2_1" viewBox="-3 -47 27 46">
      <rect fill="rgb(0,255,0)" height="32" stroke-width="2" width="23" x="-1" y="-40"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Breaker:shape2_2" viewBox="-4 -48 28 48">
      <rect fill="none" height="35" stroke-width="2" width="24" x="-2" y="-41"/>
      <polyline fill="none" points="19,-39 3,-6" stroke-width="2"/>
      <polyline fill="none" points="18,-6 2,-39" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Breaker:shape2_3" viewBox="-4 -46 28 44">
      <rect fill="none" height="33" stroke-width="2" width="24" x="-2" y="-40"/>
      <polyline fill="none" points="18,-7 2,-40" stroke-width="2"/>
      <polyline fill="none" points="19,-40 3,-7" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-45"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape0_0" viewBox="0 0 20 50">
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="47.7308" y2="37.5586"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="12.0711" y2="1.91098"/>
      <line stroke-width="2" x1="1.5339" x2="11.1196" y1="14.6835" y2="38.0229"/>
      <line stroke-width="2" x1="7.79317" x2="14.424" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape0_1" viewBox="0 0 20 50">
      <line stroke-width="2" x1="7.77069" x2="14.4465" y1="11.5353" y2="11.5353"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.95842" y2="47.8157"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape0_2" viewBox="0 0 20 50">
      <line stroke-width="2" x1="5.91257" x2="16.3046" y1="13.614" y2="39.8683"/>
      <line stroke-width="2" x1="17.1558" x2="5.06136" y1="13.7286" y2="39.8683"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.96389" y2="48.2042"/>
      <line stroke-width="2" x1="7.80206" x2="14.4151" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape0_3" viewBox="0 0 20 50">
      <polyline fill="none" points="17.1086,13.8684 5.10859,39.8683" stroke-width="2"/>
      <polyline fill="none" points="5.8936,13.6195 16.3236,39.8683" stroke-width="2"/>
      <line stroke-width="2" x1="11.1086" x2="11.1086" y1="1.7049" y2="47.8157"/>
      <line stroke-width="2" x1="7.78951" x2="14.4277" y1="11.5353" y2="11.5353"/>
      <use height="8" terminal-index="0" width="8" x="8.1086" xlink:href="#terminal" y="3.85257"/>
      <use height="8" terminal-index="1" width="8" x="8.1086" xlink:href="#terminal" y="41.4261"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape5_0" viewBox="-1 -18 57 20">
      <line stroke-width="1" x1="14" x2="14" y1="-8" y2="-6"/>
      <line stroke-width="1" x1="38" x2="13" y1="-7" y2="-16"/>
      <line stroke-width="1" x1="38" x2="47" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="5" x2="14" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="54" x2="54" y1="-9" y2="-6"/>
      <line stroke-width="1" x1="51" x2="51" y1="-10" y2="-5"/>
      <line stroke-width="1" x1="47" x2="47" y1="-14" y2="0"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape5_1" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape5_2" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape5_3" viewBox="0 -16 56 19">
      <line stroke-width="1" x1="14" x2="14" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="54" x2="54" y1="-5" y2="-8"/>
      <line stroke-width="1" x1="51" x2="51" y1="-4" y2="-9"/>
      <line stroke-width="1" x1="47" x2="47" y1="1" y2="-14"/>
      <line stroke-width="1" x1="6" x2="48" y1="-7" y2="-7"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape1_0" viewBox="8 -100 20 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-61" y2="-52"/>
      <line stroke-width="1" x1="19" x2="10" y1="-61" y2="-86"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape1_1" viewBox="10 -100 18 57">
      <line stroke-width="1" x1="18" x2="20" y1="-85" y2="-85"/>
      <line stroke-width="1" x1="19" x2="19" y1="-94" y2="-52"/>
      <line stroke-width="1" x1="12" x2="26" y1="-52" y2="-52"/>
      <line stroke-width="1" x1="16" x2="21" y1="-48" y2="-48"/>
      <line stroke-width="1" x1="17" x2="20" y1="-45" y2="-45"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape1_2" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape1_3" viewBox="10 -100 18 56">
      <line stroke-width="1" x1="18" x2="20" y1="-86" y2="-86"/>
      <line stroke-width="1" x1="19" x2="19" y1="-95" y2="-53"/>
      <line stroke-width="1" x1="12" x2="26" y1="-53" y2="-53"/>
      <line stroke-width="1" x1="16" x2="21" y1="-49" y2="-49"/>
      <line stroke-width="1" x1="17" x2="20" y1="-46" y2="-46"/>
      <use height="8" terminal-index="0" width="8" x="16" xlink:href="#terminal" y="-97"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape4_0" viewBox="-1 -16 48 17">
      <line stroke-width="1" x1="14" x2="39" y1="-5" y2="-14"/>
      <line stroke-width="1" x1="42" x2="34" y1="-5" y2="-5"/>
      <line stroke-width="1" x1="34" x2="34" y1="-7" y2="-4"/>
      <line stroke-width="1" x1="14" x2="5" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape4_1" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape4_2" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape4_3" viewBox="-3 -11 52 12">
      <line stroke-width="1" x1="11" x2="11" y1="-3" y2="-6"/>
      <line stroke-width="1" x1="3" x2="43" y1="-5" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="38" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape2_0" viewBox="-1 -30 22 29">
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-5.0564"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape2_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape2_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="9.7856" x2="19.1085" y1="-12.363" y2="-21.6112"/>
      <line x1="10.2755" x2="1.03594" y1="-12.2144" y2="-21.8412"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape2_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-5"/>
      <circle cx="10" cy="-18" fill="none" r="3"/>
      <line x1="10" x2="1" y1="-15" y2="-6"/>
      <line x1="19" x2="10" y1="-14" y2="-23"/>
      <line x1="10" x2="1" y1="-23" y2="-14"/>
      <line x1="19" x2="10" y1="-6" y2="-15"/>
      <line x1="19.1085" x2="9.95129" y1="-21.6451" y2="-12.5633"/>
      <line x1="10.2968" x2="0.939632" y1="-12.3327" y2="-21.8464"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:shape3_0" viewBox="-1 -30 22 30">
      <line x1="10" x2="1" y1="-15" y2="-24"/>
      <line x1="19" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="1" y1="-7" y2="-16"/>
      <line x1="19" x2="10" y1="-24" y2="-15"/>
      <line x1="10" x2="10" y1="-15" y2="-24"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:shape3_1" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="Disconnector:shape3_2" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="9.78968" x2="0.951774" y1="-17.3132" y2="-8.47532"/>
      <line x1="10.2429" x2="19.0808" y1="-17.2679" y2="-8.43"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="Disconnector:shape3_3" viewBox="-1 -30 22 30">
      <line x1="10" x2="10" y1="-15" y2="-25"/>
      <circle cx="10" cy="-12" fill="none" r="3"/>
      <line x1="10" x2="19" y1="-15" y2="-24"/>
      <line x1="1" x2="10" y1="-16" y2="-7"/>
      <line x1="10" x2="19" y1="-7" y2="-16"/>
      <line x1="1" x2="10" y1="-24" y2="-15"/>
      <line x1="0.986614" x2="9.98946" y1="-8.42733" y2="-17.4713"/>
      <line x1="9.98946" x2="19.1979" y1="-17.4302" y2="-8.34511"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-27"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Capacitor:shape0_0" viewBox="-2 -24 35 24">
      <line stroke-width="1" x1="31" x2="0" y1="-18" y2="-18"/>
      <line stroke-width="1" x1="31" x2="0" y1="-2" y2="-2"/>
      <use height="8" terminal-index="0" width="8" x="12" xlink:href="#terminal" y="-21"/>
    </symbol>
    <symbol dfg:flg="g_si" id="EnergyConsumer:shape1_0" viewBox="-1 -33 20 34">
      <line stroke-width="2" x1="9" x2="9" y1="-27" y2="-3"/>
      <polyline fill="none" points="1,-13 9,-1 17,-13" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="6" xlink:href="#terminal" y="-30"/>
    </symbol>
    <symbol dfg:flg="g_po" dfg:val="2" id="Transformer2:shape12_1" viewBox="-1 -93 61 92">
      <ellipse cx="29" cy="-62" fill="none" rx="24" ry="24.5" stroke-width="2"/>
      <line stroke-width="2" x1="29" x2="29" y1="-60" y2="-68"/>
      <line stroke-width="2" x1="29" x2="37" y1="-68" y2="-76"/>
      <line stroke-width="2" x1="21" x2="29" y1="-76" y2="-68"/>
      <line stroke-width="2" x1="50" x2="50" y1="-80" y2="-80"/>
      <line stroke-width="2" x1="1" x2="58" y1="-53" y2="-86"/>
      <use height="8" terminal-index="0" width="8" x="26" xlink:href="#terminal" y="-89"/>
      <use height="8" terminal-index="2" width="8" x="26" xlink:href="#terminal" y="-71.25"/>
    </symbol>
    <symbol dfg:flg="g_po" dfg:val="2" id="Transformer2:shape12_0" viewBox="-1 -93 61 92">
      <circle cx="29" cy="-31" fill="none" r="24" stroke-width="2"/>
      <line stroke-width="2" x1="40" x2="24" y1="-26" y2="-35"/>
      <line stroke-width="2" x1="40" x2="24" y1="-26" y2="-18"/>
      <line stroke-width="2" x1="24" x2="24" y1="-35" y2="-18"/>
      <use height="8" terminal-index="1" width="8" x="27.25" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="GroundLine:shape0_0" viewBox="-2 -32 22 32">
      <line stroke-width="1" x1="7" x2="11" y1="-2" y2="-2"/>
      <line stroke-width="1" x1="9" x2="9" y1="-27" y2="-9"/>
      <line stroke-width="1" x1="0" x2="18" y1="-9" y2="-9"/>
      <line stroke-width="1" x1="6" x2="13" y1="-6" y2="-6"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="-29"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape33_0" viewBox="-1 -48 37 48">
      <ellipse cx="7" cy="-23" fill="none" rx="6.5" ry="6" stroke-width="2"/>
      <circle cx="7" cy="-13" fill="none" r="6.5" stroke-width="2"/>
      <circle cx="14" cy="-17" fill="none" r="6.5" stroke-width="2"/>
      <rect fill="none" height="14" stroke-width="2" width="6" x="26" y="-30"/>
      <line stroke-width="2" x1="30" x2="28" y1="-2" y2="-2"/>
      <line stroke-width="2" x1="31" x2="27" y1="-4" y2="-4"/>
      <line stroke-width="2" x1="34" x2="24" y1="-6" y2="-6"/>
      <line stroke-width="2" x1="29" x2="29" y1="-16" y2="-6"/>
      <line stroke-width="2" x1="29" x2="8" y1="-34" y2="-34"/>
      <line stroke-width="2" x1="29" x2="29" y1="-34" y2="-20"/>
      <line stroke-width="2" x1="30" x2="29" y1="-22" y2="-20"/>
      <line stroke-width="2" x1="29" x2="28" y1="-20" y2="-22"/>
      <line stroke-width="2" x1="8" x2="8" y1="-29" y2="-43"/>
      <use height="8" terminal-index="0" width="8" x="5" xlink:href="#terminal" y="-45"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape36_0" viewBox="-1 -51 17 51">
      <rect fill="none" height="19" stroke-width="1" width="8" x="4" y="-40"/>
      <line stroke-width="1" x1="6" x2="9" y1="-2" y2="-2"/>
      <line stroke-width="1" x1="5" x2="11" y1="-5" y2="-5"/>
      <line stroke-width="1" x1="1" x2="14" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="8" x2="8" y1="-21" y2="-7"/>
      <line stroke-width="1" x1="7" x2="7" y1="-46" y2="-27"/>
      <line stroke-width="1" x1="6" x2="7" y1="-29" y2="-27"/>
      <line stroke-width="1" x1="8" x2="9" y1="-27" y2="-29"/>
      <use height="8" terminal-index="0" width="8" x="4" xlink:href="#terminal" y="-48"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape63_0" viewBox="-1 -26 25 26">
      <circle cx="16" cy="-8" fill="none" r="6" stroke-width="2"/>
      <circle cx="11" cy="-15" fill="none" r="6" stroke-width="2"/>
      <circle cx="7" cy="-8" fill="none" r="6" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8.5" xlink:href="#terminal" y="-24.5"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape39_0" viewBox="-1 -16 52 18">
      <line stroke-width="1" x1="49" x2="49" y1="-6" y2="-9"/>
      <rect fill="none" height="8" stroke-width="1" width="18" x="11" y="-11"/>
      <line stroke-width="1" x1="24" x2="22" y1="-7" y2="-9"/>
      <line stroke-width="1" x1="22" x2="24" y1="-5" y2="-7"/>
      <line stroke-width="1" x1="4" x2="24" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="29" x2="43" y1="-7" y2="-7"/>
      <line stroke-width="1" x1="43" x2="43" y1="0" y2="-14"/>
      <line stroke-width="1" x1="46" x2="46" y1="-4" y2="-10"/>
      <use height="8" terminal-index="0" width="8" x="2" xlink:href="#terminal" y="-9"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape37_0" viewBox="-1 -53 17 52">
      <rect fill="none" height="19" stroke-width="2" width="8" x="3" y="-36"/>
      <line stroke-width="2" x1="10" x2="7" y1="-26" y2="-29"/>
      <line stroke-width="2" x1="8" x2="8" y1="-29" y2="-29"/>
      <line stroke-width="2" x1="4" x2="7" y1="-26" y2="-29"/>
      <line stroke-width="2" x1="7" x2="7" y1="-37" y2="-45"/>
      <line stroke-width="2" x1="7" x2="7" y1="-8" y2="-27"/>
      <line stroke-width="2" x1="14" x2="1" y1="-45" y2="-45"/>
      <line stroke-width="2" x1="10" x2="4" y1="-48" y2="-48"/>
      <line stroke-width="2" x1="9" x2="6" y1="-51" y2="-51"/>
      <use height="8" terminal-index="0" width="8" x="4" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape56_0" viewBox="-1 -41 18 42">
      <circle cx="8" cy="-20" fill="none" r="7" stroke-width="1"/>
      <line stroke-width="1" x1="8" x2="8" y1="-36" y2="-5"/>
      <use height="8" terminal-index="0" width="8" x="5" xlink:href="#terminal" y="-8"/>
      <use height="8" terminal-index="1" width="8" x="5" xlink:href="#terminal" y="-38"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape28_0" viewBox="-1 -42 25 43">
      <ellipse cx="11" cy="-12" fill="none" rx="10.5" ry="11.5" stroke-width="1"/>
      <ellipse cx="11" cy="-25" fill="none" rx="10.5" ry="11.5" stroke-width="1"/>
      <use height="8" terminal-index="0" width="8" x="9" xlink:href="#terminal" y="-39"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape40_0" viewBox="-2 -53 17 53">
      <polyline fill="none" points="10,-31 10,-31 10,-32 10,-32 10,-33 10,-33 10,-34 10,-34 9,-34 9,-35 9,-35 8,-35 8,-36 8,-36 7,-36 7,-36 6,-36 6,-36 5,-36 5,-36" stroke-width="2"/>
      <polyline fill="none" points="5,-29 5,-29 6,-29 6,-29 7,-29 7,-29 8,-29 8,-30 9,-30 9,-30 9,-30 10,-31 10,-31" stroke-width="2"/>
      <polyline fill="none" points="5,-22 5,-22 6,-22 6,-22 7,-22 7,-22 8,-22 8,-23 9,-23 9,-23 9,-23 10,-24 10,-24" stroke-width="2"/>
      <polyline fill="none" points="10,-24 10,-24 10,-25 10,-25 10,-26 10,-26 10,-27 10,-27 9,-27 9,-28 9,-28 8,-28 8,-29 8,-29 7,-29 7,-29 6,-29 6,-29 5,-29 5,-29" stroke-width="2"/>
      <polyline fill="none" points="5,-15 5,-15 6,-15 6,-15 7,-15 7,-15 8,-15 8,-16 9,-16 9,-16 9,-16 10,-17 10,-17" stroke-width="2"/>
      <polyline fill="none" points="10,-17 10,-17 10,-18 10,-18 10,-19 10,-19 10,-20 10,-20 9,-20 9,-21 9,-21 8,-21 8,-22 8,-22 7,-22 7,-22 6,-22 6,-22 5,-22 5,-22" stroke-width="2"/>
      <line stroke-width="2" x1="6" x2="6" y1="-15" y2="-6"/>
      <line stroke-width="2" x1="0" x2="13" y1="-6" y2="-6"/>
      <line stroke-width="2" x1="4" x2="9" y1="-4" y2="-4"/>
      <line stroke-width="2" x1="5" x2="8" y1="-2" y2="-2"/>
      <line stroke-width="2" x1="6" x2="6" y1="-47" y2="-36"/>
      <use height="8" terminal-index="0" width="8" x="3" xlink:href="#terminal" y="-50"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="DynamicPoint:shape48_0" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="DynamicPoint:shape48_1" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="DynamicPoint:shape48_2" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="DynamicPoint:shape48_3" viewBox="-2 -61 237 62">
      
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="DynamicPoint:shape47_0" viewBox="0 -56 108 56">
      <rect fill="none" height="52" stroke="#00ff00" stroke-width="1" width="104" x="2" y="-54"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="DynamicPoint:shape47_1" viewBox="0 -56 108 56">
      <rect fill="none" height="52" stroke="#00ff00" stroke-width="1" width="104" x="2" y="-54"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="2" id="DynamicPoint:shape47_2" viewBox="-1 -29 55 30">
      <rect fill="#ff0000" height="26" stroke="#ff0000" stroke-width="1" width="51" x="1" y="-27"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="3" id="DynamicPoint:shape47_3" viewBox="-1 -29 55 30">
      <rect fill="#ff0000" height="26" stroke="#ff0000" stroke-width="1" width="51" x="1" y="-27"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape10_0" viewBox="-1 -46 44 47">
      <ellipse cx="21" cy="-20" fill="none" rx="20" ry="19.5" stroke-width="2"/>
      <line stroke-width="2" x1="13" x2="20" y1="-27" y2="-21"/>
      <line stroke-width="2" x1="28" x2="21" y1="-27" y2="-21"/>
      <line stroke-width="2" x1="28" x2="31" y1="-26" y2="-34"/>
      <line stroke-width="2" x1="21" x2="21" y1="-12" y2="-22"/>
      <line stroke-width="2" x1="5" x2="14" y1="-24" y2="-26"/>
      <line stroke-width="2" x1="28" x2="20" y1="-7" y2="-13"/>
      <use height="8" terminal-index="0" width="8" x="18" xlink:href="#terminal" y="-43"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape52_0" viewBox="-1 -11 24 12">
      <polyline fill="none" points="1,-9 1,-4 21,-4 21,-9" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="-8"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape51_0" viewBox="0 -57 26 56">
      <line stroke-width="2" x1="13" x2="13" y1="-41" y2="-52"/>
      <line stroke-width="2" x1="13" x2="13" y1="-7" y2="-29"/>
      <line stroke-width="2" x1="24" x2="12" y1="-29" y2="-29"/>
      <polyline fill="none" points="24,-30 24,-28 24,-26 23,-25 22,-23 21,-22 20,-21 19,-20 17,-19 16,-19 14,-19 13,-19 11,-19 9,-19 8,-20 6,-21 5,-22 4,-23 3,-24 2,-26 2,-27 2,-29 2,-30 2,-32 2,-34 3,-35 4,-36 5,-38 6,-39 7,-40 8,-41 10,-41 12,-41" stroke-width="2"/>
      <use height="8" terminal-index="0" width="8" x="10" xlink:href="#terminal" y="-54"/>
      <use height="8" terminal-index="1" width="8" x="10" xlink:href="#terminal" y="-10"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Other:shape35_0" viewBox="-1 -48 43 48">
      <circle cx="15" cy="-21" fill="none" r="6.5" stroke-width="2"/>
      <ellipse cx="21" cy="-18" fill="none" rx="6.5" ry="6" stroke-width="2"/>
      <ellipse cx="14" cy="-13" fill="none" rx="6.5" ry="6" stroke-width="2"/>
      <circle cx="7" cy="-17" fill="none" r="6.5" stroke-width="2"/>
      <line stroke-width="2" x1="15" x2="15" y1="-28" y2="-43"/>
      <rect fill="none" height="14" stroke-width="2" width="6" x="32" y="-30"/>
      <line stroke-width="2" x1="34" x2="36" y1="-2" y2="-2"/>
      <line stroke-width="2" x1="33" x2="37" y1="-4" y2="-4"/>
      <line stroke-width="2" x1="30" x2="40" y1="-6" y2="-6"/>
      <line stroke-width="2" x1="35" x2="35" y1="-16" y2="-6"/>
      <line stroke-width="2" x1="15" x2="36" y1="-34" y2="-34"/>
      <line stroke-width="2" x1="35" x2="35" y1="-34" y2="-20"/>
      <line stroke-width="2" x1="34" x2="35" y1="-22" y2="-20"/>
      <line stroke-width="2" x1="35" x2="36" y1="-20" y2="-22"/>
      <use height="8" terminal-index="0" width="8" x="12" xlink:href="#terminal" y="-45"/>
    </symbol>
  </defs>
  
  <g id="HeadClass">
    <rect fill="rgb(0,0,0)" height="1844.165" stroke="none" width="2966.24" x="-207" y="-2023.165"/>
  </g>
  <g id="OtherClass">
    <ellipse cx="406.5" cy="-1842.5" fill="rgb(0,255,0)" rx="48.5" ry="26.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <circle cx="1295.637" cy="-2001.165" fill="rgb(0,255,0)" r="20.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <circle cx="822.567" cy="-2000.665" fill="rgb(255,255,0)" r="20.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <circle cx="783.567" cy="-2001.665" fill="rgb(0,255,0)" r="20.5" stroke="rgb(255,0,0)" stroke-width="1"/>
    <rect fill="rgb(170,255,170)" height="0.1" stroke="rgb(255,0,0)" stroke-width="1" width="0.1" x="24.0181" y="-1188.9"/>
    <path d="M854.553,-923.6 L854.523,-923.53 L854.453,-923.53 L854.513,-923.49 L854.493,-923.42 L854.553,-923.46 L854.603,-923.42 L854.583,-923.49 L854.643,-923.53 L854.573,-923.53 L854.553,-923.6 M854.553,-923.5" fill="rgb(170,255,170)" stroke="rgb(255,0,0)" stroke-width="5"/>
    <path d="M984.867,-284.029 L984.837,-283.959 L984.767,-283.959 L984.827,-283.919 L984.807,-283.849 L984.867,-283.889 L984.917,-283.849 L984.897,-283.919 L984.957,-283.959 L984.887,-283.959 L984.867,-284.029 M984.867,-283.929" fill="rgb(170,255,170)" stroke="rgb(255,0,0)" stroke-width="5"/>
    <circle cx="929.169" cy="-327.211" fill="rgb(170,255,170)" r="19.3289" stroke="rgb(255,0,0)" stroke-width="1" transform="matrix(1.11123,0,0,1,-274.512,-1015.36)"/>
    <circle cx="798.69" cy="-1343.5477" fill="rgb(255,255,127)" r="19.3289" stroke="rgb(255,0,0)" stroke-width="1"/>
    <circle cx="929.169" cy="-327.211" fill="rgb(170,255,170)" r="19.3289" stroke="rgb(255,0,0)" stroke-width="1" transform="matrix(1.11123,0,0,1,175.76,-1016.8)"/>
    <circle cx="1248.962" cy="-1344.9877" fill="rgb(255,255,127)" r="19.3289" stroke="rgb(255,0,0)" stroke-width="1"/>
  </g>
  <g id="MeasurementClass">
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-0" prefix="">
      <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="37" stroke="rgb(255,255,255)" x="-114.017" y="-1769.9">08:59:23</text>
      <metadata><cge:Meas_Ref ObjectID="ME-0" ObjectName="NULL.NULL"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257876" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="962" y="-1709">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257876" ObjectName="WenHuaZ.WenHuaZ_100BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257877" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="962" y="-1682">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257877" ObjectName="WenHuaZ.WenHuaZ_100BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257879" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="962" y="-1655">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257879" ObjectName="WenHuaZ.WenHuaZ_100BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257878" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="962" y="-1628">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257878" ObjectName="WenHuaZ.WenHuaZ_100BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257978" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1503.87231" y="-906.26747">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257978" ObjectName="WenHuaZ.WenHuaZ_919BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257979" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1503.87231" y="-886.26747">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257979" ObjectName="WenHuaZ.WenHuaZ_919BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257981" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1503.87231" y="-866.26747">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257981" ObjectName="WenHuaZ.WenHuaZ_919BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257980" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1503.87231" y="-846.26747">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257980" ObjectName="WenHuaZ.WenHuaZ_919BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257984" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1603.8281" y="-905.852513">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257984" ObjectName="WenHuaZ.WenHuaZ_920BK_Ia"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257985" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1603.8281" y="-885.852513">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257985" ObjectName="WenHuaZ.WenHuaZ_920BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257987" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1603.8281" y="-865.852513">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257987" ObjectName="WenHuaZ.WenHuaZ_920BK_P"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257986" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1603.8281" y="-845.852513">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257986" ObjectName="WenHuaZ.WenHuaZ_920BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259185" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1701.1081" y="-907.11999">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259185" ObjectName="WenHuaZ.WenHuaZ_921BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259186" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1701.1081" y="-887.11999">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259186" ObjectName="WenHuaZ.WenHuaZ_921BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257990" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1701.1081" y="-867.11999">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257990" ObjectName="WenHuaZ.WenHuaZ_921BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259187" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1701.1081" y="-847.11999">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259187" ObjectName="WenHuaZ.WenHuaZ_921BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257993" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1795.8532" y="-905.852513">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257993" ObjectName="WenHuaZ.WenHuaZ_922BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257994" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1795.8532" y="-885.852513">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257994" ObjectName="WenHuaZ.WenHuaZ_922BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257996" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1795.8532" y="-865.852513">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257996" ObjectName="WenHuaZ.WenHuaZ_922BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257995" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1795.8532" y="-845.852513">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257995" ObjectName="WenHuaZ.WenHuaZ_922BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258005" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2047.0693" y="-905.915884">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258005" ObjectName="WenHuaZ.WenHuaZ_924BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258006" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2047.0693" y="-885.915884">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258006" ObjectName="WenHuaZ.WenHuaZ_924BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258008" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2047.0693" y="-865.915884">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258008" ObjectName="WenHuaZ.WenHuaZ_924BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258007" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2047.0693" y="-845.915884">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258007" ObjectName="WenHuaZ.WenHuaZ_924BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257942" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="675.4792" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257942" ObjectName="WenHuaZ.WenHuaZ_912BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257943" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="675.4792" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257943" ObjectName="WenHuaZ.WenHuaZ_912BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257945" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="675.4792" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257945" ObjectName="WenHuaZ.WenHuaZ_912BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257944" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="675.4792" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257944" ObjectName="WenHuaZ.WenHuaZ_912BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257948" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="770.0711" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257948" ObjectName="WenHuaZ.WenHuaZ_913BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257949" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="770.0711" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257949" ObjectName="WenHuaZ.WenHuaZ_913BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257951" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="770.0711" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257951" ObjectName="WenHuaZ.WenHuaZ_913BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257950" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="770.0711" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257950" ObjectName="WenHuaZ.WenHuaZ_913BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259179" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1061.82623" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259179" ObjectName="WenHuaZ.WenHuaZ_915BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259180" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1061.82623" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259180" ObjectName="WenHuaZ.WenHuaZ_915BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257960" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1061.82623" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257960" ObjectName="WenHuaZ.WenHuaZ_915BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259181" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1061.82623" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259181" ObjectName="WenHuaZ.WenHuaZ_915BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257963" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1152.73168" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257963" ObjectName="WenHuaZ.WenHuaZ_916BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257964" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1152.73168" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257964" ObjectName="WenHuaZ.WenHuaZ_916BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257966" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1152.73168" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257966" ObjectName="WenHuaZ.WenHuaZ_916BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257965" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1152.73168" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257965" ObjectName="WenHuaZ.WenHuaZ_916BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257969" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1247.5346" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257969" ObjectName="WenHuaZ.WenHuaZ_917BK_Ia"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257970" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1247.5346" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257970" ObjectName="WenHuaZ.WenHuaZ_917BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257972" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1247.5346" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257972" ObjectName="WenHuaZ.WenHuaZ_917BK_P"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257971" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1247.5346" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257971" ObjectName="WenHuaZ.WenHuaZ_917BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259182" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1351.5346" y="-914.13365">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259182" ObjectName="WenHuaZ.WenHuaZ_918BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259183" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1351.5346" y="-894.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259183" ObjectName="WenHuaZ.WenHuaZ_918BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257975" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1351.5346" y="-874.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257975" ObjectName="WenHuaZ.WenHuaZ_918BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-259184" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1351.5346" y="-854.13365">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-259184" ObjectName="WenHuaZ.WenHuaZ_918BK_COS"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257922" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="575.0711" y="-914.13365">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257922" ObjectName="WenHuaZ.WenHuaZ_C01BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257923" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="575.0711" y="-894.13365">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257923" ObjectName="WenHuaZ.WenHuaZ_C01BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257924" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="575.0711" y="-874.13365">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257924" ObjectName="WenHuaZ.WenHuaZ_C01BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257925" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="575.0711" y="-854.13365">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257925" ObjectName="WenHuaZ.WenHuaZ_C01BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257910" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="19" stroke="rgb(0,255,0)" x="452.6769" y="-911.133649">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257910" ObjectName="WenHuaZ.WenHuaZ_911BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257911" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="19" stroke="rgb(0,255,0)" x="452.6769" y="-888.133649">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257911" ObjectName="WenHuaZ.WenHuaZ_911BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257913" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="19" stroke="rgb(0,255,0)" x="452.6769" y="-865.133649">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257913" ObjectName="WenHuaZ.WenHuaZ_911BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257912" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="19" stroke="rgb(0,255,0)" x="452.6769" y="-842.133649">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257912" ObjectName="WenHuaZ.WenHuaZ_911BK_COS"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257926" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2164.2915" y="-901.92335">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257926" ObjectName="WenHuaZ.WenHuaZ_C02BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257927" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2164.2915" y="-881.92335">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257927" ObjectName="WenHuaZ.WenHuaZ_C02BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257928" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2164.2915" y="-861.92335">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257928" ObjectName="WenHuaZ.WenHuaZ_C02BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257929" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2164.2915" y="-841.92335">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257929" ObjectName="WenHuaZ.WenHuaZ_C02BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257796" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="753" y="-1934">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257796" ObjectName="WenHuaZ.WenHuaZ_103BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257797" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="753" y="-1907">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257797" ObjectName="WenHuaZ.WenHuaZ_103BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257799" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="753" y="-1880">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257799" ObjectName="WenHuaZ.WenHuaZ_103BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257798" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="753" y="-1853">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257798" ObjectName="WenHuaZ.WenHuaZ_103BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257808" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1244" y="-1941">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257808" ObjectName="WenHuaZ.WenHuaZ_104BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257809" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1244" y="-1914">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257809" ObjectName="WenHuaZ.WenHuaZ_104BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257811" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1244" y="-1887">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257811" ObjectName="WenHuaZ.WenHuaZ_104BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257810" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1244" y="-1860">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257810" ObjectName="WenHuaZ.WenHuaZ_104BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257821" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="863.6582" y="-913.52397">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257821" ObjectName="WenHuaZ.WenHuaZ_901BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257823" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="863.6582" y="-893.52397">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257823" ObjectName="WenHuaZ.WenHuaZ_901BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257827" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="863.6582" y="-873.52397">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257827" ObjectName="WenHuaZ.WenHuaZ_901BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257839" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="863.6582" y="-853.52397">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257839" ObjectName="WenHuaZ.WenHuaZ_901BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258041" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1115.858" y="-273">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258041" ObjectName="WenHuaZ.WenHuaZ_931BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258042" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1115.858" y="-246">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258042" ObjectName="WenHuaZ.WenHuaZ_931BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258044" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1115.858" y="-219">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258044" ObjectName="WenHuaZ.WenHuaZ_931BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258043" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1115.858" y="-192">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258043" ObjectName="WenHuaZ.WenHuaZ_931BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258047" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1434.858" y="-273">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258047" ObjectName="WenHuaZ.WenHuaZ_932BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258048" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1434.858" y="-246">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258048" ObjectName="WenHuaZ.WenHuaZ_932BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258050" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1434.858" y="-219">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258050" ObjectName="WenHuaZ.WenHuaZ_932BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258049" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1434.858" y="-192">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258049" ObjectName="WenHuaZ.WenHuaZ_932BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257916" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="639.85803" y="-274">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257916" ObjectName="WenHuaZ.WenHuaZ_928BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257917" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="639.85803" y="-247">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257917" ObjectName="WenHuaZ.WenHuaZ_928BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257919" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="639.85803" y="-220">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257919" ObjectName="WenHuaZ.WenHuaZ_928BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257918" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="639.85803" y="-193">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257918" ObjectName="WenHuaZ.WenHuaZ_928BK_COS"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257934" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1234.858" y="-274">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257934" ObjectName="WenHuaZ.WenHuaZ_C04BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257935" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1234.858" y="-247">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257935" ObjectName="WenHuaZ.WenHuaZ_C04BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257936" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1234.858" y="-220">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257936" ObjectName="WenHuaZ.WenHuaZ_C04BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257937" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1234.858" y="-193">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257937" ObjectName="WenHuaZ.WenHuaZ_C04BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258023" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="524.6504" y="-274">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258023" ObjectName="WenHuaZ.WenHuaZ_927BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258024" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="524.6504" y="-247">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258024" ObjectName="WenHuaZ.WenHuaZ_927BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258026" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="524.6504" y="-220">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258026" ObjectName="WenHuaZ.WenHuaZ_927BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258025" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="524.6504" y="-193">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258025" ObjectName="WenHuaZ.WenHuaZ_927BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258029" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="758.85803" y="-274">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258029" ObjectName="WenHuaZ.WenHuaZ_929BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258030" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="758.85803" y="-247">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258030" ObjectName="WenHuaZ.WenHuaZ_929BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258032" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="758.85803" y="-220">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258032" ObjectName="WenHuaZ.WenHuaZ_929BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258031" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="758.85803" y="-193">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258031" ObjectName="WenHuaZ.WenHuaZ_929BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258035" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="877.85803" y="-273">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258035" ObjectName="WenHuaZ.WenHuaZ_930BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258036" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="877.85803" y="-246">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258036" ObjectName="WenHuaZ.WenHuaZ_930BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258038" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="877.85803" y="-219">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258038" ObjectName="WenHuaZ.WenHuaZ_930BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258037" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="877.85803" y="-192">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258037" ObjectName="WenHuaZ.WenHuaZ_930BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257845" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="996.858" y="-273">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257845" ObjectName="WenHuaZ.WenHuaZ_902BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257847" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="996.858" y="-246">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257847" ObjectName="WenHuaZ.WenHuaZ_902BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257851" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="996.858" y="-219">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257851" ObjectName="WenHuaZ.WenHuaZ_902BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257863" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="996.858" y="-192">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257863" ObjectName="WenHuaZ.WenHuaZ_902BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257999" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1884.2609" y="-907.11998">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257999" ObjectName="WenHuaZ.WenHuaZ_923BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258000" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1884.2609" y="-887.11998">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258000" ObjectName="WenHuaZ.WenHuaZ_923BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258002" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1884.2609" y="-867.11998">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258002" ObjectName="WenHuaZ.WenHuaZ_923BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258001" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="1884.2609" y="-847.11998">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258001" ObjectName="WenHuaZ.WenHuaZ_923BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258053" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1553.858" y="-272">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258053" ObjectName="WenHuaZ.WenHuaZ_933BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258054" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1553.858" y="-245">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258054" ObjectName="WenHuaZ.WenHuaZ_933BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258056" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1553.858" y="-218">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258056" ObjectName="WenHuaZ.WenHuaZ_933BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258055" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1553.858" y="-191">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258055" ObjectName="WenHuaZ.WenHuaZ_933BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258059" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1672.85803" y="-272">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258059" ObjectName="WenHuaZ.WenHuaZ_934BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258060" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1672.85803" y="-245">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258060" ObjectName="WenHuaZ.WenHuaZ_934BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258062" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1672.85803" y="-218">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258062" ObjectName="WenHuaZ.WenHuaZ_934BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258061" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1672.85803" y="-191">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258061" ObjectName="WenHuaZ.WenHuaZ_934BK_COS"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257938" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1334.858" y="-273">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257938" ObjectName="WenHuaZ.WenHuaZ_C05BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257939" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1334.858" y="-246">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257939" ObjectName="WenHuaZ.WenHuaZ_C05BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257940" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1334.858" y="-219">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257940" ObjectName="WenHuaZ.WenHuaZ_C05BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257941" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1334.858" y="-192">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257941" ObjectName="WenHuaZ.WenHuaZ_C05BK_Ic"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257930" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2279.1381" y="-905.91589">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257930" ObjectName="WenHuaZ.WenHuaZ_C03BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257931" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2279.1381" y="-885.91589">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257931" ObjectName="WenHuaZ.WenHuaZ_C03BK_Ia"/></metadata>
    </g>
    <g MeasureType="Ib" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257932" prefix="Ib ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2279.1381" y="-865.91589">Ib 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257932" ObjectName="WenHuaZ.WenHuaZ_C03BK_Ib"/></metadata>
    </g>
    <g MeasureType="Ic" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257933" prefix="Ic ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2279.1381" y="-845.91589">Ic 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257933" ObjectName="WenHuaZ.WenHuaZ_C03BK_Ic"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258011" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2370.0295" y="-905.91589">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258011" ObjectName="WenHuaZ.WenHuaZ_925BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258012" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2370.0295" y="-885.91589">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258012" ObjectName="WenHuaZ.WenHuaZ_925BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258014" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2370.0295" y="-865.91589">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258014" ObjectName="WenHuaZ.WenHuaZ_925BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258013" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2370.0295" y="-845.91589">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258013" ObjectName="WenHuaZ.WenHuaZ_925BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258017" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2467.9432" y="-904.58504">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258017" ObjectName="WenHuaZ.WenHuaZ_926BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258018" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2467.9432" y="-884.58504">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258018" ObjectName="WenHuaZ.WenHuaZ_926BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258020" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2467.9432" y="-864.58504">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258020" ObjectName="WenHuaZ.WenHuaZ_926BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-258019" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2467.9432" y="-844.58504">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-258019" ObjectName="WenHuaZ.WenHuaZ_926BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257898" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2579.1655" y="-904.58504">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257898" ObjectName="WenHuaZ.WenHuaZ_994BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257899" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2579.1655" y="-884.58504">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257899" ObjectName="WenHuaZ.WenHuaZ_994BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257901" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2579.1655" y="-864.58504">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257901" ObjectName="WenHuaZ.WenHuaZ_994BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257900" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="16" stroke="rgb(0,255,0)" x="2579.1655" y="-844.58504">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257900" ObjectName="WenHuaZ.WenHuaZ_994BK_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257904" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1797.858" y="-271">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257904" ObjectName="WenHuaZ.WenHuaZ_995BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257905" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1797.858" y="-244">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257905" ObjectName="WenHuaZ.WenHuaZ_995BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257907" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1797.858" y="-217">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257907" ObjectName="WenHuaZ.WenHuaZ_995BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257906" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1797.858" y="-190">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257906" ObjectName="WenHuaZ.WenHuaZ_995BK_COS"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257868" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="338" y="-1619">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257868" ObjectName="WenHuaZ.WenHuaZ_110#1_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257869" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="338" y="-1592">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257869" ObjectName="WenHuaZ.WenHuaZ_110#1_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257870" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="338" y="-1565">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257870" ObjectName="WenHuaZ.WenHuaZ_110#1_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257871" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="338" y="-1538">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257871" ObjectName="WenHuaZ.WenHuaZ_110#1_Uab"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257872" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1513.9043" y="-1630.71761">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257872" ObjectName="WenHuaZ.WenHuaZ_110#2_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257873" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1513.9043" y="-1603.71761">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257873" ObjectName="WenHuaZ.WenHuaZ_110#2_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257874" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1513.9043" y="-1576.71761">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257874" ObjectName="WenHuaZ.WenHuaZ_110#2_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257875" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1513.9043" y="-1549.71761">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257875" ObjectName="WenHuaZ.WenHuaZ_110#2_Uab"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257882" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="280" y="-1344">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257882" ObjectName="WenHuaZ.WenHuaZ_10#1_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257883" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="280" y="-1317">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257883" ObjectName="WenHuaZ.WenHuaZ_10#1_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257884" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="280" y="-1290">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257884" ObjectName="WenHuaZ.WenHuaZ_10#1_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257885" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="280" y="-1263">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257885" ObjectName="WenHuaZ.WenHuaZ_10#1_Uab"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257892" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="426.1196" y="-275.21308">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257892" ObjectName="WenHuaZ.WenHuaZ_9001BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257893" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="426.1196" y="-248.21308">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257893" ObjectName="WenHuaZ.WenHuaZ_9001BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257895" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="426.1196" y="-221.21308">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257895" ObjectName="WenHuaZ.WenHuaZ_9001BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257894" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="426.1196" y="-194.21308">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257894" ObjectName="WenHuaZ.WenHuaZ_9001BK_COS"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257887" prefix="Ua ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="266" y="-669">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257887" ObjectName="WenHuaZ.WenHuaZ_10#2_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257888" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="266" y="-642">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257888" ObjectName="WenHuaZ.WenHuaZ_10#2_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257889" prefix="Uc ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="266" y="-615">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257889" ObjectName="WenHuaZ.WenHuaZ_10#2_Uc"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257890" prefix="Uab ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="266" y="-588">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257890" ObjectName="WenHuaZ.WenHuaZ_10#2_Uab"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257820" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="696.2905" y="-1521.02235">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257820" ObjectName="WenHuaZ.WenHuaZ_1034SW_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257822" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="696.2905" y="-1494.02235">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257822" ObjectName="WenHuaZ.WenHuaZ_1034SW_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257824" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="696.2905" y="-1467.02235">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257824" ObjectName="WenHuaZ.WenHuaZ_1034SW_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257838" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="696.2905" y="-1440.02235">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257838" ObjectName="WenHuaZ.WenHuaZ_1034SW_COS"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257844" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1437.8231" y="-1524.47258">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257844" ObjectName="WenHuaZ.WenHuaZ_1044SW_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257846" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1437.8231" y="-1497.47258">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257846" ObjectName="WenHuaZ.WenHuaZ_1044SW_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257848" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1437.8231" y="-1470.47258">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257848" ObjectName="WenHuaZ.WenHuaZ_1044SW_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257862" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1437.8231" y="-1443.47258">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257862" ObjectName="WenHuaZ.WenHuaZ_1044SW_COS"/></metadata>
    </g>
    <g MeasureType="Tap" PreSymbol="0" align="1" appendix="" decimal="0" id="ME-257840" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="973.219" y="-1403.2">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257840" ObjectName="WenHuaZ.WenHuaZ_1T_Tap"/></metadata>
    </g>
    <g MeasureType="Tmp1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257841" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="973.219" y="-1376.2">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257841" ObjectName="WenHuaZ.WenHuaZ_1T_Tmp1"/></metadata>
    </g>
    <g MeasureType="Tmp2" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257842" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="973.219" y="-1349.2">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257842" ObjectName="WenHuaZ.WenHuaZ_1T_Tmp2"/></metadata>
    </g>
    <g MeasureType="WT1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257843" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="973.219" y="-1322.2">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257843" ObjectName="WenHuaZ.WenHuaZ_1T_Tmp"/></metadata>
    </g>
    <g MeasureType="Tap" PreSymbol="0" align="1" appendix="" decimal="0" id="ME-257864" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1369.37" y="-1433.77">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257864" ObjectName="WenHuaZ.WenHuaZ_2T_Tap"/></metadata>
    </g>
    <g MeasureType="Tmp1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257865" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1369.37" y="-1406.77">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257865" ObjectName="WenHuaZ.WenHuaZ_2T_Tmp1"/></metadata>
    </g>
    <g MeasureType="Tmp2" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257866" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1369.37" y="-1379.77">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257866" ObjectName="WenHuaZ.WenHuaZ_2T_Tmp2"/></metadata>
    </g>
    <g MeasureType="WT1" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-257867" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="21" stroke="rgb(0,255,0)" x="1369.37" y="-1352.77">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257867" ObjectName="WenHuaZ.WenHuaZ_2T_Tmp"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257954" prefix="P ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="19" stroke="rgb(0,255,0)" x="965.573" y="-909.809">P 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257954" ObjectName="WenHuaZ.WenHuaZ_914BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257955" prefix="Q ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="19" stroke="rgb(0,255,0)" x="965.573" y="-891.809">Q 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257955" ObjectName="WenHuaZ.WenHuaZ_914BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257957" prefix="Ia ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="19" stroke="rgb(0,255,0)" x="965.573" y="-873.809">Ia 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257957" ObjectName="WenHuaZ.WenHuaZ_914BK_Ia"/></metadata>
    </g>
    <g MeasureType="Cos" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-257956" prefix="Cos ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="19" stroke="rgb(0,255,0)" x="965.573" y="-855.809">Cos 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-257956" ObjectName="WenHuaZ.WenHuaZ_914BK_Cos"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="2" id="ME-439836" prefix="Ibh   ">
      <text fill="rgb(0,255,0)" font-family="Serif" font-size="16" stroke="rgb(0,255,0)" x="1602.92" y="-828.024">Ibh   0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-439836" ObjectName="WenHuaZ.WenHua920_Imt"/></metadata>
    </g>
  </g>
  <g id="ConnectiveNodeClass">
    <g>
      <path class="NFkV10" d="M 920,-1245.48 L 920,-1260.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58915@1" ObjectIDND1="58927@0"/></metadata>
    <path d="M 920,-1245.48 L 920,-1260.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 920,-1193 L 920,-1211.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58916@0" ObjectIDND1="58927@1"/></metadata>
    <path d="M 920,-1193 L 920,-1211.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 920,-1276.77 L 920,-1305" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58915@0"/></metadata>
    <path d="M 920,-1276.77 L 920,-1305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 823.279,-1358 L 823.279,-1332 L 869,-1332 L 869,-1141 L 920,-1141 L 920,-1175" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="58916@1" ObjectIDND1="59121@1"/></metadata>
    <path d="M 823.279,-1358 L 823.279,-1332 L 869,-1332 L 869,-1141 L 920,-1141 L 920,-1175" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 823.279,-1370.2 L 823.279,-1358" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="58916@1" ObjectIDND1="59121@1"/></metadata>
    <path d="M 823.279,-1370.2 L 823.279,-1358" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 842,-1358 L 823.279,-1358" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="xcswitch" ObjectIDND0="59121@1" ObjectIDND1="58916@1"/></metadata>
    <path d="M 842,-1358 L 823.279,-1358" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1483,-1328.28 L 1483,-1387" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="58919@1" ObjectIDND1="59122@1"/></metadata>
    <path d="M 1483,-1328.28 L 1483,-1387" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1272.24,-1372.47 L 1272.24,-1328.28 L 1431.24,-1328.28 L 2750.24,-1328.28 L 2750.24,-708.283 L 1065.81,-708.283 L 1065.81,-683.283 L 1065.81,-526 L 1021,-526" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="58919@1" ObjectIDND1="59122@1"/></metadata>
    <path d="M 1272.24,-1372.47 L 1272.24,-1328.28 L 1431.24,-1328.28 L 2750.24,-1328.28 L 2750.24,-708.283 L 1065.81,-708.283 L 1065.81,-683.283 L 1065.81,-526 L 1021,-526" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 541.033,-595.622 L 541.033,-761.519 L 1464.32,-761.519 L 1464.32,-1025.43" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59063@1" ObjectIDND1="59061@1"/></metadata>
    <path d="M 541.033,-595.622 L 541.033,-761.519 L 1464.32,-761.519 L 1464.32,-1025.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 479.166,-617.638 L 479.166,-545.342" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59063@0" ObjectIDND1="59062@1"/></metadata>
    <path d="M 479.166,-617.638 L 479.166,-545.342" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-483 L 1823,-470" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59100@1" ObjectIDND1="59101@0"/></metadata>
    <path d="M 1823,-483 L 1823,-470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-505 L 1823,-483" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59100@1" ObjectIDND1="59101@0"/></metadata>
    <path d="M 1823,-505 L 1823,-483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-528 L 1823,-505" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59100@1" ObjectIDND1="59101@0"/></metadata>
    <path d="M 1823,-528 L 1823,-505" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-505 L 1841,-505" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="59101@0" ObjectIDND1="59100@1"/></metadata>
    <path d="M 1823,-505 L 1841,-505" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-563.64 L 1823,-546" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59100@0" ObjectIDND1="58946@1"/></metadata>
    <path d="M 1823,-563.64 L 1823,-546" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-611.297 L 1823,-597.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59099@1" ObjectIDND1="58946@0"/></metadata>
    <path d="M 1823,-611.297 L 1823,-597.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1823,-658 L 1823,-627.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59099@0"/></metadata>
    <path d="M 1823,-658 L 1823,-627.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1464.81,-1207.79 L 1464.81,-1178.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="59059@1" ObjectIDND1="59061@0"/></metadata>
    <path d="M 1464.81,-1207.79 L 1464.81,-1178.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1464.81,-1255.44 L 1464.81,-1241.63" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59060@1" ObjectIDND1="59059@0"/></metadata>
    <path d="M 1464.81,-1255.44 L 1464.81,-1241.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1130 L 2601,-1117" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59096@1" ObjectIDND1="59097@0"/></metadata>
    <path d="M 2601,-1130 L 2601,-1117" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1152 L 2601,-1130" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59096@1" ObjectIDND1="59097@0"/></metadata>
    <path d="M 2601,-1152 L 2601,-1130" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2616.36,-1130 L 2601,-1130" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59096@1" ObjectIDND1="59097@0"/></metadata>
    <path d="M 2616.36,-1130 L 2601,-1130" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 2653.86,-1130 L 2668,-1130" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 2653.86,-1130 L 2668,-1130" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 2668,-1130 L 2687,-1130 L 2687,-1108" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 2668,-1130 L 2687,-1130 L 2687,-1108" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 2668,-1109 L 2668,-1130" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 2668,-1109 L 2668,-1130" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1175 L 2601,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59096@1" ObjectIDND1="59097@0"/></metadata>
    <path d="M 2601,-1175 L 2601,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1152 L 2619,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="59097@0" ObjectIDND1="59096@1"/></metadata>
    <path d="M 2601,-1152 L 2619,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1210.64 L 2601,-1193" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59096@0" ObjectIDND1="58942@1"/></metadata>
    <path d="M 2601,-1210.64 L 2601,-1193" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1258.3 L 2601,-1244.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59095@1" ObjectIDND1="58942@0"/></metadata>
    <path d="M 2601,-1258.3 L 2601,-1244.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2601,-1305 L 2601,-1274.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59095@0"/></metadata>
    <path d="M 2601,-1305 L 2601,-1274.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1206.64 L 2495,-1188" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59009@0" ObjectIDND1="59036@1"/></metadata>
    <path d="M 2495,-1206.64 L 2495,-1188" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1240.48 L 2495,-1255.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59008@1" ObjectIDND1="59036@0"/></metadata>
    <path d="M 2495,-1240.48 L 2495,-1255.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1206.64 L 2405,-1187" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59004@0" ObjectIDND1="59031@1"/></metadata>
    <path d="M 2405,-1206.64 L 2405,-1187" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1254.3 L 2405,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59003@1" ObjectIDND1="59031@0"/></metadata>
    <path d="M 2405,-1254.3 L 2405,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1149 L 2495,-1114" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61857@0" ObjectIDND1="59009@1" ObjectIDND2="59010@0"/></metadata>
    <path d="M 2495,-1149 L 2495,-1114" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1170 L 2495,-1149" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59009@1" ObjectIDND1="61857@0" ObjectIDND2="59010@0"/></metadata>
    <path d="M 2495,-1170 L 2495,-1149" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1149 L 2513,-1149" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59010@0" ObjectIDND1="61857@0" ObjectIDND2="59009@1"/></metadata>
    <path d="M 2495,-1149 L 2513,-1149" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2495,-1305 L 2495,-1271.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59008@0"/></metadata>
    <path d="M 2495,-1305 L 2495,-1271.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1148 L 2405,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61856@0" ObjectIDND1="59004@1" ObjectIDND2="59005@0"/></metadata>
    <path d="M 2405,-1148 L 2405,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1169 L 2405,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59004@1" ObjectIDND1="61856@0" ObjectIDND2="59005@0"/></metadata>
    <path d="M 2405,-1169 L 2405,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1148 L 2423,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59005@0" ObjectIDND1="61856@0" ObjectIDND2="59004@1"/></metadata>
    <path d="M 2405,-1148 L 2423,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2405,-1305 L 2405,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59003@0"/></metadata>
    <path d="M 2405,-1305 L 2405,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1088 L 2299.85,-1070.56" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59080@1" ObjectIDND1="59081@0" ObjectIDND2="59129@0"/></metadata>
    <path d="M 2300,-1088 L 2299.85,-1070.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1102.73 L 2300,-1088" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59080@1" ObjectIDND1="59081@0" ObjectIDND2="59129@0"/></metadata>
    <path d="M 2300,-1102.73 L 2300,-1088" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1088 L 2318,-1088" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59081@0" ObjectIDND1="59080@1" ObjectIDND2="59129@0"/></metadata>
    <path d="M 2300,-1088 L 2318,-1088" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1154 L 2300,-1136.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="59080@0" ObjectIDND1="59078@1" ObjectIDND2="59079@0"/></metadata>
    <path d="M 2300,-1154 L 2300,-1136.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1175 L 2300,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="59078@1" ObjectIDND1="59080@0" ObjectIDND2="59079@0"/></metadata>
    <path d="M 2300,-1175 L 2300,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1154 L 2318,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="59079@0" ObjectIDND1="59080@0" ObjectIDND2="59078@1"/></metadata>
    <path d="M 2300,-1154 L 2318,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1210.76 L 2300,-1193" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59078@0" ObjectIDND1="58957@1"/></metadata>
    <path d="M 2300,-1210.76 L 2300,-1193" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1258.3 L 2300,-1245.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59077@1" ObjectIDND1="58957@0"/></metadata>
    <path d="M 2300,-1258.3 L 2300,-1245.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2300,-1305 L 2300,-1274.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59077@0"/></metadata>
    <path d="M 2300,-1305 L 2300,-1274.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-509 L 1370,-492.131" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="59092@0" ObjectIDND1="59090@1" ObjectIDND2="59091@0"/></metadata>
    <path d="M 1370,-509 L 1370,-492.131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-529 L 1370,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="59090@1" ObjectIDND1="59092@0" ObjectIDND2="59091@0"/></metadata>
    <path d="M 1370,-529 L 1370,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1393,-509 L 1370,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="59091@0" ObjectIDND1="59092@0" ObjectIDND2="59090@1"/></metadata>
    <path d="M 1393,-509 L 1370,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-564.64 L 1370,-547" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59090@0" ObjectIDND1="58962@1"/></metadata>
    <path d="M 1370,-564.64 L 1370,-547" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-612.297 L 1370,-598.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59089@1" ObjectIDND1="58962@0"/></metadata>
    <path d="M 1370,-612.297 L 1370,-598.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-658 L 1370,-628.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59089@0"/></metadata>
    <path d="M 1370,-658 L 1370,-628.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-438 L 1370,-426" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="59131@0" ObjectIDND1="59092@1" ObjectIDND2="59093@0"/></metadata>
    <path d="M 1370,-438 L 1370,-426" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-458.732 L 1370,-438" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="capacitor" DevType2="switch" ObjectIDND0="59092@1" ObjectIDND1="59131@0" ObjectIDND2="59093@0"/></metadata>
    <path d="M 1370,-458.732 L 1370,-438" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1370,-438 L 1395,-438" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="capacitor" DevType2="switch" ObjectIDND0="59093@0" ObjectIDND1="59131@0" ObjectIDND2="59092@1"/></metadata>
    <path d="M 1370,-438 L 1395,-438" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-510 L 1712,-475" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61865@0" ObjectIDND1="59044@1" ObjectIDND2="59045@0"/></metadata>
    <path d="M 1712,-510 L 1712,-475" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-533 L 1712,-510" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59044@1" ObjectIDND1="61865@0" ObjectIDND2="59045@0"/></metadata>
    <path d="M 1712,-533 L 1712,-510" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-510 L 1730,-510" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59045@0" ObjectIDND1="61865@0" ObjectIDND2="59044@1"/></metadata>
    <path d="M 1712,-510 L 1730,-510" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-568.64 L 1712,-551" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59044@0" ObjectIDND1="59094@1"/></metadata>
    <path d="M 1712,-568.64 L 1712,-551" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-616.297 L 1712,-602.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59043@1" ObjectIDND1="59094@0"/></metadata>
    <path d="M 1712,-616.297 L 1712,-602.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,-658 L 1712,-632.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59043@0"/></metadata>
    <path d="M 1712,-658 L 1712,-632.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-510 L 1623,-475" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61864@0" ObjectIDND1="59039@1" ObjectIDND2="59040@0"/></metadata>
    <path d="M 1623,-510 L 1623,-475" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-533 L 1623,-510" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59039@1" ObjectIDND1="61864@0" ObjectIDND2="59040@0"/></metadata>
    <path d="M 1623,-533 L 1623,-510" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-510 L 1641,-510" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59040@0" ObjectIDND1="61864@0" ObjectIDND2="59039@1"/></metadata>
    <path d="M 1623,-510 L 1641,-510" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-568.64 L 1623,-551" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59039@0" ObjectIDND1="59082@1"/></metadata>
    <path d="M 1623,-568.64 L 1623,-551" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-616.297 L 1623,-602.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59038@1" ObjectIDND1="59082@0"/></metadata>
    <path d="M 1623,-616.297 L 1623,-602.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1623,-658 L 1623,-632.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59038@0"/></metadata>
    <path d="M 1623,-658 L 1623,-632.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1416 L 1171,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58933@0" ObjectIDND1="59122@2"/></metadata>
    <path d="M 1171,-1416 L 1171,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1367 L 1171,-1386" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58933@0" ObjectIDND1="59122@2"/></metadata>
    <path d="M 1171,-1367 L 1171,-1386" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1419 L 724,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 724,-1419 L 724,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1373 L 724,-1389" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 724,-1373 L 724,-1389" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 724,-1430.83 L 691,-1430.83 L 691,-1414" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 724,-1430.83 L 691,-1430.83 L 691,-1414" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 751,-1430.83 L 724,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 751,-1430.83 L 724,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1154 L 599,-1137.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="59068@0" ObjectIDND1="59066@1" ObjectIDND2="59067@0"/></metadata>
    <path d="M 599,-1154 L 599,-1137.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1176 L 599,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="59066@1" ObjectIDND1="59068@0" ObjectIDND2="59067@0"/></metadata>
    <path d="M 599,-1176 L 599,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 617,-1154 L 599,-1154" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="59067@0" ObjectIDND1="59068@0" ObjectIDND2="59066@1"/></metadata>
    <path d="M 617,-1154 L 599,-1154" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1536 L 822,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" DevType2="switch" ObjectIDND0="58901@-1" ObjectIDND1="58924@0" ObjectIDND2="58926@0"/></metadata>
    <path d="M 822,-1536 L 822,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1522.13 L 822,-1536" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" DevType2="switch" ObjectIDND0="58924@0" ObjectIDND1="58901@-1" ObjectIDND2="58926@0"/></metadata>
    <path d="M 822,-1522.13 L 822,-1536" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 857,-1536 L 822,-1536" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" DevType2="switch" ObjectIDND0="58926@0" ObjectIDND1="58901@-1" ObjectIDND2="58924@0"/></metadata>
    <path d="M 857,-1536 L 822,-1536" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-483 L 678,-470" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59108@1" ObjectIDND1="59109@0"/></metadata>
    <path d="M 678,-483 L 678,-470" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-505 L 678,-483" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59108@1" ObjectIDND1="59109@0"/></metadata>
    <path d="M 678,-505 L 678,-483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 694.361,-483 L 678,-483" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59108@1" ObjectIDND1="59109@0"/></metadata>
    <path d="M 694.361,-483 L 678,-483" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 694,-1763 L 729,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58909@0" ObjectIDND1="59119@0"/></metadata>
    <path d="M 694,-1763 L 729,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 657,-1717 L 657,-1763 L 694,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58909@0" ObjectIDND1="59119@0"/></metadata>
    <path d="M 657,-1717 L 657,-1763 L 694,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 694,-1719 L 694,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58909@0" ObjectIDND1="59119@0"/></metadata>
    <path d="M 694,-1719 L 694,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 729,-1763 L 746.361,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58909@0" ObjectIDND1="59119@0"/></metadata>
    <path d="M 729,-1763 L 746.361,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 729,-1723 L 729,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="59119@0" ObjectIDND1="58909@0"/></metadata>
    <path d="M 729,-1723 L 729,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1222,-1759 L 1239.36,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58920@0" ObjectIDND1="59120@0"/></metadata>
    <path d="M 1222,-1759 L 1239.36,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1759 L 1222,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58920@0" ObjectIDND1="59120@0"/></metadata>
    <path d="M 1187,-1759 L 1222,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1222,-1721 L 1222,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="59120@0" ObjectIDND1="58920@0"/></metadata>
    <path d="M 1222,-1721 L 1222,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1621 L 1298,-1636.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58913@1" ObjectIDND1="58914@0" ObjectIDND2="58921@0"/></metadata>
    <path d="M 1298,-1621 L 1298,-1636.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1606.13 L 1298,-1621" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58914@0" ObjectIDND1="58913@1" ObjectIDND2="58921@0"/></metadata>
    <path d="M 1298,-1606.13 L 1298,-1621" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1329,-1621 L 1298,-1621" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58921@0" ObjectIDND1="58913@1" ObjectIDND2="58914@0"/></metadata>
    <path d="M 1329,-1621 L 1298,-1621" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1687 L 1298,-1701.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58917@1" ObjectIDND1="58913@0" ObjectIDND2="58922@0"/></metadata>
    <path d="M 1298,-1687 L 1298,-1701.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1670.48 L 1298,-1687" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58913@0" ObjectIDND1="58917@1" ObjectIDND2="58922@0"/></metadata>
    <path d="M 1298,-1670.48 L 1298,-1687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1329,-1687 L 1298,-1687" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="58922@0" ObjectIDND1="58917@1" ObjectIDND2="58913@0"/></metadata>
    <path d="M 1329,-1687 L 1298,-1687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1276.86,-1759 L 1298,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58920@1" ObjectIDND1="58917@0" ObjectIDND2="58923@0" ObjectIDND3="62249@0"/></metadata>
    <path d="M 1276.86,-1759 L 1298,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1759 L 1298,-1786" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58920@1" ObjectIDND1="58917@0" ObjectIDND2="58923@0" ObjectIDND3="62249@0"/></metadata>
    <path d="M 1298,-1759 L 1298,-1786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1735.13 L 1298,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58917@0" ObjectIDND1="58920@1" ObjectIDND2="58923@0" ObjectIDND3="62249@0"/></metadata>
    <path d="M 1298,-1735.13 L 1298,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1327,-1759 L 1298,-1759" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58923@0" ObjectIDND1="58920@1" ObjectIDND2="58917@0" ObjectIDND3="62249@0"/></metadata>
    <path d="M 1327,-1759 L 1298,-1759" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 783.861,-1763 L 804,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58909@1" ObjectIDND1="58908@0" ObjectIDND2="58912@0" ObjectIDND3="62250@0"/></metadata>
    <path d="M 783.861,-1763 L 804,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1763 L 804,-1781" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58909@1" ObjectIDND1="58908@0" ObjectIDND2="58912@0" ObjectIDND3="62250@0"/></metadata>
    <path d="M 804,-1763 L 804,-1781" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1737.13 L 804,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58908@0" ObjectIDND1="58909@1" ObjectIDND2="58912@0" ObjectIDND3="62250@0"/></metadata>
    <path d="M 804,-1737.13 L 804,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 834,-1763 L 804,-1763" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="58912@0" ObjectIDND1="58909@1" ObjectIDND2="58908@0" ObjectIDND3="62250@0"/></metadata>
    <path d="M 834,-1763 L 804,-1763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1688 L 804,-1703.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58908@1" ObjectIDND1="58906@0" ObjectIDND2="58911@0"/></metadata>
    <path d="M 804,-1688 L 804,-1703.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1673.48 L 804,-1688" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58906@0" ObjectIDND1="58908@1" ObjectIDND2="58911@0"/></metadata>
    <path d="M 804,-1673.48 L 804,-1688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 835,-1688 L 804,-1688" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="58911@0" ObjectIDND1="58908@1" ObjectIDND2="58906@0"/></metadata>
    <path d="M 835,-1688 L 804,-1688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1622 L 804,-1639.64" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58906@1" ObjectIDND1="58907@0" ObjectIDND2="58910@0"/></metadata>
    <path d="M 804,-1622 L 804,-1639.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1607.13 L 804,-1622" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58907@0" ObjectIDND1="58906@1" ObjectIDND2="58910@0"/></metadata>
    <path d="M 804,-1607.13 L 804,-1622" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 835,-1622 L 804,-1622" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58910@0" ObjectIDND1="58906@1" ObjectIDND2="58907@0"/></metadata>
    <path d="M 835,-1622 L 804,-1622" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 804,-1573.73 L 804,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="58901@-1" ObjectIDND1="58907@1"/></metadata>
    <path d="M 804,-1573.73 L 804,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1071,-1550 L 1082.36,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58936@0" ObjectIDND1="58934@1" ObjectIDND2="58941@0"/></metadata>
    <path d="M 1071,-1550 L 1082.36,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1057.32,-1550 L 1071,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58934@1" ObjectIDND1="58936@0" ObjectIDND2="58941@0"/></metadata>
    <path d="M 1057.32,-1550 L 1071,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1071,-1514 L 1071,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="breaker" ObjectIDND0="58941@0" ObjectIDND1="58936@0" ObjectIDND2="58934@1"/></metadata>
    <path d="M 1071,-1514 L 1071,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1119.86,-1550 L 1148,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="58902@-1" ObjectIDND1="58936@1"/></metadata>
    <path d="M 1119.86,-1550 L 1148,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 959.361,-1550 L 925,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="58901@-1" ObjectIDND1="58935@0"/></metadata>
    <path d="M 959.361,-1550 L 925,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-508 L 1225,-491.131" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="59086@0" ObjectIDND1="59084@1" ObjectIDND2="59085@0"/></metadata>
    <path d="M 1225,-508 L 1225,-491.131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-528 L 1225,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="59084@1" ObjectIDND1="59086@0" ObjectIDND2="59085@0"/></metadata>
    <path d="M 1225,-528 L 1225,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1248,-508 L 1225,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="59085@0" ObjectIDND1="59086@0" ObjectIDND2="59084@1"/></metadata>
    <path d="M 1248,-508 L 1225,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1203.64 L 1894,-1185" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58994@0" ObjectIDND1="59021@1"/></metadata>
    <path d="M 1894,-1203.64 L 1894,-1185" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1237.48 L 1894,-1252.3" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58993@1" ObjectIDND1="59021@0"/></metadata>
    <path d="M 1894,-1237.48 L 1894,-1252.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1203.64 L 1804,-1184" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58989@0" ObjectIDND1="59016@1"/></metadata>
    <path d="M 1804,-1203.64 L 1804,-1184" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1251.3 L 1804,-1237.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58988@1" ObjectIDND1="59016@0"/></metadata>
    <path d="M 1804,-1251.3 L 1804,-1237.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1206.64 L 1361,-1185" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58969@0" ObjectIDND1="58996@1"/></metadata>
    <path d="M 1361,-1206.64 L 1361,-1185" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1252.3 L 1361,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58968@1" ObjectIDND1="58996@0"/></metadata>
    <path d="M 1361,-1252.3 L 1361,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1021,-563.64 L 1021,-544" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58919@0" ObjectIDND1="58932@1"/></metadata>
    <path d="M 1021,-563.64 L 1021,-544" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1021,-611.297 L 1021,-597.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58918@1" ObjectIDND1="58932@0"/></metadata>
    <path d="M 1021,-611.297 L 1021,-597.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1021,-658 L 1021,-627.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="58918@0"/></metadata>
    <path d="M 1021,-658 L 1021,-627.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1962,-522 L 1962,-493" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="59056@1"/></metadata>
    <path d="M 1962,-522 L 1962,-493" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1962,-610.297 L 1962,-540" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59056@0" ObjectIDND1="59055@1"/></metadata>
    <path d="M 1962,-610.297 L 1962,-540" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1962,-658 L 1962,-626.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59055@0"/></metadata>
    <path d="M 1962,-658 L 1962,-626.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-507 L 906,-472" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61861@0" ObjectIDND1="59024@1" ObjectIDND2="59025@0"/></metadata>
    <path d="M 906,-507 L 906,-472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-530 L 906,-507" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59024@1" ObjectIDND1="61861@0" ObjectIDND2="59025@0"/></metadata>
    <path d="M 906,-530 L 906,-507" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-507 L 924,-507" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59025@0" ObjectIDND1="61861@0" ObjectIDND2="59024@1"/></metadata>
    <path d="M 906,-507 L 924,-507" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-565.64 L 906,-548" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59024@0" ObjectIDND1="59048@1"/></metadata>
    <path d="M 906,-565.64 L 906,-548" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-613.297 L 906,-599.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59023@1" ObjectIDND1="59048@0"/></metadata>
    <path d="M 906,-613.297 L 906,-599.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 906,-658 L 906,-629.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59023@0"/></metadata>
    <path d="M 906,-658 L 906,-629.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-508 L 814,-473" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61860@0" ObjectIDND1="59019@1" ObjectIDND2="59020@0"/></metadata>
    <path d="M 814,-508 L 814,-473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-531 L 814,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59019@1" ObjectIDND1="61860@0" ObjectIDND2="59020@0"/></metadata>
    <path d="M 814,-531 L 814,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-508 L 832,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59020@0" ObjectIDND1="61860@0" ObjectIDND2="59019@1"/></metadata>
    <path d="M 814,-508 L 832,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-566.64 L 814,-549" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59019@0" ObjectIDND1="59046@1"/></metadata>
    <path d="M 814,-566.64 L 814,-549" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-614.297 L 814,-600.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59018@1" ObjectIDND1="59046@0"/></metadata>
    <path d="M 814,-614.297 L 814,-600.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 814,-658 L 814,-630.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59018@0"/></metadata>
    <path d="M 814,-658 L 814,-630.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-508 L 582,-473" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61858@0" ObjectIDND1="59014@1" ObjectIDND2="59015@0"/></metadata>
    <path d="M 582,-508 L 582,-473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-531 L 582,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59014@1" ObjectIDND1="61858@0" ObjectIDND2="59015@0"/></metadata>
    <path d="M 582,-531 L 582,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-508 L 600,-508" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59015@0" ObjectIDND1="61858@0" ObjectIDND2="59014@1"/></metadata>
    <path d="M 582,-508 L 600,-508" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-566.64 L 582,-549" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59014@0" ObjectIDND1="59041@1"/></metadata>
    <path d="M 582,-566.64 L 582,-549" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-614.297 L 582,-600.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59013@1" ObjectIDND1="59041@0"/></metadata>
    <path d="M 582,-614.297 L 582,-600.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 582,-658 L 582,-630.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59013@0"/></metadata>
    <path d="M 582,-658 L 582,-630.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-563.64 L 1225,-546" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59084@0" ObjectIDND1="58961@1"/></metadata>
    <path d="M 1225,-563.64 L 1225,-546" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-611.297 L 1225,-597.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59083@1" ObjectIDND1="58961@0"/></metadata>
    <path d="M 1225,-611.297 L 1225,-597.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-658 L 1225,-627.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59083@0"/></metadata>
    <path d="M 1225,-658 L 1225,-627.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-528 L 678,-505" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59108@1" ObjectIDND1="59109@0"/></metadata>
    <path d="M 678,-528 L 678,-505" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-505 L 696,-505" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="59109@0" ObjectIDND1="59108@1"/></metadata>
    <path d="M 678,-505 L 696,-505" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-562.64 L 678,-546" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59108@0" ObjectIDND1="58951@1"/></metadata>
    <path d="M 678,-562.64 L 678,-546" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-611.297 L 678,-596.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59107@1" ObjectIDND1="58951@0"/></metadata>
    <path d="M 678,-611.297 L 678,-596.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 678,-658 L 678,-627.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59107@0"/></metadata>
    <path d="M 678,-658 L 678,-627.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-509 L 1517,-474" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61863@0" ObjectIDND1="59034@1" ObjectIDND2="59035@0"/></metadata>
    <path d="M 1517,-509 L 1517,-474" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-532 L 1517,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59034@1" ObjectIDND1="61863@0" ObjectIDND2="59035@0"/></metadata>
    <path d="M 1517,-532 L 1517,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-509 L 1535,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59035@0" ObjectIDND1="61863@0" ObjectIDND2="59034@1"/></metadata>
    <path d="M 1517,-509 L 1535,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-567.64 L 1517,-550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59034@0" ObjectIDND1="59070@1"/></metadata>
    <path d="M 1517,-567.64 L 1517,-550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-615.297 L 1517,-601.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59033@1" ObjectIDND1="59070@0"/></metadata>
    <path d="M 1517,-615.297 L 1517,-601.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1517,-658 L 1517,-631.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59033@0"/></metadata>
    <path d="M 1517,-658 L 1517,-631.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-509 L 1111,-474" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61862@0" ObjectIDND1="59029@1" ObjectIDND2="59030@0"/></metadata>
    <path d="M 1111,-509 L 1111,-474" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-532 L 1111,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="59029@1" ObjectIDND1="61862@0" ObjectIDND2="59030@0"/></metadata>
    <path d="M 1111,-532 L 1111,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-509 L 1129,-509" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59030@0" ObjectIDND1="61862@0" ObjectIDND2="59029@1"/></metadata>
    <path d="M 1111,-509 L 1129,-509" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-567.64 L 1111,-550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59029@0" ObjectIDND1="59050@1"/></metadata>
    <path d="M 1111,-567.64 L 1111,-550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-615.297 L 1111,-601.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59028@1" ObjectIDND1="59050@0"/></metadata>
    <path d="M 1111,-615.297 L 1111,-601.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1111,-658 L 1111,-631.766" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58904@-1" ObjectIDND1="59028@0"/></metadata>
    <path d="M 1111,-658 L 1111,-631.766" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1473 L 1271.01,-1483.73" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="58929@1" ObjectIDND1="58930@0" ObjectIDND2="59122@0"/></metadata>
    <path d="M 1271.01,-1473 L 1271.01,-1483.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1308,-1473 L 1271.01,-1473" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="58930@0" ObjectIDND1="58929@1" ObjectIDND2="59122@0"/></metadata>
    <path d="M 1308,-1473 L 1271.01,-1473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1473 L 1271.01,-1447.58" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" DevType2="switch" ObjectIDND0="59122@0" ObjectIDND1="58929@1" ObjectIDND2="58930@0"/></metadata>
    <path d="M 1271.01,-1473 L 1271.01,-1447.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1298,-1572.73 L 1298,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="58902@-1" ObjectIDND1="58914@1"/></metadata>
    <path d="M 1298,-1572.73 L 1298,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1472 L 856,-1472" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="58925@0" ObjectIDND1="58924@1" ObjectIDND2="59121@0"/></metadata>
    <path d="M 822,-1472 L 856,-1472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1488.73 L 822,-1472" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="58924@1" ObjectIDND1="58925@0" ObjectIDND2="59121@0"/></metadata>
    <path d="M 822,-1488.73 L 822,-1472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1472 L 822,-1448.4" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="transformer2" ObjectIDND0="58925@0" ObjectIDND1="58924@1" ObjectIDND2="59121@0"/></metadata>
    <path d="M 822,-1472 L 822,-1448.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 523.595,-1115.25 L 537.734,-1115.25" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 523.595,-1115.25 L 537.734,-1115.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 537.734,-1094.25 L 537.734,-1115.25" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 537.734,-1094.25 L 537.734,-1115.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1759 L 1187,-1717" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58920@0" ObjectIDND1="59120@0"/></metadata>
    <path d="M 1187,-1759 L 1187,-1717" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1187,-1759 L 1150,-1759 L 1150,-1715" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" ObjectIDND0="58920@0" ObjectIDND1="59120@0"/></metadata>
    <path d="M 1187,-1759 L 1150,-1759 L 1150,-1715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1089 L 2176.54,-1072.9" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59074@1" ObjectIDND1="59075@0" ObjectIDND2="59128@0"/></metadata>
    <path d="M 2176,-1089 L 2176.54,-1072.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1103.73 L 2176,-1089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59074@1" ObjectIDND1="59075@0" ObjectIDND2="59128@0"/></metadata>
    <path d="M 2176,-1103.73 L 2176,-1089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1089 L 2194,-1089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59075@0" ObjectIDND1="59074@1" ObjectIDND2="59128@0"/></metadata>
    <path d="M 2176,-1089 L 2194,-1089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1089 L 599,-1074" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59068@1" ObjectIDND1="59069@0" ObjectIDND2="59127@0"/></metadata>
    <path d="M 599,-1089 L 599,-1074" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1103.73 L 599,-1089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59068@1" ObjectIDND1="59069@0" ObjectIDND2="59127@0"/></metadata>
    <path d="M 599,-1103.73 L 599,-1089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1089 L 618,-1089" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59069@0" ObjectIDND1="59068@1" ObjectIDND2="59127@0"/></metadata>
    <path d="M 599,-1089 L 618,-1089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1530 L 1271.01,-1517.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" DevType2="switch" ObjectIDND0="58929@0" ObjectIDND1="58902@-1" ObjectIDND2="58931@0"/></metadata>
    <path d="M 1271.01,-1530 L 1271.01,-1517.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1550 L 1271.01,-1530" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" DevType2="switch" ObjectIDND0="58902@-1" ObjectIDND1="58929@0" ObjectIDND2="58931@0"/></metadata>
    <path d="M 1271.01,-1550 L 1271.01,-1530" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1530 L 1304,-1530" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="bus" ObjectIDND0="58931@0" ObjectIDND1="58929@0" ObjectIDND2="58902@-1"/></metadata>
    <path d="M 1271.01,-1530 L 1304,-1530" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1155 L 2176,-1137.13" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="59074@0" ObjectIDND1="59072@1" ObjectIDND2="59073@0"/></metadata>
    <path d="M 2176,-1155 L 2176,-1137.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1176 L 2176,-1155" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" DevType2="switch" ObjectIDND0="59072@1" ObjectIDND1="59074@0" ObjectIDND2="59073@0"/></metadata>
    <path d="M 2176,-1176 L 2176,-1155" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1155 L 2194,-1155" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="xcswitch" ObjectIDND0="59073@0" ObjectIDND1="59074@0" ObjectIDND2="59072@1"/></metadata>
    <path d="M 2176,-1155 L 2194,-1155" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1211.76 L 2176,-1194" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59072@0" ObjectIDND1="58956@1"/></metadata>
    <path d="M 2176,-1211.76 L 2176,-1194" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1259.3 L 2176,-1246.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59071@1" ObjectIDND1="58956@0"/></metadata>
    <path d="M 2176,-1259.3 L 2176,-1246.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2176,-1305 L 2176,-1275.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59071@0"/></metadata>
    <path d="M 2176,-1305 L 2176,-1275.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1175 L 485,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="switch" ObjectIDND0="59104@1" ObjectIDND1="59105@0"/></metadata>
    <path d="M 485,-1175 L 485,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1152 L 503,-1152" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" ObjectIDND0="59105@0" ObjectIDND1="59104@1"/></metadata>
    <path d="M 485,-1152 L 503,-1152" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1210.64 L 485,-1193" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59104@0" ObjectIDND1="58947@1"/></metadata>
    <path d="M 485,-1210.64 L 485,-1193" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1258.3 L 485,-1244.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59103@1" ObjectIDND1="58947@0"/></metadata>
    <path d="M 485,-1258.3 L 485,-1244.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 485,-1305 L 485,-1274.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59103@0"/></metadata>
    <path d="M 485,-1305 L 485,-1274.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1211.76 L 599,-1194" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59066@0" ObjectIDND1="58952@1"/></metadata>
    <path d="M 599,-1211.76 L 599,-1194" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1259.3 L 599,-1246.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="59065@1" ObjectIDND1="58952@0"/></metadata>
    <path d="M 599,-1259.3 L 599,-1246.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 599,-1305 L 599,-1275.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59065@0"/></metadata>
    <path d="M 599,-1305 L 599,-1275.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1146 L 1361,-1111" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61849@0" ObjectIDND1="58969@1" ObjectIDND2="58970@0"/></metadata>
    <path d="M 1361,-1146 L 1361,-1111" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1167 L 1361,-1146" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58969@1" ObjectIDND1="61849@0" ObjectIDND2="58970@0"/></metadata>
    <path d="M 1361,-1167 L 1361,-1146" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1146 L 1379,-1146" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58970@0" ObjectIDND1="61849@0" ObjectIDND2="58969@1"/></metadata>
    <path d="M 1361,-1146 L 1379,-1146" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1361,-1305 L 1361,-1268.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58968@0"/></metadata>
    <path d="M 1361,-1305 L 1361,-1268.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1148 L 1275,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61848@0" ObjectIDND1="58964@1" ObjectIDND2="58965@0"/></metadata>
    <path d="M 1275,-1148 L 1275,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1171 L 1275,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58964@1" ObjectIDND1="61848@0" ObjectIDND2="58965@0"/></metadata>
    <path d="M 1275,-1171 L 1275,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1148 L 1293,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58965@0" ObjectIDND1="61848@0" ObjectIDND2="58964@1"/></metadata>
    <path d="M 1275,-1148 L 1293,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1206.64 L 1275,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58964@0" ObjectIDND1="58991@1"/></metadata>
    <path d="M 1275,-1206.64 L 1275,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1254.3 L 1275,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58963@1" ObjectIDND1="58991@0"/></metadata>
    <path d="M 1275,-1254.3 L 1275,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1275,-1305 L 1275,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58963@0"/></metadata>
    <path d="M 1275,-1305 L 1275,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1148 L 1185,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61847@0" ObjectIDND1="58959@1" ObjectIDND2="58960@0"/></metadata>
    <path d="M 1185,-1148 L 1185,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1171 L 1185,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58959@1" ObjectIDND1="61847@0" ObjectIDND2="58960@0"/></metadata>
    <path d="M 1185,-1171 L 1185,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1148 L 1203,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58960@0" ObjectIDND1="61847@0" ObjectIDND2="58959@1"/></metadata>
    <path d="M 1185,-1148 L 1203,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1206.64 L 1185,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58959@0" ObjectIDND1="58986@1"/></metadata>
    <path d="M 1185,-1206.64 L 1185,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1254.3 L 1185,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58958@1" ObjectIDND1="58986@0"/></metadata>
    <path d="M 1185,-1254.3 L 1185,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1185,-1305 L 1185,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58958@0"/></metadata>
    <path d="M 1185,-1305 L 1185,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1149 L 1096,-1114" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61846@0" ObjectIDND1="58954@1" ObjectIDND2="58955@0"/></metadata>
    <path d="M 1096,-1149 L 1096,-1114" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1172 L 1096,-1149" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58954@1" ObjectIDND1="61846@0" ObjectIDND2="58955@0"/></metadata>
    <path d="M 1096,-1172 L 1096,-1149" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1149 L 1114,-1149" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58955@0" ObjectIDND1="61846@0" ObjectIDND2="58954@1"/></metadata>
    <path d="M 1096,-1149 L 1114,-1149" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1207.64 L 1096,-1190" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58954@0" ObjectIDND1="58981@1"/></metadata>
    <path d="M 1096,-1207.64 L 1096,-1190" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1255.3 L 1096,-1241.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58953@1" ObjectIDND1="58981@0"/></metadata>
    <path d="M 1096,-1255.3 L 1096,-1241.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1096,-1305 L 1096,-1271.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58953@0"/></metadata>
    <path d="M 1096,-1305 L 1096,-1271.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1148 L 1010,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61845@0" ObjectIDND1="58949@1" ObjectIDND2="58950@0"/></metadata>
    <path d="M 1010,-1148 L 1010,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1171 L 1010,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58949@1" ObjectIDND1="61845@0" ObjectIDND2="58950@0"/></metadata>
    <path d="M 1010,-1171 L 1010,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1148 L 1028,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58950@0" ObjectIDND1="61845@0" ObjectIDND2="58949@1"/></metadata>
    <path d="M 1010,-1148 L 1028,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1206.64 L 1010,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58949@0" ObjectIDND1="58976@1"/></metadata>
    <path d="M 1010,-1206.64 L 1010,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1254.3 L 1010,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58948@1" ObjectIDND1="58976@0"/></metadata>
    <path d="M 1010,-1254.3 L 1010,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1010,-1305 L 1010,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58948@0"/></metadata>
    <path d="M 1010,-1305 L 1010,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1148 L 797,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61844@0" ObjectIDND1="58944@1" ObjectIDND2="58945@0"/></metadata>
    <path d="M 797,-1148 L 797,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1171 L 797,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58944@1" ObjectIDND1="61844@0" ObjectIDND2="58945@0"/></metadata>
    <path d="M 797,-1171 L 797,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1148 L 815,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58945@0" ObjectIDND1="61844@0" ObjectIDND2="58944@1"/></metadata>
    <path d="M 797,-1148 L 815,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1206.64 L 797,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58944@0" ObjectIDND1="58971@1"/></metadata>
    <path d="M 797,-1206.64 L 797,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1254.3 L 797,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58943@1" ObjectIDND1="58971@0"/></metadata>
    <path d="M 797,-1254.3 L 797,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 797,-1305 L 797,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58943@0"/></metadata>
    <path d="M 797,-1305 L 797,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1148 L 713,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61803@0" ObjectIDND1="58939@1" ObjectIDND2="58940@0"/></metadata>
    <path d="M 713,-1148 L 713,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1171 L 713,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58939@1" ObjectIDND1="61803@0" ObjectIDND2="58940@0"/></metadata>
    <path d="M 713,-1171 L 713,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1148 L 731,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58940@0" ObjectIDND1="61803@0" ObjectIDND2="58939@1"/></metadata>
    <path d="M 713,-1148 L 731,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1206.64 L 713,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58939@0" ObjectIDND1="58966@1"/></metadata>
    <path d="M 713,-1206.64 L 713,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1254.3 L 713,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58938@1" ObjectIDND1="58966@0"/></metadata>
    <path d="M 713,-1254.3 L 713,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 713,-1305 L 713,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58938@0"/></metadata>
    <path d="M 713,-1305 L 713,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1171,-1430.71 L 1141,-1430.71 L 1141,-1413" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58933@0" ObjectIDND1="59122@2"/></metadata>
    <path d="M 1171,-1430.71 L 1141,-1430.71 L 1141,-1413" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1201,-1430.71 L 1171,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58933@0" ObjectIDND1="59122@2"/></metadata>
    <path d="M 1201,-1430.71 L 1171,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2075.45,-1151 L 2075.45,-1116" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61855@0" ObjectIDND1="58999@1" ObjectIDND2="59000@0"/></metadata>
    <path d="M 2075.45,-1151 L 2075.45,-1116" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2075.45,-1174 L 2075.45,-1151" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58999@1" ObjectIDND1="61855@0" ObjectIDND2="59000@0"/></metadata>
    <path d="M 2075.45,-1174 L 2075.45,-1151" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2075.45,-1151 L 2092,-1151" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="59000@0" ObjectIDND1="61855@0" ObjectIDND2="58999@1"/></metadata>
    <path d="M 2075.45,-1151 L 2092,-1151" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2075.45,-1213.64 L 2075.45,-1192" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58999@0" ObjectIDND1="59026@1"/></metadata>
    <path d="M 2075.45,-1213.64 L 2075.45,-1192" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2074,-1257.3 L 2075.45,-1247.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58998@1" ObjectIDND1="59026@0"/></metadata>
    <path d="M 2074,-1257.3 L 2075.45,-1247.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2074,-1305 L 2074,-1273.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58998@0"/></metadata>
    <path d="M 2074,-1305 L 2074,-1273.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1146 L 1894,-1111" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61854@0" ObjectIDND1="58994@1" ObjectIDND2="58995@0"/></metadata>
    <path d="M 1894,-1146 L 1894,-1111" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1167 L 1894,-1146" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58994@1" ObjectIDND1="61854@0" ObjectIDND2="58995@0"/></metadata>
    <path d="M 1894,-1167 L 1894,-1146" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1146 L 1912,-1146" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58995@0" ObjectIDND1="61854@0" ObjectIDND2="58994@1"/></metadata>
    <path d="M 1894,-1146 L 1912,-1146" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1894,-1305 L 1894,-1268.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58993@0"/></metadata>
    <path d="M 1894,-1305 L 1894,-1268.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1145 L 1804,-1110" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61853@0" ObjectIDND1="58989@1" ObjectIDND2="58990@0"/></metadata>
    <path d="M 1804,-1145 L 1804,-1110" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1166 L 1804,-1145" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58989@1" ObjectIDND1="61853@0" ObjectIDND2="58990@0"/></metadata>
    <path d="M 1804,-1166 L 1804,-1145" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1145 L 1822,-1145" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58990@0" ObjectIDND1="61853@0" ObjectIDND2="58989@1"/></metadata>
    <path d="M 1804,-1145 L 1822,-1145" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1804,-1305 L 1804,-1267.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58988@0"/></metadata>
    <path d="M 1804,-1305 L 1804,-1267.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1990,-1168 L 1990,-1139" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="59052@1"/></metadata>
    <path d="M 1990,-1168 L 1990,-1139" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1990,-1256.3 L 1990,-1186" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59052@0" ObjectIDND1="59051@1"/></metadata>
    <path d="M 1990,-1256.3 L 1990,-1186" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1990,-1305 L 1990,-1272.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="59051@0"/></metadata>
    <path d="M 1990,-1305 L 1990,-1272.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1148 L 1720,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61852@0" ObjectIDND1="58984@1" ObjectIDND2="58985@0"/></metadata>
    <path d="M 1720,-1148 L 1720,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1171 L 1720,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58984@1" ObjectIDND1="61852@0" ObjectIDND2="58985@0"/></metadata>
    <path d="M 1720,-1171 L 1720,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1148 L 1738,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58985@0" ObjectIDND1="61852@0" ObjectIDND2="58984@1"/></metadata>
    <path d="M 1720,-1148 L 1738,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1206.64 L 1720,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58984@0" ObjectIDND1="59011@1"/></metadata>
    <path d="M 1720,-1206.64 L 1720,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1254.3 L 1720,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58983@1" ObjectIDND1="59011@0"/></metadata>
    <path d="M 1720,-1254.3 L 1720,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1720,-1305 L 1720,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58983@0"/></metadata>
    <path d="M 1720,-1305 L 1720,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1148 L 1634,-1113" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61851@0" ObjectIDND1="58979@1" ObjectIDND2="58980@0"/></metadata>
    <path d="M 1634,-1148 L 1634,-1113" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1171 L 1634,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58979@1" ObjectIDND1="61851@0" ObjectIDND2="58980@0"/></metadata>
    <path d="M 1634,-1171 L 1634,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1148 L 1652,-1148" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58980@0" ObjectIDND1="61851@0" ObjectIDND2="58979@1"/></metadata>
    <path d="M 1634,-1148 L 1652,-1148" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1206.64 L 1634,-1189" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58979@0" ObjectIDND1="59006@1"/></metadata>
    <path d="M 1634,-1206.64 L 1634,-1189" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1254.3 L 1634,-1240.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58978@1" ObjectIDND1="59006@0"/></metadata>
    <path d="M 1634,-1254.3 L 1634,-1240.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1634,-1305 L 1634,-1270.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58978@0"/></metadata>
    <path d="M 1634,-1305 L 1634,-1270.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1147 L 1544,-1112" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="load" DevType1="xcswitch" DevType2="switch" ObjectIDND0="61850@0" ObjectIDND1="58974@1" ObjectIDND2="58975@0"/></metadata>
    <path d="M 1544,-1147 L 1544,-1112" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1170 L 1544,-1147" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="switch" ObjectIDND0="58974@1" ObjectIDND1="61850@0" ObjectIDND2="58975@0"/></metadata>
    <path d="M 1544,-1170 L 1544,-1147" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1147 L 1562,-1147" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="load" DevType2="xcswitch" ObjectIDND0="58975@0" ObjectIDND1="61850@0" ObjectIDND2="58974@1"/></metadata>
    <path d="M 1544,-1147 L 1562,-1147" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1205.64 L 1544,-1188" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58974@0" ObjectIDND1="59001@1"/></metadata>
    <path d="M 1544,-1205.64 L 1544,-1188" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1253.3 L 1544,-1239.48" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="58973@1" ObjectIDND1="59001@0"/></metadata>
    <path d="M 1544,-1253.3 L 1544,-1239.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1544,-1305 L 1544,-1269.77" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="58903@-1" ObjectIDND1="58973@0"/></metadata>
    <path d="M 1544,-1305 L 1544,-1269.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1012,-1550 L 1022.76,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="switch" ObjectIDND0="58934@0" ObjectIDND1="58935@1" ObjectIDND2="58937@0"/></metadata>
    <path d="M 1012,-1550 L 1022.76,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 996.861,-1550 L 1012,-1550" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58935@1" ObjectIDND1="58934@0" ObjectIDND2="58937@0"/></metadata>
    <path d="M 996.861,-1550 L 1012,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1012,-1550 L 1012,-1515" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="switch" ObjectIDND0="58937@0" ObjectIDND1="58934@0" ObjectIDND2="58935@1"/></metadata>
    <path d="M 1012,-1550 L 1012,-1515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1271.01,-1430.71 L 1201,-1430.71" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="transformer2" DevType1="switch" ObjectIDND0="59122@2" ObjectIDND1="58933@0"/></metadata>
    <path d="M 1271.01,-1430.71 L 1201,-1430.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 1201,-1430.71 L 1201,-1411" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58933@0" ObjectIDND1="59122@2"/></metadata>
    <path d="M 1201,-1430.71 L 1201,-1411" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 822,-1430.83 L 751,-1430.83" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 822,-1430.83 L 751,-1430.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV110" d="M 751,-1430.83 L 751,-1410" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="transformer2" ObjectIDND0="58928@0" ObjectIDND1="59121@2"/></metadata>
    <path d="M 751,-1430.83 L 751,-1410" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-437 L 1225,-425" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="capacitor" DevType1="switch" DevType2="switch" ObjectIDND0="59130@0" ObjectIDND1="59086@1" ObjectIDND2="59087@0"/></metadata>
    <path d="M 1225,-437 L 1225,-425" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 731.861,-483 L 750.861,-483 L 750.861,-461" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 731.861,-483 L 750.861,-483 L 750.861,-461" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-457.732 L 1225,-437" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="capacitor" DevType2="switch" ObjectIDND0="59086@1" ObjectIDND1="59130@0" ObjectIDND2="59087@0"/></metadata>
    <path d="M 1225,-457.732 L 1225,-437" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1225,-437 L 1250,-437" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="capacitor" DevType2="switch" ObjectIDND0="59087@0" ObjectIDND1="59130@0" ObjectIDND2="59086@1"/></metadata>
    <path d="M 1225,-437 L 1250,-437" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 479.166,-634.107 L 479.166,-658" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="59062@0" ObjectIDND1="58904@-1"/></metadata>
    <path d="M 479.166,-634.107 L 479.166,-658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 541.033,-595.622 L 541.033,-503.42 L 479.166,-503.42 L 479.166,-527.342" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59061@1" ObjectIDND1="59063@1"/></metadata>
    <path d="M 541.033,-595.622 L 541.033,-503.42 L 479.166,-503.42 L 479.166,-527.342" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1465.4,-1270.09 L 1465.4,-1302.87" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="59060@0" ObjectIDND1="58903@-1"/></metadata>
    <path d="M 1465.4,-1270.09 L 1465.4,-1302.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1464.32,-1025.43 L 1464.81,-1160.32" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="59063@1" ObjectIDND1="59061@1"/></metadata>
    <path d="M 1464.32,-1025.43 L 1464.81,-1160.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M486.095,-1115.25 C486.195,-1115.25 486.468,-1126.03 486.471,-1126.13" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M486.095,-1115.25 C486.195,-1115.25 486.468,-1126.03 486.471,-1126.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M599,-1030 C598.96,-1030.4 599.293,-1005.73 599.294,-1005.63" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59068@1" ObjectIDND1="59069@0" ObjectIDND2="59127@0"/></metadata>
    <path d="M599,-1030 C598.96,-1030.4 599.293,-1005.73 599.294,-1005.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M569.242,-1013.9 C569.342,-1013.9 598.26,-1014.33 598.36,-1014.33" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M569.242,-1013.9 C569.342,-1013.9 598.26,-1014.33 598.36,-1014.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M598.86,-974.426 C598.96,-974.426 598.429,-989.537 598.425,-989.637" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M598.86,-974.426 C598.96,-974.426 598.429,-989.537 598.425,-989.637" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M2299.85,-1026.56 C2299.81,-1026.96 2300.14,-1002.29 2300.14,-1002.19" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59080@1" ObjectIDND1="59081@0" ObjectIDND2="59129@0"/></metadata>
    <path d="M2299.85,-1026.56 C2299.81,-1026.96 2300.14,-1002.29 2300.14,-1002.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M2270.09,-1010.46 C2270.19,-1010.46 2299.11,-1010.89 2299.21,-1010.89" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M2270.09,-1010.46 C2270.19,-1010.46 2299.11,-1010.89 2299.21,-1010.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M2299.71,-970.986 C2299.81,-970.986 2299.28,-986.097 2299.27,-986.197" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M2299.71,-970.986 C2299.81,-970.986 2299.28,-986.097 2299.27,-986.197" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M2176.54,-1028.9 C2176.5,-1029.31 2176.83,-1004.63 2176.83,-1004.53" stroke-width="4"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="capacitor" ObjectIDND0="59074@1" ObjectIDND1="59075@0" ObjectIDND2="59128@0"/></metadata>
    <path d="M2176.54,-1028.9 C2176.5,-1029.31 2176.83,-1004.63 2176.83,-1004.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M2146.78,-1012.8 C2146.88,-1012.8 2175.8,-1013.23 2175.9,-1013.24" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M2146.78,-1012.8 C2146.88,-1012.8 2175.8,-1013.23 2175.9,-1013.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M2176.4,-973.328 C2176.5,-973.328 2175.97,-988.438 2175.96,-988.538" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M2176.4,-973.328 C2176.5,-973.328 2175.97,-988.438 2175.96,-988.538" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFBuDaiDian" d="M 677.373,-438.598 L 677.373,-453.808" stroke-width="4"/>
      <metadata><cge:CN_Ref/></metadata>
    <path d="M 677.373,-438.598 L 677.373,-453.808" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="BusbarSectionClass">
    <g id="WenHuaZ.WenHuaZ_110#2">
      <path class="NFkV110" d="M 1139,-1550 L 1568,-1550" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="58902" ObjectName="WenHuaZ.WenHuaZ_110#2"/><cge:TPSR_Ref TObjectID="58902"/></metadata>
    <path d="M 1139,-1550 L 1568,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="WenHuaZ.WenHuaZ_10#1">
      <path class="NFkV10" d="M 466,-1305 L 2710,-1305" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="58903" ObjectName="WenHuaZ.WenHuaZ_10#1"/><cge:TPSR_Ref TObjectID="58903"/></metadata>
    <path d="M 466,-1305 L 2710,-1305" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="WenHuaZ.WenHuaZ_110#1">
      <path class="NFkV110" d="M 524,-1550 L 931,-1550" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="58901" ObjectName="WenHuaZ.WenHuaZ_110#1"/><cge:TPSR_Ref TObjectID="58901"/></metadata>
    <path d="M 524,-1550 L 931,-1550" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="WenHuaZ.WenHuaZ_10#2">
      <path class="NFkV10" d="M 458,-658 L 2058,-658" stroke-width="15"/>
      <metadata><cge:PSR_Ref ObjectID="58904" ObjectName="WenHuaZ.WenHuaZ_10#2"/><cge:TPSR_Ref TObjectID="58904"/></metadata>
    <path d="M 458,-658 L 2058,-658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="ACLineSegmentClass">
    <g beginPointId="257808" endPointId="0" id="110kV.Lqqwl_wen" runFlow="0">
      <path class="NFkV110" d="M 1298,-1786 L 1298,-1815" stroke-width="4"/>
      <metadata><cge:PSR_Ref ObjectID="62249" ObjectName="62249"/><cge:TPSR_Ref TObjectID="62249_SS-293"/></metadata>
    <path d="M 1298,-1786 L 1298,-1815" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g beginPointId="257796" endPointId="0" id="110kV.Lqglw_w" runFlow="0">
      <path class="NFkV110" d="M 804,-1781 L 804,-1814.11" stroke-width="4"/>
      <metadata><cge:PSR_Ref ObjectID="62250" ObjectName="62250"/><cge:TPSR_Ref TObjectID="62250_SS-293"/></metadata>
    <path d="M 804,-1781 L 804,-1814.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="PowerTransformer2Class">
    <g id="WenHuaZ.WenHuaZ_1T" primType="transformer2">
      <g id="WD-0">
        <use class="kV10" height="91.0628" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" xlink:href="#Transformer2:shape12_0" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="59121-L"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV110" height="91.0628" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" xlink:href="#Transformer2:shape12_1" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="59121-H"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="59121" ObjectName="WenHuaZ.WenHuaZ_1T"/><cge:TPSR_Ref TObjectID="59121"/></metadata>
    <rect fill="white" height="91.0628" opacity="0" stroke="white" transform="matrix(1.0339,0,0,1,792.529,-1364.33)" width="60.3786" x="-1.18931" y="-91"/></g>
    <g id="WenHuaZ.WenHuaZ_2T" primType="transformer2">
      <g id="WD-0">
        <use class="kV10" height="91" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" xlink:href="#Transformer2:shape12_0" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="59122-L"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV110" height="91" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" xlink:href="#Transformer2:shape12_1" y="-91"/>
        <metadata><cge:PSR_Ref ObjectID="59122-H"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="59122" ObjectName="WenHuaZ.WenHuaZ_2T"/><cge:TPSR_Ref TObjectID="59122"/></metadata>
    <rect fill="white" height="91" opacity="0" stroke="white" transform="matrix(1.0339,0,0,1,1241.52,-1365)" width="58" x="0" y="-91"/></g>
  </g>
  <g id="BreakerClass">
    <g id="258305" primType="breaker">
      <use class="kV110" height="27" transform="matrix(0.96,0,0,0.62069,1017,-1540.07)" width="48" x="0" xlink:href="#Breaker:shape3_0" y="-29"/>
      <metadata><cge:PSR_Ref ObjectID="58934" ObjectName="WenHuaZ.WenHuaZ_100BK"/>
        <cge:Meas_Ref ObjectID="ME-258305"/><cge:TPSR_Ref TObjectID="58934"/></metadata>
    <rect fill="white" height="27" opacity="0" stroke="white" transform="matrix(0.96,0,0,0.62069,1017,-1540.07)" width="48" x="0" y="-29"/></g>
    <g id="258653" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1537.55,-1200)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59001" ObjectName="WenHuaZ.WenHuaZ_919BK"/>
        <cge:Meas_Ref ObjectID="ME-258653"/><cge:TPSR_Ref TObjectID="59001"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1537.55,-1200)" width="27" x="-4" y="-48"/></g>
    <g id="258678" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1627.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59006" ObjectName="WenHuaZ.WenHuaZ_920BK"/>
        <cge:Meas_Ref ObjectID="ME-258678"/><cge:TPSR_Ref TObjectID="59006"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1627.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258703" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1713.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59011" ObjectName="WenHuaZ.WenHuaZ_921BK"/>
        <cge:Meas_Ref ObjectID="ME-258703"/><cge:TPSR_Ref TObjectID="59011"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1713.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258728" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1797.55,-1198)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59016" ObjectName="WenHuaZ.WenHuaZ_922BK"/>
        <cge:Meas_Ref ObjectID="ME-258728"/><cge:TPSR_Ref TObjectID="59016"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1797.55,-1198)" width="27" x="-4" y="-48"/></g>
    <g id="258778" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2069,-1208)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59026" ObjectName="WenHuaZ.WenHuaZ_924BK"/>
        <cge:Meas_Ref ObjectID="ME-258778"/><cge:TPSR_Ref TObjectID="59026"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2069,-1208)" width="27" x="-4" y="-48"/></g>
    <g id="258478" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,706.552,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58966" ObjectName="WenHuaZ.WenHuaZ_912BK"/>
        <cge:Meas_Ref ObjectID="ME-258478"/><cge:TPSR_Ref TObjectID="58966"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,706.552,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258503" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,790.552,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58971" ObjectName="WenHuaZ.WenHuaZ_913BK"/>
        <cge:Meas_Ref ObjectID="ME-258503"/><cge:TPSR_Ref TObjectID="58971"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,790.552,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258528" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1003.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58976" ObjectName="WenHuaZ.WenHuaZ_914BK"/>
        <cge:Meas_Ref ObjectID="ME-258528"/><cge:TPSR_Ref TObjectID="58976"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1003.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258553" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1089.55,-1202)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58981" ObjectName="WenHuaZ.WenHuaZ_915BK"/>
        <cge:Meas_Ref ObjectID="ME-258553"/><cge:TPSR_Ref TObjectID="58981"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1089.55,-1202)" width="27" x="-4" y="-48"/></g>
    <g id="258578" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1178.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58986" ObjectName="WenHuaZ.WenHuaZ_916BK"/>
        <cge:Meas_Ref ObjectID="ME-258578"/><cge:TPSR_Ref TObjectID="58986"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1178.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258603" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1268.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58991" ObjectName="WenHuaZ.WenHuaZ_917BK"/>
        <cge:Meas_Ref ObjectID="ME-258603"/><cge:TPSR_Ref TObjectID="58991"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1268.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258628" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1354.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58996" ObjectName="WenHuaZ.WenHuaZ_918BK"/>
        <cge:Meas_Ref ObjectID="ME-258628"/><cge:TPSR_Ref TObjectID="58996"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1354.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258405" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,592.552,-1206)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58952" ObjectName="WenHuaZ.WenHuaZ_C01BK"/>
        <cge:Meas_Ref ObjectID="ME-258405"/><cge:TPSR_Ref TObjectID="58952"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,592.552,-1206)" width="27" x="-4" y="-48"/></g>
    <g id="258380" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,478.552,-1205)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58947" ObjectName="WenHuaZ.WenHuaZ_911BK"/>
        <cge:Meas_Ref ObjectID="ME-258380"/><cge:TPSR_Ref TObjectID="58947"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,478.552,-1205)" width="27" x="-4" y="-48"/></g>
    <g id="258428" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,2169.55,-1206)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58956" ObjectName="WenHuaZ.WenHuaZ_C02BK"/>
        <cge:Meas_Ref ObjectID="ME-258428"/><cge:TPSR_Ref TObjectID="58956"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,2169.55,-1206)" width="27" x="-4" y="-48"/></g>
    <g id="258171" primType="breaker">
      <use class="kV110" height="48" transform="matrix(0.586207,0,0,0.94,797.552,-1634)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58906" ObjectName="WenHuaZ.WenHuaZ_103BK"/>
        <cge:Meas_Ref ObjectID="ME-258171"/><cge:TPSR_Ref TObjectID="58906"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,797.552,-1634)" width="27" x="-4" y="-48"/></g>
    <g id="258199" primType="breaker">
      <use class="kV110" height="48" transform="matrix(0.62069,0,0,0.94,1291.17,-1631)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58913" ObjectName="WenHuaZ.WenHuaZ_104BK"/>
        <cge:Meas_Ref ObjectID="ME-258199"/><cge:TPSR_Ref TObjectID="58913"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.62069,0,0,0.94,1291.17,-1631)" width="27" x="-4" y="-48"/></g>
    <g id="258899" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1104.55,-562)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59050" ObjectName="WenHuaZ.WenHuaZ_931BK"/>
        <cge:Meas_Ref ObjectID="ME-258899"/><cge:TPSR_Ref TObjectID="59050"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1104.55,-562)" width="27" x="-4" y="-48"/></g>
    <g id="258961" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1510.55,-562)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59070" ObjectName="WenHuaZ.WenHuaZ_932BK"/>
        <cge:Meas_Ref ObjectID="ME-258961"/><cge:TPSR_Ref TObjectID="59070"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1510.55,-562)" width="27" x="-4" y="-48"/></g>
    <g id="258403" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,671.552,-557)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58951" ObjectName="WenHuaZ.WenHuaZ_928BK"/>
        <cge:Meas_Ref ObjectID="ME-258403"/><cge:TPSR_Ref TObjectID="58951"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,671.552,-557)" width="27" x="-4" y="-48"/></g>
    <g id="258453" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1218.55,-558)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58961" ObjectName="WenHuaZ.WenHuaZ_C04BK"/>
        <cge:Meas_Ref ObjectID="ME-258453"/><cge:TPSR_Ref TObjectID="58961"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1218.55,-558)" width="27" x="-4" y="-48"/></g>
    <g id="258853" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,575.552,-561)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59041" ObjectName="WenHuaZ.WenHuaZ_927BK"/>
        <cge:Meas_Ref ObjectID="ME-258853"/><cge:TPSR_Ref TObjectID="59041"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,575.552,-561)" width="27" x="-4" y="-48"/></g>
    <g id="258891" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,807.552,-561)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59046" ObjectName="WenHuaZ.WenHuaZ_929BK"/>
        <cge:Meas_Ref ObjectID="ME-258891"/><cge:TPSR_Ref TObjectID="59046"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,807.552,-561)" width="27" x="-4" y="-48"/></g>
    <g id="258895" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,899.552,-560)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59048" ObjectName="WenHuaZ.WenHuaZ_930BK"/>
        <cge:Meas_Ref ObjectID="ME-258895"/><cge:TPSR_Ref TObjectID="59048"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,899.552,-560)" width="27" x="-4" y="-48"/></g>
    <g id="258301" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1014.55,-558)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58932" ObjectName="WenHuaZ.WenHuaZ_902BK"/>
        <cge:Meas_Ref ObjectID="ME-258301"/><cge:TPSR_Ref TObjectID="58932"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1014.55,-558)" width="27" x="-4" y="-48"/></g>
    <g id="258753" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1887.55,-1198)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59021" ObjectName="WenHuaZ.WenHuaZ_923BK"/>
        <cge:Meas_Ref ObjectID="ME-258753"/><cge:TPSR_Ref TObjectID="59021"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1887.55,-1198)" width="27" x="-4" y="-48"/></g>
    <g id="258999" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1616.55,-563)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59082" ObjectName="WenHuaZ.WenHuaZ_933BK"/>
        <cge:Meas_Ref ObjectID="ME-258999"/><cge:TPSR_Ref TObjectID="59082"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1616.55,-563)" width="27" x="-4" y="-48"/></g>
    <g id="259034" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1705.55,-563)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59094" ObjectName="WenHuaZ.WenHuaZ_934BK"/>
        <cge:Meas_Ref ObjectID="ME-259034"/><cge:TPSR_Ref TObjectID="59094"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1705.55,-563)" width="27" x="-4" y="-48"/></g>
    <g id="258455" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1363.55,-559)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58962" ObjectName="WenHuaZ.WenHuaZ_C05BK"/>
        <cge:Meas_Ref ObjectID="ME-258455"/><cge:TPSR_Ref TObjectID="58962"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1363.55,-559)" width="27" x="-4" y="-48"/></g>
    <g id="258430" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.96,2293.55,-1205)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58957" ObjectName="WenHuaZ.WenHuaZ_C03BK"/>
        <cge:Meas_Ref ObjectID="ME-258430"/><cge:TPSR_Ref TObjectID="58957"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.96,2293.55,-1205)" width="27" x="-4" y="-48"/></g>
    <g id="258803" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2398.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59031" ObjectName="WenHuaZ.WenHuaZ_925BK"/>
        <cge:Meas_Ref ObjectID="ME-258803"/><cge:TPSR_Ref TObjectID="59031"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2398.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258828" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2488.55,-1201)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59036" ObjectName="WenHuaZ.WenHuaZ_926BK"/>
        <cge:Meas_Ref ObjectID="ME-258828"/><cge:TPSR_Ref TObjectID="59036"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2488.55,-1201)" width="27" x="-4" y="-48"/></g>
    <g id="258355" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,2594.55,-1205)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58942" ObjectName="WenHuaZ.WenHuaZ_994BK"/>
        <cge:Meas_Ref ObjectID="ME-258355"/><cge:TPSR_Ref TObjectID="58942"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,2594.55,-1205)" width="27" x="-4" y="-48"/></g>
    <g id="258378" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1816.55,-558)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58946" ObjectName="WenHuaZ.WenHuaZ_995BK"/>
        <cge:Meas_Ref ObjectID="ME-258378"/><cge:TPSR_Ref TObjectID="58946"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1816.55,-558)" width="27" x="-4" y="-48"/></g>
    <g id="258927" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,1458.36,-1202.15)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="59059" ObjectName="WenHuaZ.WenHuaZ_9001BK"/>
        <cge:Meas_Ref ObjectID="ME-258927"/><cge:TPSR_Ref TObjectID="59059"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,1458.36,-1202.15)" width="27" x="-4" y="-48"/></g>
    <g id="258269" primType="breaker">
      <use class="kV10" height="48" transform="matrix(0.586207,0,0,0.94,913.552,-1206)" width="27" x="-4" xlink:href="#Breaker:shape2_0" y="-48"/>
      <metadata><cge:PSR_Ref ObjectID="58927" ObjectName="WenHuaZ.WenHuaZ_901BK"/>
        <cge:Meas_Ref ObjectID="ME-258269"/><cge:TPSR_Ref TObjectID="58927"/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(0.586207,0,0,0.94,913.552,-1206)" width="27" x="-4" y="-48"/></g>
  </g>
  <g id="DisconnectorClass">
    <g id="259003" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,1210.19,-430)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="59086" ObjectName="WenHuaZ.WenHuaZ_C046SW"/>
        <cge:Meas_Ref ObjectID="ME-259003"/><cge:TPSR_Ref TObjectID="59086"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1210.19,-430)" width="16" x="5" y="-63"/></g>
    <g id="259004" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1244.95,-431.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59087" ObjectName="WenHuaZ.WenHuaZ_C0467SW"/>
        <cge:Meas_Ref ObjectID="ME-259004"/><cge:TPSR_Ref TObjectID="59087"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1244.95,-431.095)" width="60" x="-1" y="-23"/></g>
    <g id="258263" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,807.187,-1461)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58924" ObjectName="WenHuaZ.WenHuaZ_1034SW"/>
        <cge:Meas_Ref ObjectID="ME-258263"/><cge:TPSR_Ref TObjectID="58924"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,807.187,-1461)" width="16" x="5" y="-63"/></g>
    <g id="258265" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,850.946,-1465.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58925" ObjectName="WenHuaZ.WenHuaZ_10348SW"/>
        <cge:Meas_Ref ObjectID="ME-258265"/><cge:TPSR_Ref TObjectID="58925"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,850.946,-1465.7)" width="60" x="-1" y="-23"/></g>
    <g id="258293" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,679.965,-1324.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="58928" ObjectName="WenHuaZ.WenHuaZ_1010SW"/>
        <cge:Meas_Ref ObjectID="ME-258293"/><cge:TPSR_Ref TObjectID="58928"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,679.965,-1324.92)" width="25" x="3" y="-100"/></g>
    <g id="258295" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1256.2,-1456)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58929" ObjectName="WenHuaZ.WenHuaZ_1044SW"/>
        <cge:Meas_Ref ObjectID="ME-258295"/><cge:TPSR_Ref TObjectID="58929"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1256.2,-1456)" width="16" x="5" y="-63"/></g>
    <g id="258297" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1302.95,-1466.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58930" ObjectName="WenHuaZ.WenHuaZ_10448SW"/>
        <cge:Meas_Ref ObjectID="ME-258297"/><cge:TPSR_Ref TObjectID="58930"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1302.95,-1466.7)" width="60" x="-1" y="-23"/></g>
    <g id="258303" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1129.96,-1323.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="58933" ObjectName="WenHuaZ.WenHuaZ_1020SW"/>
        <cge:Meas_Ref ObjectID="ME-258303"/><cge:TPSR_Ref TObjectID="58933"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1129.96,-1323.92)" width="25" x="3" y="-100"/></g>
    <g id="258307" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,952,-1545.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="58935" ObjectName="WenHuaZ.WenHuaZ_1001SW"/>
        <cge:Meas_Ref ObjectID="ME-258307"/><cge:TPSR_Ref TObjectID="58935"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,952,-1545.34)" width="45" x="1" y="-15"/></g>
    <g id="258328" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,1075,-1545.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="58936" ObjectName="WenHuaZ.WenHuaZ_1002SW"/>
        <cge:Meas_Ref ObjectID="ME-258328"/><cge:TPSR_Ref TObjectID="58936"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1075,-1545.34)" width="45" x="1" y="-15"/></g>
    <g id="258330" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1000.96,-1425.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="58937" ObjectName="WenHuaZ.WenHuaZ_10017SW"/>
        <cge:Meas_Ref ObjectID="ME-258330"/><cge:TPSR_Ref TObjectID="58937"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1000.96,-1425.92)" width="25" x="3" y="-100"/></g>
    <g id="258353" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1059.96,-1424.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="58941" ObjectName="WenHuaZ.WenHuaZ_10027SW"/>
        <cge:Meas_Ref ObjectID="ME-258353"/><cge:TPSR_Ref TObjectID="58941"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1059.96,-1424.92)" width="25" x="3" y="-100"/></g>
    <g id="258507" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1534,-1247)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58973" ObjectName="WenHuaZ.WenHuaZ_919XC"/>
        <cge:Meas_Ref ObjectID="ME-258507"/><cge:TPSR_Ref TObjectID="58973"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1534,-1247)" width="22" x="-1" y="-30"/></g>
    <g id="258507" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1533" xlink:href="#Disconnector:shape3_0" y="-1194"/>
      <metadata><cge:PSR_Ref ObjectID="58974" ObjectName="WenHuaZ.WenHuaZ_919XC1"/>
        <cge:Meas_Ref ObjectID="ME-258507"/><cge:TPSR_Ref TObjectID="58974"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1533" y="-1194"/></g>
    <g id="258508" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1556.95,-1140.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58975" ObjectName="WenHuaZ.WenHuaZ_91938SW"/>
        <cge:Meas_Ref ObjectID="ME-258508"/><cge:TPSR_Ref TObjectID="58975"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1556.95,-1140.7)" width="60" x="-1" y="-23"/></g>
    <g id="258532" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1624,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58978" ObjectName="WenHuaZ.WenHuaZ_920XC"/>
        <cge:Meas_Ref ObjectID="ME-258532"/><cge:TPSR_Ref TObjectID="58978"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1624,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258532" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1623" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58979" ObjectName="WenHuaZ.WenHuaZ_920XC1"/>
        <cge:Meas_Ref ObjectID="ME-258532"/><cge:TPSR_Ref TObjectID="58979"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1623" y="-1195"/></g>
    <g id="258533" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1646.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58980" ObjectName="WenHuaZ.WenHuaZ_92038SW"/>
        <cge:Meas_Ref ObjectID="ME-258533"/><cge:TPSR_Ref TObjectID="58980"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1646.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258557" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1710,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58983" ObjectName="WenHuaZ.WenHuaZ_921XC"/>
        <cge:Meas_Ref ObjectID="ME-258557"/><cge:TPSR_Ref TObjectID="58983"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1710,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258557" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1709" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58984" ObjectName="WenHuaZ.WenHuaZ_921XC1"/>
        <cge:Meas_Ref ObjectID="ME-258557"/><cge:TPSR_Ref TObjectID="58984"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1709" y="-1195"/></g>
    <g id="258558" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1732.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58985" ObjectName="WenHuaZ.WenHuaZ_92138SW"/>
        <cge:Meas_Ref ObjectID="ME-258558"/><cge:TPSR_Ref TObjectID="58985"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1732.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258915" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1980,-1250)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59051" ObjectName="WenHuaZ.WenHuaZ_0951XC"/>
        <cge:Meas_Ref ObjectID="ME-258915"/><cge:TPSR_Ref TObjectID="59051"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1980,-1250)" width="22" x="-1" y="-30"/></g>
    <g id="258915" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1979" xlink:href="#Disconnector:shape3_0" y="-1192"/>
      <metadata><cge:PSR_Ref ObjectID="59052" ObjectName="WenHuaZ.WenHuaZ_0951XC1"/>
        <cge:Meas_Ref ObjectID="ME-258915"/><cge:TPSR_Ref TObjectID="59052"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1979" y="-1192"/></g>
    <g id="258582" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1794,-1245)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58988" ObjectName="WenHuaZ.WenHuaZ_922XC"/>
        <cge:Meas_Ref ObjectID="ME-258582"/><cge:TPSR_Ref TObjectID="58988"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1794,-1245)" width="22" x="-1" y="-30"/></g>
    <g id="258582" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1793" xlink:href="#Disconnector:shape3_0" y="-1190"/>
      <metadata><cge:PSR_Ref ObjectID="58989" ObjectName="WenHuaZ.WenHuaZ_922XC1"/>
        <cge:Meas_Ref ObjectID="ME-258582"/><cge:TPSR_Ref TObjectID="58989"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1793" y="-1190"/></g>
    <g id="258583" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1816.95,-1138.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58990" ObjectName="WenHuaZ.WenHuaZ_92238SW"/>
        <cge:Meas_Ref ObjectID="ME-258583"/><cge:TPSR_Ref TObjectID="58990"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1816.95,-1138.7)" width="60" x="-1" y="-23"/></g>
    <g id="258607" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1884,-1246)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58993" ObjectName="WenHuaZ.WenHuaZ_923XC"/>
        <cge:Meas_Ref ObjectID="ME-258607"/><cge:TPSR_Ref TObjectID="58993"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1884,-1246)" width="22" x="-1" y="-30"/></g>
    <g id="258607" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1883" xlink:href="#Disconnector:shape3_0" y="-1191"/>
      <metadata><cge:PSR_Ref ObjectID="58994" ObjectName="WenHuaZ.WenHuaZ_923XC1"/>
        <cge:Meas_Ref ObjectID="ME-258607"/><cge:TPSR_Ref TObjectID="58994"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1883" y="-1191"/></g>
    <g id="258608" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1906.95,-1139.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58995" ObjectName="WenHuaZ.WenHuaZ_92338SW"/>
        <cge:Meas_Ref ObjectID="ME-258608"/><cge:TPSR_Ref TObjectID="58995"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1906.95,-1139.7)" width="60" x="-1" y="-23"/></g>
    <g id="258632" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2064,-1251)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58998" ObjectName="WenHuaZ.WenHuaZ_924XC"/>
        <cge:Meas_Ref ObjectID="ME-258632"/><cge:TPSR_Ref TObjectID="58998"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2064,-1251)" width="22" x="-1" y="-30"/></g>
    <g id="258632" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2064.45" xlink:href="#Disconnector:shape3_0" y="-1198"/>
      <metadata><cge:PSR_Ref ObjectID="58999" ObjectName="WenHuaZ.WenHuaZ_924XC1"/>
        <cge:Meas_Ref ObjectID="ME-258632"/><cge:TPSR_Ref TObjectID="58999"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2064.45" y="-1198"/></g>
    <g id="258633" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2086.95,-1144.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59000" ObjectName="WenHuaZ.WenHuaZ_92438SW"/>
        <cge:Meas_Ref ObjectID="ME-258633"/><cge:TPSR_Ref TObjectID="59000"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2086.95,-1144.7)" width="60" x="-1" y="-23"/></g>
    <g id="258267" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,851.946,-1529.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58926" ObjectName="WenHuaZ.WenHuaZ_10347SW"/>
        <cge:Meas_Ref ObjectID="ME-258267"/><cge:TPSR_Ref TObjectID="58926"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,851.946,-1529.7)" width="60" x="-1" y="-23"/></g>
    <g id="258332" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,703,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58938" ObjectName="WenHuaZ.WenHuaZ_912XC"/>
        <cge:Meas_Ref ObjectID="ME-258332"/><cge:TPSR_Ref TObjectID="58938"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,703,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258332" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="702" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58939" ObjectName="WenHuaZ.WenHuaZ_912XC1"/>
        <cge:Meas_Ref ObjectID="ME-258332"/><cge:TPSR_Ref TObjectID="58939"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="702" y="-1195"/></g>
    <g id="258333" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,725.946,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58940" ObjectName="WenHuaZ.WenHuaZ_91238SW"/>
        <cge:Meas_Ref ObjectID="ME-258333"/><cge:TPSR_Ref TObjectID="58940"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,725.946,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258357" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,787,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58943" ObjectName="WenHuaZ.WenHuaZ_913XC"/>
        <cge:Meas_Ref ObjectID="ME-258357"/><cge:TPSR_Ref TObjectID="58943"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,787,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258357" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="786" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58944" ObjectName="WenHuaZ.WenHuaZ_913XC1"/>
        <cge:Meas_Ref ObjectID="ME-258357"/><cge:TPSR_Ref TObjectID="58944"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="786" y="-1195"/></g>
    <g id="258358" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,809.946,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58945" ObjectName="WenHuaZ.WenHuaZ_91338SW"/>
        <cge:Meas_Ref ObjectID="ME-258358"/><cge:TPSR_Ref TObjectID="58945"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,809.946,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258382" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1000,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58948" ObjectName="WenHuaZ.WenHuaZ_914XC"/>
        <cge:Meas_Ref ObjectID="ME-258382"/><cge:TPSR_Ref TObjectID="58948"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1000,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258382" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="999" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58949" ObjectName="WenHuaZ.WenHuaZ_914XC1"/>
        <cge:Meas_Ref ObjectID="ME-258382"/><cge:TPSR_Ref TObjectID="58949"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="999" y="-1195"/></g>
    <g id="258383" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1022.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58950" ObjectName="WenHuaZ.WenHuaZ_91438SW"/>
        <cge:Meas_Ref ObjectID="ME-258383"/><cge:TPSR_Ref TObjectID="58950"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1022.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258407" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1086,-1249)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58953" ObjectName="WenHuaZ.WenHuaZ_915XC"/>
        <cge:Meas_Ref ObjectID="ME-258407"/><cge:TPSR_Ref TObjectID="58953"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1086,-1249)" width="22" x="-1" y="-30"/></g>
    <g id="258407" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1085" xlink:href="#Disconnector:shape3_0" y="-1196"/>
      <metadata><cge:PSR_Ref ObjectID="58954" ObjectName="WenHuaZ.WenHuaZ_915XC1"/>
        <cge:Meas_Ref ObjectID="ME-258407"/><cge:TPSR_Ref TObjectID="58954"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1085" y="-1196"/></g>
    <g id="258408" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1108.95,-1142.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58955" ObjectName="WenHuaZ.WenHuaZ_91538SW"/>
        <cge:Meas_Ref ObjectID="ME-258408"/><cge:TPSR_Ref TObjectID="58955"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1108.95,-1142.7)" width="60" x="-1" y="-23"/></g>
    <g id="258432" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1175,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58958" ObjectName="WenHuaZ.WenHuaZ_916XC"/>
        <cge:Meas_Ref ObjectID="ME-258432"/><cge:TPSR_Ref TObjectID="58958"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1175,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258432" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1174" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58959" ObjectName="WenHuaZ.WenHuaZ_916XC1"/>
        <cge:Meas_Ref ObjectID="ME-258432"/><cge:TPSR_Ref TObjectID="58959"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1174" y="-1195"/></g>
    <g id="258433" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1197.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58960" ObjectName="WenHuaZ.WenHuaZ_91638SW"/>
        <cge:Meas_Ref ObjectID="ME-258433"/><cge:TPSR_Ref TObjectID="58960"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1197.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258457" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1265,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58963" ObjectName="WenHuaZ.WenHuaZ_917XC"/>
        <cge:Meas_Ref ObjectID="ME-258457"/><cge:TPSR_Ref TObjectID="58963"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1265,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258457" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1264" xlink:href="#Disconnector:shape3_0" y="-1195"/>
      <metadata><cge:PSR_Ref ObjectID="58964" ObjectName="WenHuaZ.WenHuaZ_917XC1"/>
        <cge:Meas_Ref ObjectID="ME-258457"/><cge:TPSR_Ref TObjectID="58964"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1264" y="-1195"/></g>
    <g id="258458" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1287.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58965" ObjectName="WenHuaZ.WenHuaZ_91738SW"/>
        <cge:Meas_Ref ObjectID="ME-258458"/><cge:TPSR_Ref TObjectID="58965"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1287.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258482" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1351,-1246)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58968" ObjectName="WenHuaZ.WenHuaZ_918XC"/>
        <cge:Meas_Ref ObjectID="ME-258482"/><cge:TPSR_Ref TObjectID="58968"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1351,-1246)" width="22" x="-1" y="-30"/></g>
    <g id="258482" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1350" xlink:href="#Disconnector:shape3_0" y="-1191"/>
      <metadata><cge:PSR_Ref ObjectID="58969" ObjectName="WenHuaZ.WenHuaZ_918XC1"/>
        <cge:Meas_Ref ObjectID="ME-258482"/><cge:TPSR_Ref TObjectID="58969"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1350" y="-1191"/></g>
    <g id="258483" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1373.95,-1139.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58970" ObjectName="WenHuaZ.WenHuaZ_91838SW"/>
        <cge:Meas_Ref ObjectID="ME-258483"/><cge:TPSR_Ref TObjectID="58970"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1373.95,-1139.7)" width="60" x="-1" y="-23"/></g>
    <g id="258944" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,589,-1253)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59065" ObjectName="WenHuaZ.WenHuaZ_C01XC"/>
        <cge:Meas_Ref ObjectID="ME-258944"/><cge:TPSR_Ref TObjectID="59065"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,589,-1253)" width="22" x="-1" y="-30"/></g>
    <g id="258944" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="588" xlink:href="#Disconnector:shape3_0" y="-1200"/>
      <metadata><cge:PSR_Ref ObjectID="59066" ObjectName="WenHuaZ.WenHuaZ_C01XC1"/>
        <cge:Meas_Ref ObjectID="ME-258944"/><cge:TPSR_Ref TObjectID="59066"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="588" y="-1200"/></g>
    <g id="258945" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,611.946,-1147.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59067" ObjectName="WenHuaZ.WenHuaZ_C0168SW"/>
        <cge:Meas_Ref ObjectID="ME-258945"/><cge:TPSR_Ref TObjectID="59067"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,611.946,-1147.7)" width="60" x="-1" y="-23"/></g>
    <g id="258946" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,584.187,-1076)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="59068" ObjectName="WenHuaZ.WenHuaZ_C016SW"/>
        <cge:Meas_Ref ObjectID="ME-258946"/><cge:TPSR_Ref TObjectID="59068"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,584.187,-1076)" width="16" x="5" y="-63"/></g>
    <g id="259079" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,475,-1252)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59103" ObjectName="WenHuaZ.WenHuaZ_911XC"/>
        <cge:Meas_Ref ObjectID="ME-259079"/><cge:TPSR_Ref TObjectID="59103"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,475,-1252)" width="22" x="-1" y="-30"/></g>
    <g id="259079" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="474" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="59104" ObjectName="WenHuaZ.WenHuaZ_911XC1"/>
        <cge:Meas_Ref ObjectID="ME-259079"/><cge:TPSR_Ref TObjectID="59104"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="474" y="-1199"/></g>
    <g id="259080" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,497.946,-1145.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59105" ObjectName="WenHuaZ.WenHuaZ_91138SW"/>
        <cge:Meas_Ref ObjectID="ME-259080"/><cge:TPSR_Ref TObjectID="59105"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,497.946,-1145.7)" width="60" x="-1" y="-23"/></g>
    <g id="258963" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2166,-1253)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59071" ObjectName="WenHuaZ.WenHuaZ_C02XC"/>
        <cge:Meas_Ref ObjectID="ME-258963"/><cge:TPSR_Ref TObjectID="59071"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2166,-1253)" width="22" x="-1" y="-30"/></g>
    <g id="258963" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2165" xlink:href="#Disconnector:shape3_0" y="-1200"/>
      <metadata><cge:PSR_Ref ObjectID="59072" ObjectName="WenHuaZ.WenHuaZ_C02XC1"/>
        <cge:Meas_Ref ObjectID="ME-258963"/><cge:TPSR_Ref TObjectID="59072"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2165" y="-1200"/></g>
    <g id="258964" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2188.95,-1148.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59073" ObjectName="WenHuaZ.WenHuaZ_C0268SW"/>
        <cge:Meas_Ref ObjectID="ME-258964"/><cge:TPSR_Ref TObjectID="59073"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2188.95,-1148.7)" width="60" x="-1" y="-23"/></g>
    <g id="258965" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,2161.19,-1076)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="59074" ObjectName="WenHuaZ.WenHuaZ_C026SW"/>
        <cge:Meas_Ref ObjectID="ME-258965"/><cge:TPSR_Ref TObjectID="59074"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,2161.19,-1076)" width="16" x="5" y="-63"/></g>
    <g id="258218" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,910,-1254)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58915" ObjectName="WenHuaZ.WenHuaZ_901XC"/>
        <cge:Meas_Ref ObjectID="ME-258218"/><cge:TPSR_Ref TObjectID="58915"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,910,-1254)" width="22" x="-1" y="-30"/></g>
    <g id="258218" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="909" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="58916" ObjectName="WenHuaZ.WenHuaZ_901XC1"/>
        <cge:Meas_Ref ObjectID="ME-258218"/><cge:TPSR_Ref TObjectID="58916"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="909" y="-1199"/></g>
    <g id="258175" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,789.187,-1676)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58908" ObjectName="WenHuaZ.WenHuaZ_1033SW"/>
        <cge:Meas_Ref ObjectID="ME-258175"/><cge:TPSR_Ref TObjectID="58908"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,789.187,-1676)" width="16" x="5" y="-63"/></g>
    <g id="258173" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,789.187,-1546)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58907" ObjectName="WenHuaZ.WenHuaZ_1031SW"/>
        <cge:Meas_Ref ObjectID="ME-258173"/><cge:TPSR_Ref TObjectID="58907"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,789.187,-1546)" width="16" x="5" y="-63"/></g>
    <g id="258197" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,828.946,-1756.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58912" ObjectName="WenHuaZ.WenHuaZ_10338SW"/>
        <cge:Meas_Ref ObjectID="ME-258197"/><cge:TPSR_Ref TObjectID="58912"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,828.946,-1756.7)" width="60" x="-1" y="-23"/></g>
    <g id="258195" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,829.946,-1681.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58911" ObjectName="WenHuaZ.WenHuaZ_10337SW"/>
        <cge:Meas_Ref ObjectID="ME-258195"/><cge:TPSR_Ref TObjectID="58911"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,829.946,-1681.7)" width="60" x="-1" y="-23"/></g>
    <g id="258193" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,829.946,-1615.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58910" ObjectName="WenHuaZ.WenHuaZ_10317SW"/>
        <cge:Meas_Ref ObjectID="ME-258193"/><cge:TPSR_Ref TObjectID="58910"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,829.946,-1615.7)" width="60" x="-1" y="-23"/></g>
    <g id="258230" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1283.19,-1674)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58917" ObjectName="WenHuaZ.WenHuaZ_1043SW"/>
        <cge:Meas_Ref ObjectID="ME-258230"/><cge:TPSR_Ref TObjectID="58917"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1283.19,-1674)" width="16" x="5" y="-63"/></g>
    <g id="258216" primType="switch">
      <use class="kV110" height="45" transform="matrix(1.06667,0,0,1.11111,1283.19,-1545)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="58914" ObjectName="WenHuaZ.WenHuaZ_1041SW"/>
        <cge:Meas_Ref ObjectID="ME-258216"/><cge:TPSR_Ref TObjectID="58914"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1283.19,-1545)" width="16" x="5" y="-63"/></g>
    <g id="258261" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1321.95,-1752.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58923" ObjectName="WenHuaZ.WenHuaZ_10438SW"/>
        <cge:Meas_Ref ObjectID="ME-258261"/><cge:TPSR_Ref TObjectID="58923"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1321.95,-1752.7)" width="60" x="-1" y="-23"/></g>
    <g id="258259" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1323.95,-1680.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58922" ObjectName="WenHuaZ.WenHuaZ_10437SW"/>
        <cge:Meas_Ref ObjectID="ME-258259"/><cge:TPSR_Ref TObjectID="58922"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1323.95,-1680.7)" width="60" x="-1" y="-23"/></g>
    <g id="258257" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1323.95,-1614.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58921" ObjectName="WenHuaZ.WenHuaZ_10417SW"/>
        <cge:Meas_Ref ObjectID="ME-258257"/><cge:TPSR_Ref TObjectID="58921"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1323.95,-1614.7)" width="60" x="-1" y="-23"/></g>
    <g id="258299" primType="switch">
      <use class="kV110" height="25" transform="matrix(0.95082,0,0,0.666667,1298.95,-1523.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="58931" ObjectName="WenHuaZ.WenHuaZ_10447SW"/>
        <cge:Meas_Ref ObjectID="ME-258299"/><cge:TPSR_Ref TObjectID="58931"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1298.95,-1523.7)" width="60" x="-1" y="-23"/></g>
    <g id="258947" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,612.946,-1082.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59069" ObjectName="WenHuaZ.WenHuaZ_C0167SW"/>
        <cge:Meas_Ref ObjectID="ME-258947"/><cge:TPSR_Ref TObjectID="59069"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,612.946,-1082.7)" width="60" x="-1" y="-23"/></g>
    <g id="258966" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2188.95,-1082.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59075" ObjectName="WenHuaZ.WenHuaZ_C0267SW"/>
        <cge:Meas_Ref ObjectID="ME-258966"/><cge:TPSR_Ref TObjectID="59075"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2188.95,-1082.7)" width="60" x="-1" y="-23"/></g>
    <g id="259169" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,1210.96,-1631.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="59120" ObjectName="WenHuaZ.WenHuaZ_01047SW"/>
        <cge:Meas_Ref ObjectID="ME-259169"/><cge:TPSR_Ref TObjectID="59120"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,1210.96,-1631.92)" width="25" x="3" y="-100"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="16" transform="matrix(1.11111,0,0,1,478.734,-1110.59)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,478.734,-1110.59)" width="45" x="1" y="-15"/></g>
    <g id="258255" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,1232,-1754.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="58920" ObjectName="WenHuaZ.WenHuaZ_0104SW"/>
        <cge:Meas_Ref ObjectID="ME-258255"/><cge:TPSR_Ref TObjectID="58920"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,1232,-1754.34)" width="45" x="1" y="-15"/></g>
    <g id="259167" primType="switch">
      <use class="kV110" height="60" transform="matrix(0.666667,0,0,0.95082,717.965,-1633.92)" width="25" x="3" xlink:href="#Disconnector:shape1_0" y="-100"/>
      <metadata><cge:PSR_Ref ObjectID="59119" ObjectName="WenHuaZ.WenHuaZ_01037SW"/>
        <cge:Meas_Ref ObjectID="ME-259167"/><cge:TPSR_Ref TObjectID="59119"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(0.666667,0,0,0.95082,717.965,-1633.92)" width="25" x="3" y="-100"/></g>
    <g id="258177" primType="switch">
      <use class="kV110" height="16" transform="matrix(1.11111,0,0,1,739,-1758.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="58909" ObjectName="WenHuaZ.WenHuaZ_0103SW"/>
        <cge:Meas_Ref ObjectID="ME-258177"/><cge:TPSR_Ref TObjectID="58909"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,739,-1758.34)" width="45" x="1" y="-15"/></g>
    <g id="258782" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1101,-609)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59028" ObjectName="WenHuaZ.WenHuaZ_931XC"/>
        <cge:Meas_Ref ObjectID="ME-258782"/><cge:TPSR_Ref TObjectID="59028"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1101,-609)" width="22" x="-1" y="-30"/></g>
    <g id="258782" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1100" xlink:href="#Disconnector:shape3_0" y="-556"/>
      <metadata><cge:PSR_Ref ObjectID="59029" ObjectName="WenHuaZ.WenHuaZ_931XC1"/>
        <cge:Meas_Ref ObjectID="ME-258782"/><cge:TPSR_Ref TObjectID="59029"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1100" y="-556"/></g>
    <g id="258783" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1123.95,-503.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59030" ObjectName="WenHuaZ.WenHuaZ_93138SW"/>
        <cge:Meas_Ref ObjectID="ME-258783"/><cge:TPSR_Ref TObjectID="59030"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1123.95,-503.095)" width="60" x="-1" y="-23"/></g>
    <g id="258807" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1507,-609)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59033" ObjectName="WenHuaZ.WenHuaZ_932XC"/>
        <cge:Meas_Ref ObjectID="ME-258807"/><cge:TPSR_Ref TObjectID="59033"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1507,-609)" width="22" x="-1" y="-30"/></g>
    <g id="258807" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1506" xlink:href="#Disconnector:shape3_0" y="-556"/>
      <metadata><cge:PSR_Ref ObjectID="59034" ObjectName="WenHuaZ.WenHuaZ_932XC1"/>
        <cge:Meas_Ref ObjectID="ME-258807"/><cge:TPSR_Ref TObjectID="59034"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1506" y="-556"/></g>
    <g id="259092" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,668,-605)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59107" ObjectName="WenHuaZ.WenHuaZ_928XC"/>
        <cge:Meas_Ref ObjectID="ME-259092"/><cge:TPSR_Ref TObjectID="59107"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,668,-605)" width="22" x="-1" y="-30"/></g>
    <g id="259092" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="667" xlink:href="#Disconnector:shape3_0" y="-552"/>
      <metadata><cge:PSR_Ref ObjectID="59108" ObjectName="WenHuaZ.WenHuaZ_928XC1"/>
        <cge:Meas_Ref ObjectID="ME-259092"/><cge:TPSR_Ref TObjectID="59108"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="667" y="-552"/></g>
    <g id="259093" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,690.946,-499.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59109" ObjectName="WenHuaZ.WenHuaZ_92838SW"/>
        <cge:Meas_Ref ObjectID="ME-259093"/><cge:TPSR_Ref TObjectID="59109"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,690.946,-499.095)" width="60" x="-1" y="-23"/></g>
    <g id="259001" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1215,-605)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59083" ObjectName="WenHuaZ.WenHuaZ_C04XC"/>
        <cge:Meas_Ref ObjectID="ME-259001"/><cge:TPSR_Ref TObjectID="59083"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1215,-605)" width="22" x="-1" y="-30"/></g>
    <g id="259001" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1214" xlink:href="#Disconnector:shape3_0" y="-552"/>
      <metadata><cge:PSR_Ref ObjectID="59084" ObjectName="WenHuaZ.WenHuaZ_C04XC1"/>
        <cge:Meas_Ref ObjectID="ME-259001"/><cge:TPSR_Ref TObjectID="59084"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1214" y="-552"/></g>
    <g id="259002" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1242.95,-502.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59085" ObjectName="WenHuaZ.WenHuaZ_C0468SW"/>
        <cge:Meas_Ref ObjectID="ME-259002"/><cge:TPSR_Ref TObjectID="59085"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1242.95,-502.095)" width="60" x="-1" y="-23"/></g>
    <g id="258707" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,572,-608)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59013" ObjectName="WenHuaZ.WenHuaZ_927XC"/>
        <cge:Meas_Ref ObjectID="ME-258707"/><cge:TPSR_Ref TObjectID="59013"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,572,-608)" width="22" x="-1" y="-30"/></g>
    <g id="258707" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="571" xlink:href="#Disconnector:shape3_0" y="-555"/>
      <metadata><cge:PSR_Ref ObjectID="59014" ObjectName="WenHuaZ.WenHuaZ_927XC1"/>
        <cge:Meas_Ref ObjectID="ME-258707"/><cge:TPSR_Ref TObjectID="59014"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="571" y="-555"/></g>
    <g id="258708" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,594.946,-502.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59015" ObjectName="WenHuaZ.WenHuaZ_92738SW"/>
        <cge:Meas_Ref ObjectID="ME-258708"/><cge:TPSR_Ref TObjectID="59015"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,594.946,-502.095)" width="60" x="-1" y="-23"/></g>
    <g id="258732" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,804,-608)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59018" ObjectName="WenHuaZ.WenHuaZ_929XC"/>
        <cge:Meas_Ref ObjectID="ME-258732"/><cge:TPSR_Ref TObjectID="59018"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,804,-608)" width="22" x="-1" y="-30"/></g>
    <g id="258732" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="803" xlink:href="#Disconnector:shape3_0" y="-555"/>
      <metadata><cge:PSR_Ref ObjectID="59019" ObjectName="WenHuaZ.WenHuaZ_929XC1"/>
        <cge:Meas_Ref ObjectID="ME-258732"/><cge:TPSR_Ref TObjectID="59019"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="803" y="-555"/></g>
    <g id="258733" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,826.946,-502.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59020" ObjectName="WenHuaZ.WenHuaZ_92938SW"/>
        <cge:Meas_Ref ObjectID="ME-258733"/><cge:TPSR_Ref TObjectID="59020"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,826.946,-502.095)" width="60" x="-1" y="-23"/></g>
    <g id="258757" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,896,-607)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59023" ObjectName="WenHuaZ.WenHuaZ_930XC"/>
        <cge:Meas_Ref ObjectID="ME-258757"/><cge:TPSR_Ref TObjectID="59023"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,896,-607)" width="22" x="-1" y="-30"/></g>
    <g id="258757" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="895" xlink:href="#Disconnector:shape3_0" y="-554"/>
      <metadata><cge:PSR_Ref ObjectID="59024" ObjectName="WenHuaZ.WenHuaZ_930XC1"/>
        <cge:Meas_Ref ObjectID="ME-258757"/><cge:TPSR_Ref TObjectID="59024"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="895" y="-554"/></g>
    <g id="258758" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,918.946,-501.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59025" ObjectName="WenHuaZ.WenHuaZ_93038SW"/>
        <cge:Meas_Ref ObjectID="ME-258758"/><cge:TPSR_Ref TObjectID="59025"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,918.946,-501.095)" width="60" x="-1" y="-23"/></g>
    <g id="258917" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1952,-604)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59055" ObjectName="WenHuaZ.WenHuaZ_0952XC"/>
        <cge:Meas_Ref ObjectID="ME-258917"/><cge:TPSR_Ref TObjectID="59055"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1952,-604)" width="22" x="-1" y="-30"/></g>
    <g id="258917" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1951" xlink:href="#Disconnector:shape3_0" y="-546"/>
      <metadata><cge:PSR_Ref ObjectID="59056" ObjectName="WenHuaZ.WenHuaZ_0952XC1"/>
        <cge:Meas_Ref ObjectID="ME-258917"/><cge:TPSR_Ref TObjectID="59056"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1951" y="-546"/></g>
    <g id="258232" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1011,-605)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="58918" ObjectName="WenHuaZ.WenHuaZ_902XC"/>
        <cge:Meas_Ref ObjectID="ME-258232"/><cge:TPSR_Ref TObjectID="58918"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1011,-605)" width="22" x="-1" y="-30"/></g>
    <g id="258232" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1010" xlink:href="#Disconnector:shape3_0" y="-550"/>
      <metadata><cge:PSR_Ref ObjectID="58919" ObjectName="WenHuaZ.WenHuaZ_902XC1"/>
        <cge:Meas_Ref ObjectID="ME-258232"/><cge:TPSR_Ref TObjectID="58919"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1010" y="-550"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="16" transform="matrix(1.11111,0,0,1,687,-478.344)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,687,-478.344)" width="45" x="1" y="-15"/></g>
    <g id="258808" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1529.95,-503.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59035" ObjectName="WenHuaZ.WenHuaZ_93238SW"/>
        <cge:Meas_Ref ObjectID="ME-258808"/><cge:TPSR_Ref TObjectID="59035"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1529.95,-503.095)" width="60" x="-1" y="-23"/></g>
    <g id="258832" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1613,-610)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59038" ObjectName="WenHuaZ.WenHuaZ_933XC"/>
        <cge:Meas_Ref ObjectID="ME-258832"/><cge:TPSR_Ref TObjectID="59038"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1613,-610)" width="22" x="-1" y="-30"/></g>
    <g id="258832" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1612" xlink:href="#Disconnector:shape3_0" y="-557"/>
      <metadata><cge:PSR_Ref ObjectID="59039" ObjectName="WenHuaZ.WenHuaZ_933XC1"/>
        <cge:Meas_Ref ObjectID="ME-258832"/><cge:TPSR_Ref TObjectID="59039"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1612" y="-557"/></g>
    <g id="258833" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1635.95,-504.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59040" ObjectName="WenHuaZ.WenHuaZ_93338SW"/>
        <cge:Meas_Ref ObjectID="ME-258833"/><cge:TPSR_Ref TObjectID="59040"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1635.95,-504.095)" width="60" x="-1" y="-23"/></g>
    <g id="258857" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1702,-610)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59043" ObjectName="WenHuaZ.WenHuaZ_934XC"/>
        <cge:Meas_Ref ObjectID="ME-258857"/><cge:TPSR_Ref TObjectID="59043"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1702,-610)" width="22" x="-1" y="-30"/></g>
    <g id="258857" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1701" xlink:href="#Disconnector:shape3_0" y="-557"/>
      <metadata><cge:PSR_Ref ObjectID="59044" ObjectName="WenHuaZ.WenHuaZ_934XC1"/>
        <cge:Meas_Ref ObjectID="ME-258857"/><cge:TPSR_Ref TObjectID="59044"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1701" y="-557"/></g>
    <g id="258858" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1724.95,-504.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59045" ObjectName="WenHuaZ.WenHuaZ_93438SW"/>
        <cge:Meas_Ref ObjectID="ME-258858"/><cge:TPSR_Ref TObjectID="59045"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1724.95,-504.095)" width="60" x="-1" y="-23"/></g>
    <g id="259022" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,1355.19,-431)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="59092" ObjectName="WenHuaZ.WenHuaZ_C056SW"/>
        <cge:Meas_Ref ObjectID="ME-259022"/><cge:TPSR_Ref TObjectID="59092"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,1355.19,-431)" width="16" x="5" y="-63"/></g>
    <g id="259023" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1389.95,-432.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59093" ObjectName="WenHuaZ.WenHuaZ_C0567SW"/>
        <cge:Meas_Ref ObjectID="ME-259023"/><cge:TPSR_Ref TObjectID="59093"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1389.95,-432.095)" width="60" x="-1" y="-23"/></g>
    <g id="259020" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1360,-606)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59089" ObjectName="WenHuaZ.WenHuaZ_C05XC"/>
        <cge:Meas_Ref ObjectID="ME-259020"/><cge:TPSR_Ref TObjectID="59089"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1360,-606)" width="22" x="-1" y="-30"/></g>
    <g id="259020" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1359" xlink:href="#Disconnector:shape3_0" y="-553"/>
      <metadata><cge:PSR_Ref ObjectID="59090" ObjectName="WenHuaZ.WenHuaZ_C05XC1"/>
        <cge:Meas_Ref ObjectID="ME-259020"/><cge:TPSR_Ref TObjectID="59090"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1359" y="-553"/></g>
    <g id="259021" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.625,1387.95,-503.095)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59091" ObjectName="WenHuaZ.WenHuaZ_C0568SW"/>
        <cge:Meas_Ref ObjectID="ME-259021"/><cge:TPSR_Ref TObjectID="59091"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.625,1387.95,-503.095)" width="60" x="-1" y="-23"/></g>
    <g id="258982" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2290,-1252)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59077" ObjectName="WenHuaZ.WenHuaZ_C03XC"/>
        <cge:Meas_Ref ObjectID="ME-258982"/><cge:TPSR_Ref TObjectID="59077"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2290,-1252)" width="22" x="-1" y="-30"/></g>
    <g id="258982" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2289" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="59078" ObjectName="WenHuaZ.WenHuaZ_C03XC1"/>
        <cge:Meas_Ref ObjectID="ME-258982"/><cge:TPSR_Ref TObjectID="59078"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2289" y="-1199"/></g>
    <g id="258983" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2312.95,-1147.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59079" ObjectName="WenHuaZ.WenHuaZ_C0368SW"/>
        <cge:Meas_Ref ObjectID="ME-258983"/><cge:TPSR_Ref TObjectID="59079"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2312.95,-1147.7)" width="60" x="-1" y="-23"/></g>
    <g id="258984" primType="switch">
      <use class="kV10" height="45" transform="matrix(1.06667,0,0,1.11111,2285.19,-1075)" width="16" x="5" xlink:href="#Disconnector:shape0_0" y="-63"/>
      <metadata><cge:PSR_Ref ObjectID="59080" ObjectName="WenHuaZ.WenHuaZ_C036SW"/>
        <cge:Meas_Ref ObjectID="ME-258984"/><cge:TPSR_Ref TObjectID="59080"/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(1.06667,0,0,1.11111,2285.19,-1075)" width="16" x="5" y="-63"/></g>
    <g id="258985" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2312.95,-1081.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59081" ObjectName="WenHuaZ.WenHuaZ_C0367SW"/>
        <cge:Meas_Ref ObjectID="ME-258985"/><cge:TPSR_Ref TObjectID="59081"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2312.95,-1081.7)" width="60" x="-1" y="-23"/></g>
    <g id="258657" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2395,-1248)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59003" ObjectName="WenHuaZ.WenHuaZ_925XC"/>
        <cge:Meas_Ref ObjectID="ME-258657"/><cge:TPSR_Ref TObjectID="59003"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2395,-1248)" width="22" x="-1" y="-30"/></g>
    <g id="258657" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2394" xlink:href="#Disconnector:shape3_0" y="-1193"/>
      <metadata><cge:PSR_Ref ObjectID="59004" ObjectName="WenHuaZ.WenHuaZ_925XC1"/>
        <cge:Meas_Ref ObjectID="ME-258657"/><cge:TPSR_Ref TObjectID="59004"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2394" y="-1193"/></g>
    <g id="258658" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2417.95,-1141.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59005" ObjectName="WenHuaZ.WenHuaZ_92538SW"/>
        <cge:Meas_Ref ObjectID="ME-258658"/><cge:TPSR_Ref TObjectID="59005"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2417.95,-1141.7)" width="60" x="-1" y="-23"/></g>
    <g id="258682" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2485,-1249)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59008" ObjectName="WenHuaZ.WenHuaZ_926XC"/>
        <cge:Meas_Ref ObjectID="ME-258682"/><cge:TPSR_Ref TObjectID="59008"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2485,-1249)" width="22" x="-1" y="-30"/></g>
    <g id="258682" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2484" xlink:href="#Disconnector:shape3_0" y="-1194"/>
      <metadata><cge:PSR_Ref ObjectID="59009" ObjectName="WenHuaZ.WenHuaZ_926XC1"/>
        <cge:Meas_Ref ObjectID="ME-258682"/><cge:TPSR_Ref TObjectID="59009"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2484" y="-1194"/></g>
    <g id="258683" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2507.95,-1142.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59010" ObjectName="WenHuaZ.WenHuaZ_92638SW"/>
        <cge:Meas_Ref ObjectID="ME-258683"/><cge:TPSR_Ref TObjectID="59010"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2507.95,-1142.7)" width="60" x="-1" y="-23"/></g>
    <g id="259036" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,2591,-1252)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59095" ObjectName="WenHuaZ.WenHuaZ_994XC"/>
        <cge:Meas_Ref ObjectID="ME-259036"/><cge:TPSR_Ref TObjectID="59095"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,2591,-1252)" width="22" x="-1" y="-30"/></g>
    <g id="259036" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="2590" xlink:href="#Disconnector:shape3_0" y="-1199"/>
      <metadata><cge:PSR_Ref ObjectID="59096" ObjectName="WenHuaZ.WenHuaZ_994XC1"/>
        <cge:Meas_Ref ObjectID="ME-259036"/><cge:TPSR_Ref TObjectID="59096"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="2590" y="-1199"/></g>
    <g id="259037" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,2613.95,-1145.7)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59097" ObjectName="WenHuaZ.WenHuaZ_99438SW"/>
        <cge:Meas_Ref ObjectID="ME-259037"/><cge:TPSR_Ref TObjectID="59097"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,2613.95,-1145.7)" width="60" x="-1" y="-23"/></g>
    <g id="0" primType="switch">
      <use class="BuDaiDian" height="16" transform="matrix(1.11111,0,0,1,2609,-1125.34)" width="45" x="1" xlink:href="#Disconnector:shape4_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/>
        <cge:Meas_Ref ObjectID="ME-0"/></metadata>
    <rect fill="white" height="16" opacity="0" stroke="white" transform="matrix(1.11111,0,0,1,2609,-1125.34)" width="45" x="1" y="-15"/></g>
    <g id="258929" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1454.81,-1249.15)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59060" ObjectName="WenHuaZ.WenHuaZ_9001XC"/>
        <cge:Meas_Ref ObjectID="ME-258929"/><cge:TPSR_Ref TObjectID="59060"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1454.81,-1249.15)" width="22" x="-1" y="-30"/></g>
    <g id="258929" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1453.81" xlink:href="#Disconnector:shape3_0" y="-1184.32"/>
      <metadata><cge:PSR_Ref ObjectID="59061" ObjectName="WenHuaZ.WenHuaZ_9001XC1"/>
        <cge:Meas_Ref ObjectID="ME-258929"/><cge:TPSR_Ref TObjectID="59061"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1453.81" y="-1184.32"/></g>
    <g id="259058" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,1813,-605)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59099" ObjectName="WenHuaZ.WenHuaZ_995XC"/>
        <cge:Meas_Ref ObjectID="ME-259058"/><cge:TPSR_Ref TObjectID="59099"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,1813,-605)" width="22" x="-1" y="-30"/></g>
    <g id="259058" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="1812" xlink:href="#Disconnector:shape3_0" y="-552"/>
      <metadata><cge:PSR_Ref ObjectID="59100" ObjectName="WenHuaZ.WenHuaZ_995XC1"/>
        <cge:Meas_Ref ObjectID="ME-259058"/><cge:TPSR_Ref TObjectID="59100"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1812" y="-552"/></g>
    <g id="259059" primType="switch">
      <use class="kV10" height="25" transform="matrix(0.95082,0,0,0.666667,1835.95,-498.702)" width="60" x="-1" xlink:href="#Disconnector:shape5_0" y="-23"/>
      <metadata><cge:PSR_Ref ObjectID="59101" ObjectName="WenHuaZ.WenHuaZ_99538SW"/>
        <cge:Meas_Ref ObjectID="ME-259059"/><cge:TPSR_Ref TObjectID="59101"/></metadata>
    <rect fill="white" height="25" opacity="0" stroke="white" transform="matrix(0.95082,0,0,0.666667,1835.95,-498.702)" width="60" x="-1" y="-23"/></g>
    <g id="258930" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,0.96875,469.166,-611.342)" width="22" x="-1" xlink:href="#Disconnector:shape2_0" y="-30"/>
      <metadata><cge:PSR_Ref ObjectID="59062" ObjectName="WenHuaZ.WenHuaZ_90012XC"/>
        <cge:Meas_Ref ObjectID="ME-258930"/><cge:TPSR_Ref TObjectID="59062"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,0.96875,469.166,-611.342)" width="22" x="-1" y="-30"/></g>
    <g id="258930" primType="xcswitch">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="22" x="468.166" xlink:href="#Disconnector:shape3_0" y="-551.342"/>
      <metadata><cge:PSR_Ref ObjectID="59063" ObjectName="WenHuaZ.WenHuaZ_90012XC1"/>
        <cge:Meas_Ref ObjectID="ME-258930"/><cge:TPSR_Ref TObjectID="59063"/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="468.166" y="-551.342"/></g>
  </g>
  <g id="DynamicPointClass">
    <g id="0" primType="dynp_usual">
      <use height="62" transform="matrix(0.807692,0,0,0.983051,52.4145,-1567.28)" width="237" x="-2" xlink:href="#DynamicPoint:shape48_0" y="-61"/>
    <metadata/><rect fill="white" height="62" opacity="0" stroke="white" transform="matrix(0.807692,0,0,0.983051,52.4145,-1567.28)" width="237" x="-2" y="-61"/></g>
    <g id="293" primType="dynp_station">
      <use height="72.1282" transform="matrix(1,0,0,1,0,0)" width="139.104" x="80.3153" xlink:href="#DynamicPoint:shape47_0" y="-1638.72"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-293"/></metadata>
    <rect fill="white" height="72.1282" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="139.104" x="80.3153" y="-1638.72"/></g>
  </g>
  <g id="EnergyConsumerClass">
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="703" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61803" ObjectName="WenHuaZ.912Ld"/><cge:TPSR_Ref TObjectID="61803"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="703" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="787" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61844" ObjectName="WenHuaZ.913Ld"/><cge:TPSR_Ref TObjectID="61844"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="787" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1000" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61845" ObjectName="WenHuaZ.914Ld"/><cge:TPSR_Ref TObjectID="61845"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1000" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1086" xlink:href="#EnergyConsumer:shape1_0" y="-1120"/>
      <metadata><cge:PSR_Ref ObjectID="61846" ObjectName="WenHuaZ.915Ld"/><cge:TPSR_Ref TObjectID="61846"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1086" y="-1120"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1175" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61847" ObjectName="WenHuaZ.916Ld"/><cge:TPSR_Ref TObjectID="61847"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1175" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1265" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61848" ObjectName="WenHuaZ.917Ld"/><cge:TPSR_Ref TObjectID="61848"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1265" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1351" xlink:href="#EnergyConsumer:shape1_0" y="-1117"/>
      <metadata><cge:PSR_Ref ObjectID="61849" ObjectName="WenHuaZ.918Ld"/><cge:TPSR_Ref TObjectID="61849"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1351" y="-1117"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="804" xlink:href="#EnergyConsumer:shape1_0" y="-479"/>
      <metadata><cge:PSR_Ref ObjectID="61860" ObjectName="WenHuaZ.929Ld"/><cge:TPSR_Ref TObjectID="61860"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="804" y="-479"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="896" xlink:href="#EnergyConsumer:shape1_0" y="-478"/>
      <metadata><cge:PSR_Ref ObjectID="61861" ObjectName="WenHuaZ.930Ld"/><cge:TPSR_Ref TObjectID="61861"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="896" y="-478"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1101" xlink:href="#EnergyConsumer:shape1_0" y="-480"/>
      <metadata><cge:PSR_Ref ObjectID="61862" ObjectName="WenHuaZ.931Ld"/><cge:TPSR_Ref TObjectID="61862"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1101" y="-480"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1507" xlink:href="#EnergyConsumer:shape1_0" y="-480"/>
      <metadata><cge:PSR_Ref ObjectID="61863" ObjectName="WenHuaZ.932Ld"/><cge:TPSR_Ref TObjectID="61863"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1507" y="-480"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1534" xlink:href="#EnergyConsumer:shape1_0" y="-1118"/>
      <metadata><cge:PSR_Ref ObjectID="61850" ObjectName="WenHuaZ.919Ld"/><cge:TPSR_Ref TObjectID="61850"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1534" y="-1118"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1624" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61851" ObjectName="WenHuaZ.920Ld"/><cge:TPSR_Ref TObjectID="61851"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1624" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1710" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61852" ObjectName="WenHuaZ.921Ld"/><cge:TPSR_Ref TObjectID="61852"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1710" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1794" xlink:href="#EnergyConsumer:shape1_0" y="-1116"/>
      <metadata><cge:PSR_Ref ObjectID="61853" ObjectName="WenHuaZ.922Ld"/><cge:TPSR_Ref TObjectID="61853"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1794" y="-1116"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1884" xlink:href="#EnergyConsumer:shape1_0" y="-1117"/>
      <metadata><cge:PSR_Ref ObjectID="61854" ObjectName="WenHuaZ.923Ld"/><cge:TPSR_Ref TObjectID="61854"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1884" y="-1117"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2065.45" xlink:href="#EnergyConsumer:shape1_0" y="-1122"/>
      <metadata><cge:PSR_Ref ObjectID="61855" ObjectName="WenHuaZ.924Ld"/><cge:TPSR_Ref TObjectID="61855"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2065.45" y="-1122"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="572" xlink:href="#EnergyConsumer:shape1_0" y="-479"/>
      <metadata><cge:PSR_Ref ObjectID="61858" ObjectName="WenHuaZ.927Ld"/><cge:TPSR_Ref TObjectID="61858"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="572" y="-479"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1613" xlink:href="#EnergyConsumer:shape1_0" y="-481"/>
      <metadata><cge:PSR_Ref ObjectID="61864" ObjectName="WenHuaZ.933Ld"/><cge:TPSR_Ref TObjectID="61864"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1613" y="-481"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="1702" xlink:href="#EnergyConsumer:shape1_0" y="-481"/>
      <metadata><cge:PSR_Ref ObjectID="61865" ObjectName="WenHuaZ.934Ld"/><cge:TPSR_Ref TObjectID="61865"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1702" y="-481"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2395" xlink:href="#EnergyConsumer:shape1_0" y="-1119"/>
      <metadata><cge:PSR_Ref ObjectID="61856" ObjectName="WenHuaZ.925Ld"/><cge:TPSR_Ref TObjectID="61856"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2395" y="-1119"/></g>
    <g id="DEV-0">
      <use class="kV10" height="34" transform="matrix(1,0,0,1,0,0)" width="20" x="2485" xlink:href="#EnergyConsumer:shape1_0" y="-1120"/>
      <metadata><cge:PSR_Ref ObjectID="61857" ObjectName="WenHuaZ.926Ld"/><cge:TPSR_Ref TObjectID="61857"/></metadata>
    <rect fill="white" height="34" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="2485" y="-1120"/></g>
  </g>
  <g id="CapacitorClass">
    <g id="DEV-0">
      <use class="kV10" height="24" transform="matrix(0.939394,0,0,1,1210.91,-407)" width="35" x="-2" xlink:href="#Capacitor:shape0_0" y="-24"/>
      <metadata><cge:PSR_Ref ObjectID="59130" ObjectName="WenHuaZ.#4cp"/><cge:TPSR_Ref TObjectID="59130"/></metadata>
    <rect fill="white" height="24" opacity="0" stroke="white" transform="matrix(0.939394,0,0,1,1210.91,-407)" width="35" x="-2" y="-24"/></g>
    <g id="DEV-0">
      <use class="kV10" height="24" transform="matrix(0.939394,0,0,1,585.203,-987.632)" width="35" x="-2" xlink:href="#Capacitor:shape0_0" y="-24"/>
      <metadata><cge:PSR_Ref ObjectID="59127" ObjectName="WenHuaZ.#1cp"/><cge:TPSR_Ref TObjectID="59127"/></metadata>
    <rect fill="white" height="24" opacity="0" stroke="white" transform="matrix(0.939394,0,0,1,585.203,-987.632)" width="35" x="-2" y="-24"/></g>
    <g id="DEV-0">
      <use class="kV10" height="24" transform="matrix(0.939394,0,0,1,2162.74,-986.534)" width="35" x="-2" xlink:href="#Capacitor:shape0_0" y="-24"/>
      <metadata><cge:PSR_Ref ObjectID="59128" ObjectName="WenHuaZ.#2cp"/><cge:TPSR_Ref TObjectID="59128"/></metadata>
    <rect fill="white" height="24" opacity="0" stroke="white" transform="matrix(0.939394,0,0,1,2162.74,-986.534)" width="35" x="-2" y="-24"/></g>
    <g id="DEV-0">
      <use class="kV10" height="24" transform="matrix(0.939394,0,0,1,1355.91,-408)" width="35" x="-2" xlink:href="#Capacitor:shape0_0" y="-24"/>
      <metadata><cge:PSR_Ref ObjectID="59131" ObjectName="WenHuaZ.#5cp"/><cge:TPSR_Ref TObjectID="59131"/></metadata>
    <rect fill="white" height="24" opacity="0" stroke="white" transform="matrix(0.939394,0,0,1,1355.91,-408)" width="35" x="-2" y="-24"/></g>
    <g id="DEV-0">
      <use class="kV10" height="24" transform="matrix(0.939394,0,0,1,2286.05,-984.192)" width="35" x="-2" xlink:href="#Capacitor:shape0_0" y="-24"/>
      <metadata><cge:PSR_Ref ObjectID="59129" ObjectName="WenHuaZ.#3cp"/><cge:TPSR_Ref TObjectID="59129"/></metadata>
    <rect fill="white" height="24" opacity="0" stroke="white" transform="matrix(0.939394,0,0,1,2286.05,-984.192)" width="35" x="-2" y="-24"/></g>
  </g>
  <g id="GroundLineClass">
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="712" xlink:href="#GroundLine:shape0_0" y="-1379"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="712" y="-1379"/></g>
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="1159" xlink:href="#GroundLine:shape0_0" y="-1373"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="1159" y="-1373"/></g>
    <g id="DEV-0">
      <use class="JieDi" height="32" transform="matrix(1,0,0,1,0,0)" width="22" x="738.861" xlink:href="#GroundLine:shape0_0" y="-467"/>
    <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="738.861" y="-467"/></g>
  </g>
  <g id="OtherClass">
    <g id="DEV-0">
      <use class="kV10" height="48" transform="matrix(1,0,0,1,0,0)" width="37" x="1953" xlink:href="#Other:shape33_0" y="-499"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="37" x="1953" y="-499"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,530.734,-1049.61)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,530.734,-1049.61)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,1139,-1694.3)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,1139,-1694.3)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,1180,-1672.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,1180,-1672.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="18" transform="matrix(1,0,0,1,0,0)" width="52" x="836" xlink:href="#Other:shape39_0" y="-1368"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="18" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="52" x="836" y="-1368"/></g>
    <g id="DEV-0">
      <use class="kV10" height="51" transform="matrix(1,0,0,1,0,0)" width="17" x="1474.99" xlink:href="#Other:shape37_0" y="-1432.12"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="51" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17" x="1474.99" y="-1432.12"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,1194,-1366.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,1194,-1366.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,744,-1365.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,744,-1365.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="26" transform="matrix(0.956522,0,0,0.962963,646,-1696.3)" width="25" x="-1" xlink:href="#Other:shape63_0" y="-26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26" opacity="0" stroke="white" transform="matrix(0.956522,0,0,0.962963,646,-1696.3)" width="25" x="-1" y="-26"/></g>
    <g id="DEV-0">
      <use class="kV110" height="52" transform="matrix(1,0,0,0.981132,687,-1674.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,687,-1674.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV110" height="42" transform="matrix(1,0,0,1,0,0)" width="18" x="715" xlink:href="#Other:shape56_0" y="-1425"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="18" x="715" y="-1425"/></g>
    <g id="DEV-0">
      <use class="kV110" height="42" transform="matrix(1,0,0,1,0,0)" width="18" x="1162" xlink:href="#Other:shape56_0" y="-1422"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="42" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="18" x="1162" y="-1422"/></g>
    <g id="DEV-0">
      <use class="kV10" height="45" transform="matrix(0.793103,0,0,0.914894,2589.09,-1080.66)" width="27" x="1" xlink:href="#Other:shape28_0" y="-46"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(0.793103,0,0,0.914894,2589.09,-1080.66)" width="27" x="1" y="-46"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,2661,-1064.36)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,2661,-1064.36)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="53" transform="matrix(1,0,0,1,0,0)" width="17" x="2679" xlink:href="#Other:shape40_0" y="-1114"/>
    <metadata/><rect fill="white" height="53" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17" x="2679" y="-1114"/></g>
    <g id="DEV-0">
      <use class="kV10" height="45" transform="matrix(0.793103,0,0,0.914894,1811.09,-433.66)" width="27" x="1" xlink:href="#Other:shape28_0" y="-46"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="45" opacity="0" stroke="white" transform="matrix(0.793103,0,0,0.914894,1811.09,-433.66)" width="27" x="1" y="-46"/></g>
    <g id="DEV-0">
      <use class="kV10" height="32.8908" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="469.604" xlink:href="#Other:shape10_0" y="-1156.2"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="32.8908" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="30.7914" x="469.604" y="-1156.2"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="586.86" xlink:href="#Other:shape52_0" y="-980.426"/>
    <metadata/><rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="586.86" y="-980.426"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="586" xlink:href="#Other:shape51_0" y="-1080"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="586" y="-1080"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,562.242,-969.258)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,562.242,-969.258)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="48" transform="matrix(1,0,0,1,0,0)" width="43" x="1974" xlink:href="#Other:shape35_0" y="-1145"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="48" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="43" x="1974" y="-1145"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="2287.71" xlink:href="#Other:shape52_0" y="-976.986"/>
    <metadata/><rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="2287.71" y="-976.986"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="2286.85" xlink:href="#Other:shape51_0" y="-1076.56"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="2286.85" y="-1076.56"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,2263.09,-965.818)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,2263.09,-965.818)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="12" transform="matrix(1,0,0,1,0,0)" width="24" x="2164.4" xlink:href="#Other:shape52_0" y="-979.328"/>
    <metadata/><rect fill="white" height="12" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="24" x="2164.4" y="-979.328"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56" transform="matrix(1,0,0,1,0,0)" width="26" x="2163.54" xlink:href="#Other:shape51_0" y="-1078.9"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="26" x="2163.54" y="-1078.9"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="52" transform="matrix(1,0,0,0.981132,2139.78,-968.16)" width="17" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="52" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,2139.78,-968.16)" width="17" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="37.6411" transform="matrix(1,0,0,0.981132,672.469,-391.924)" width="12.547" x="-1" xlink:href="#Other:shape36_0" y="-52"/>
    <metadata/><rect fill="white" height="37.6411" opacity="0" stroke="white" transform="matrix(1,0,0,0.981132,672.469,-391.924)" width="12.547" x="-1" y="-52"/></g>
    <g id="DEV-0">
      <use class="kV10" height="26.7422" transform="matrix(1,0,0,1,0,0)" width="25.0353" x="665.482" xlink:href="#Other:shape10_0" y="-473.414"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="26.7422" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="25.0353" x="665.482" y="-473.414"/></g>
  </g>
  <g id="Base_MotifButtonClass">
    <g>
      
    <metadata/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="135,-1869 135,-1828 138,-1831 138,-1866 222,-1866 225,-1869" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="225,-1828 135,-1828 138,-1831 222,-1831 222,-1866 225,-1869" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="35" stroke="rgb(248,248,255)" width="84" x="138" y="-1866"/>
    <metadata/><rect fill="white" height="35" opacity="0" stroke="white" transform="" width="84" x="138" y="-1866"/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="12.2012,-1710.24 12.2012,-1662.24 15.2012,-1665.24 15.2012,-1707.24 303.201,-1707.24 306.201,-1710.24" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="306.201,-1662.24 12.2012,-1662.24 15.2012,-1665.24 303.201,-1665.24 303.201,-1707.24 306.201,-1710.24" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="42" stroke="rgb(248,248,255)" width="288" x="15.2012" y="-1707.24"/>
    <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="288" x="15.2012" y="-1707.24"/></g>
    <g>
      <polygon fill="rgb(255,255,255)" points="293.4,-1796.96 293.4,-1761.96 296.4,-1764.96 296.4,-1793.96 410.669,-1793.96 413.669,-1796.96" stroke="none"/>
      <polygon fill="rgb(163,163,167)" points="413.669,-1761.96 293.4,-1761.96 296.4,-1764.96 410.669,-1764.96 410.669,-1793.96 413.669,-1796.96" stroke="none"/>
      <rect fill="rgb(248,248,255)" height="29" stroke="rgb(248,248,255)" width="114.269" x="296.4" y="-1793.96"/>
    <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="114.269" x="296.4" y="-1793.96"/></g>
  </g>
  <g id="TextClass">
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-191.899" y="-1701">总有功：</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="553.993" y="-684.378">10kV#2段母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1946" y="-382">＃2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1946" y="-358">PT</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="469.234" y="-431.33">母</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="470.616" y="-396.891">联</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="467.852" y="-367.98">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1010" y="-390">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1010" y="-368">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1010" y="-346">主</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1010" y="-324">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1215" y="-382">4</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1215" y="-358">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1215" y="-334">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1215" y="-310">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="450" y="-1322">10kV#1段母线</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="35" stroke="rgb(0,0,0)" x="-117.111" y="-1846.56">110kV文华站</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-202.607" y="-1483.34">危险点说明：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-169.534" y="-880.415">联系方式：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-193" y="-1666">总无功：</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1471" y="-1137">母</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1471" y="-1115">联</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1471" y="-1093">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="871" y="-1415">63MVA</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1172" y="-1448">63MVA</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1982" y="-1015">#1 </text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1982" y="-993">PT</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="703" y="-1010">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="703" y="-988">荔</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="790" y="-1010">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="790" y="-988">港</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="641.716" y="-1009.75">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="635.632" y="-986.878">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="635.632" y="-964.878">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="635.632" y="-942.878">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="786" y="-1811">青岗绿文线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1272" y="-1812">青秋文琅</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="916.806" y="-1517.3">10347</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="914.302" y="-1463.9">10348</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="626" y="-1383">1010</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="815" y="-1649">103</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="811" y="-1580">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="812" y="-1722">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="739" y="-1691">01037</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="858" y="-1774">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="856" y="-1705">37</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="862" y="-1634">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="518" y="-1569">110kV#1段母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1387.33" y="-1562.25">110kV#2段母线</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1359.81" y="-1519.21">10447</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1365.11" y="-1463.91">10448</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1308" y="-1653">104</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1305" y="-1579">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1343" y="-1633">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1342" y="-1698">37</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1305" y="-1708">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1337" y="-1773">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1231" y="-1689">01047</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1025" y="-1560">100</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="974" y="-1561">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1091" y="-1562">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1021" y="-1483">17</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1079" y="-1482">27</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1075" y="-1383">1020</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="931" y="-1221">901</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="723" y="-1220">912</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="744" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="807" y="-1220">913</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="828" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1020" y="-1219">914</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1041" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1106" y="-1219">915</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1127" y="-1160">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1195" y="-1219">916</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1216" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1285" y="-1219">917</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1306" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1370" y="-1218">918</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1392" y="-1157">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="609" y="-1223">C01</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="606" y="-1110">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="631" y="-1100">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="630" y="-1165">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="495" y="-1223">911</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="516" y="-1163">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2184" y="-1223">C02</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2183" y="-1110">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2207" y="-1165">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2207" y="-1100">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1554" y="-1219">919</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1575" y="-1158">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1644" y="-1219">920</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1665" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1730" y="-1219">921</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1751" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1814" y="-1219">922</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1835" y="-1156">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1925" y="-1157">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2085" y="-1220">924</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2105" y="-1162">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="688" y="-571">928</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="709" y="-516">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1235" y="-571">C04</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1261" y="-519">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1232" y="-462">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1263" y="-448">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="592" y="-574">927</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="613" y="-519">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="824" y="-574">929</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="916" y="-573">930</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="937" y="-518">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="845" y="-519">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1121" y="-575">931</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1142" y="-520">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1527" y="-575">932</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1548" y="-520">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1974" y="-558">0952</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1031" y="-571">902</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="897" y="-1395">档位：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="894.394" y="-1373.56">油温1：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="893.394" y="-1349.06">油温2：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1309" y="-1430">档位：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1306.87" y="-1408.17">油温1：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1308" y="-1383.53">油温2：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="897.131" y="-1327.7">绕温：</text>
    <text fill="rgb(255,250,255)" font-family="SimSun" font-size="21" stroke="rgb(255,250,255)" x="1308.74" y="-1361.44">绕温：</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="33" stroke="rgb(0,0,0)" x="148" y="-1835">AVC</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" stroke="rgb(255,255,255)" x="-175.113" y="-646">图纸编号：</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" stroke="rgb(255,255,255)" transform="matrix(1.31699,0,0,1.09307,1001.04,-2959.5)" y="1254">母联甲</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="37" stroke="rgb(0,0,0)" x="378" y="-1828">GIS</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1633" y="-576">933</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1654" y="-521">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1722" y="-576">934</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1743" y="-521">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1380" y="-572">C05</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1406" y="-520">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1377" y="-463">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1408" y="-449">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1399" y="-410">5MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1251" y="-411">5MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2310" y="-1223">C03</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2307" y="-1109">6</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2331" y="-1164">68</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2331" y="-1099">67</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2398" y="-1028">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2398" y="-1006">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2398" y="-984">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2398" y="-962">四</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2415" y="-1222">925</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2436" y="-1159">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2526" y="-1160">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2597" y="-1034">4</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2597" y="-1012">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2597" y="-990">站</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2597" y="-968">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2611" y="-1223">994</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2632" y="-1163">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1474.81" y="-1215.15">9001</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1833" y="-576">995</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1854" y="-516">38</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-1033">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-1008.78">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-989">主</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="888" y="-967">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="997" y="-1008">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="997" y="-986">领</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="997" y="-964">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1083" y="-1006">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1083" y="-984">翡</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1083" y="-962">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1184" y="-1008">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1184" y="-986">环</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1184" y="-964">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1262" y="-1008">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1262" y="-986">滨</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1262" y="-964">甲</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1351" y="-1008">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1351" y="-986">越</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1534" y="-1011">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1534" y="-989">东</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1624" y="-1014">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1624" y="-992">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1712" y="-1015">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1712" y="-993">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1712" y="-971">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1794" y="-1020">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1794" y="-998">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1794" y="-976">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1794" y="-954">一</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1893" y="-1029">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1893" y="-1007">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1893" y="-985">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1893" y="-963">二</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2065" y="-1029">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2065" y="-1007">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2065" y="-985">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2065" y="-963">三</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2207.75" y="-1013.32">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2202.75" y="-991.319">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2202.75" y="-969.319">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2202.75" y="-947.319">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2336.27" y="-1017.51">3</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2331.27" y="-995.513">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2331.27" y="-973.513">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2331.27" y="-951.513">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2482" y="-1029">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2482" y="-1007">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2482" y="-985">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2482" y="-963">五</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="479" y="-1040">1</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="479" y="-1018">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="479" y="-996">接</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="479" y="-974">地</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="479" y="-952">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="671.617" y="-384">2</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="682" y="-384">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="674" y="-362">接</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="674" y="-340">地</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="674" y="-318">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="562" y="-393">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="562" y="-371">领</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="562" y="-349">乙</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="801" y="-390">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="801" y="-368">翡</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="801" y="-346">乙</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="896" y="-392">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="896" y="-370">环</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="896" y="-348">乙</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1363" y="-381">5</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1363" y="-357">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1363" y="-333">电</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="23" stroke="rgb(186,186,186)" x="1363" y="-309">容</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1703" y="-392">备</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1703" y="-370">用</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1703" y="-348">二</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1703" y="-326">十</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1703" y="-304">二</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1810" y="-385">5</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1810" y="-363">号</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1810" y="-341">站</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1810" y="-319">变</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="628" y="-1054">5MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2194" y="-1058">5MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2322" y="-1054">5MVar</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="486.502" y="-574.342">2</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="24" stroke="rgb(0,0,0)" transform="matrix(1.18889,0,0,1.17073,38.279,-3369.79)" y="1448">备自投软压板遥控</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1295.88" y="-1495.5">1044</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="873.254" y="-1436.25">#1主变</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="839.899" y="-1492.8">1034</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2513.73" y="-1215.71">926</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1173.8" y="-1472.15">#2主变</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1913.51" y="-1210.66">923</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1237.52" y="-1779.42">0104</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2005" y="-1208.63">0951</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="751.423" y="-1780.23">0103</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="491.901" y="-1086.57">9110</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="692.836" y="-449.912">9280</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2617.3" y="-1095.74">9940</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="1283.637" y="-1994.165">程</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="1284.637" y="-1994.165">程</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="811.567" y="-1993.665">线</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="771.567" y="-1994.665">程</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="23" stroke="rgb(255,0,0)" x="772.567" y="-1994.665">程</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="32" stroke="rgb(255,255,255)" x="240.617" y="-1836.3">备自投</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1103.5" y="-381.8">文</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1103.5" y="-359.8">滨</text>
    <text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1103.5" y="-337.8">乙</text>
    <text fill="rgb(255,0,0)" font-family="Serif" font-size="24" stroke="rgb(255,0,0)" transform="matrix(1.11123,0,0,1,-274.512,-1015.36)" x="915.84" y="-321.682">程</text>
    <text fill="rgb(255,0,0)" font-family="Serif" font-size="24" stroke="rgb(255,0,0)" x="784.98" y="-1336.6377">主</text>
    <text fill="rgb(255,0,0)" font-family="Serif" font-size="24" stroke="rgb(255,0,0)" transform="matrix(1.11123,0,0,1,175.76,-1016.8)" x="915.84" y="-321.682">程</text>
    <text fill="rgb(255,0,0)" font-family="Serif" font-size="24" stroke="rgb(255,0,0)" x="1235.252" y="-1338.0777">主</text>
    <text fill="rgb(0,0,0)" font-family="SimSun" font-size="33" stroke="rgb(0,0,0)" x="294.918" y="-1765.6">AVC防误</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1500.06" y="-380.343">文</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1500.06" y="-351.343">秀</text>
    <text fill="rgb(0,0,128)" font-family="Serif" font-size="27" stroke="rgb(0,0,128)" x="107.425" y="-1595.13">文华站</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" stroke="rgb(255,0,0)" x="713.718" y="-1067.9">二级</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" stroke="rgb(255,0,0)" x="713.718" y="-1042.9">用户</text>
    <text fill="rgb(255,255,255)" font-family="Serif" font-size="11" stroke="rgb(255,255,255)" x="1605.17" y="-809.602">过流定值：500A</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1610.7" y="-380.486">文</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1610.7" y="-351.486">冠</text>
  </g>
  <g id="LinkPointClass">
    <g href="110kV文华站青岗绿文103间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="815" y="-1649">103</text></g>
    <g href="110kV文华站青秋文琅104间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1308" y="-1653">104</text></g>
    <g href="110kV文华站母联甲100间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1025" y="-1560">100</text></g>
    <g href="110kV文华站1T间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="931" y="-1221">901</text></g>
    <g href="110kV文华站文荔线912间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="723" y="-1220">912</text></g>
    <g href="110kV文华站文港913间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="807" y="-1220">913</text></g>
    <g href="110kV文华站10kV文领甲914间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1020" y="-1219">914</text></g>
    <g href="110kV文华站文翡甲915间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1106" y="-1219">915</text></g>
    <g href="110kV文华站文环甲916间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1195" y="-1219">916</text></g>
    <g href="110kV文华站文滨甲917间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1285" y="-1219">917</text></g>
    <g href="110kV文华站文越918间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1370" y="-1218">918</text></g>
    <g href="110kV文华站1号电容C01间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="609" y="-1223">C01</text></g>
    <g href="110kV文华站1号接地变911间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="495" y="-1223">911</text></g>
    <g href="110kV文华站2号电容C02间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2184" y="-1223">C02</text></g>
    <g href="110kV文华站文东919间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1554" y="-1219">919</text></g>
    <g href="110kV文华站文电920间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1644" y="-1219">920</text></g>
    <g href="110kV文华站备用十921间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1730" y="-1219">921</text></g>
    <g href="110kV文华站备用十一922间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1814" y="-1219">922</text></g>
    <g href="110kV文华站备用十三924间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2085" y="-1220">924</text></g>
    <g href="110kV文华站2号接地变928间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="688" y="-571">928</text></g>
    <g href="110kV文华站4号电容C04间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1235" y="-571">C04</text></g>
    <g href="110kV文华站10kV文领乙927间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="592" y="-574">927</text></g>
    <g href="110kV文华站文翡乙929间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="824" y="-574">929</text></g>
    <g href="110kV文华站文环乙930间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="916" y="-573">930</text></g>
    <g href="110kV文华站文滨乙931间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1121" y="-575">931</text></g>
    <g href="110kV文华站文秀932间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1527" y="-575">932</text></g>
    <g href="110kV文华站2T间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1031" y="-571">902</text></g>
    <g href="110kV文华站文冠933间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1633" y="-576">933</text></g>
    <g href="110kV文华站备用二十二934间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1722" y="-576">934</text></g>
    <g href="110kV文华站5号电容C05间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1380" y="-572">C05</text></g>
    <g href="110kV文华站3号电容C03间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2310" y="-1223">C03</text></g>
    <g href="110kV文华站备用十四925间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2415" y="-1222">925</text></g>
    <g href="110kV文华站4号站变994间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="2611" y="-1223">994</text></g>
    <g href="110kV文华站母联甲9001间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1474.81" y="-1215.15">9001</text></g>
    <g href="110kV文华站5号站变995间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(186,186,186)" font-family="SimSun" font-size="21" stroke="rgb(186,186,186)" x="1833" y="-576">995</text></g>
    <g href="nn_一次接线索引图_110kV.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="92" stroke-width="1" width="332" x="-207" y="-1899"/></g>
    <g href="AVC_110kV文华站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="41" stroke-width="1" width="90" x="135" y="-1869"/></g>
    <g href="110kV文华站备自投软压板遥控画面模板.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="48" stroke-width="1" width="294" x="12.2012" y="-1710.24"/></g>
    <g href="110kV文华站1T间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="873.254" y="-1436.25">#1主变</text></g>
    <g href="110kV文华站备用十五926间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="2513.73" y="-1215.71">926</text></g>
    <g href="110kV文华站2T间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1173.8" y="-1472.15">#2主变</text></g>
    <g href="110kV文华站备用十二923间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" x="1913.51" y="-1210.66">923</text></g>
    <g href="110kV文华站BZT间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="32" stroke="rgb(255,255,255)" x="240.617" y="-1836.3">备自投</text></g>
    <g href="110kV文华站AVC控制防误状态监视图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="35" stroke-width="1" width="113.497" x="293.629" y="-1798.08"/></g>
  </g>
</svg>