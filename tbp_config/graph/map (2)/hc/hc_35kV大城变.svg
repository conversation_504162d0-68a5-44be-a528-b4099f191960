<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="3659174712311811" height="1100" id="thSvg" source="NR-PCS9000" viewBox="0 0 1650 1100" width="1650">
 <defs>
  <style type="text/css"><![CDATA[
.kv500{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(255,255,43);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(255,255,255);fill:none}
.v400{stroke:rgb(255,85,0);fill:none}
.kv1000{stroke:rgb(255,255,255);fill:none}
.kv800{stroke:rgb(255,255,255);fill:none}
.kv6{stroke:rgb(85,255,255);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.083333333333333" x2="6.166666666666667" y1="5.000000000000004" y2="23.08333333333333"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="25.41666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.5" x2="7.583333333333333" y1="8.91666666666667" y2="25.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.91666666666667" y2="0.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.966666666666667" x2="11.58333333333333" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="3.000000000000004" y2="29.99999999999999"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.91666666666667" y2="0.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.966666666666667" x2="11.58333333333333" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.383333333333334" x2="9.666666666666668" y1="29.83899551487831" y2="29.83899551487831"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="25.41666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.583333333333333" x2="12.5" y1="4.083333333333336" y2="26.16666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.966666666666667" x2="11.58333333333333" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.91666666666667" y2="0.75"/>
  </symbol>
  <symbol id="Disconnector:刀闸_3" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="25.41666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.583333333333333" x2="12.5" y1="4.083333333333336" y2="26.16666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.41666666666667" x2="2.75" y1="3.999999999999996" y2="26.08333333333333"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.966666666666667" x2="11.58333333333333" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.91666666666667" y2="0.75"/>
  </symbol>
  <symbol id="Disconnector:刀闸_4" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="25.41666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.583333333333333" x2="12.5" y1="4.083333333333336" y2="26.16666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.41666666666667" x2="2.75" y1="3.999999999999996" y2="26.08333333333333"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.966666666666667" x2="11.58333333333333" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.91666666666667" y2="0.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.416666666666665" x2="13.58333333333334" y1="15" y2="15"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1.166666666666666"/>
   <rect fill-opacity="0" height="9" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" width="4" x="13" y="2"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="18" y2="18"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="26" y2="26"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13.5" y2="1"/>
   <ellipse cx="15.15" cy="18.43333333333333" fill-opacity="0" rx="5" ry="5" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="25.03333333333334" fill-opacity="0" rx="5" ry="5.000000000000001" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.25"/>
   <use terminal-index="1" type="0" x="7.55" xlink:href="#terminal" y="29.8"/>
   <rect fill-opacity="0" height="29.38333333333333" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.5" x="0.25" y="0.3166666666666682"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.25"/>
   <use terminal-index="1" type="0" x="7.55" xlink:href="#terminal" y="29.8"/>
   <rect fill="rgb(255,0,0)" height="29.51666666666667" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" width="14.55" x="0.2000000000000002" y="0.2333333333333307"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.25"/>
   <use terminal-index="1" type="0" x="7.55" xlink:href="#terminal" y="29.8"/>
   <rect fill-opacity="0" height="29.41666666666667" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" width="14.63437599356729" x="0.1989573397660482" y="0.3333333333333321"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.3333333333333313" x2="14.75" y1="0.4166666666666625" y2="29.75"/>
  </symbol>
  <symbol id="Breaker:开关_3" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.25"/>
   <use terminal-index="1" type="0" x="7.55" xlink:href="#terminal" y="29.8"/>
   <rect fill-opacity="0" height="29.52708361381658" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" width="14.63437599356729" x="0.1989573397660482" y="0.2229163861834174"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.3333333333333313" x2="14.75" y1="0.4166666666666625" y2="29.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.66666666666666" x2="0.3333333333333357" y1="0.3333333333333286" y2="29.66666666666666"/>
  </symbol>
  <symbol id="Breaker:开关_4" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.25"/>
   <use terminal-index="1" type="0" x="7.55" xlink:href="#terminal" y="29.8"/>
   <rect fill-opacity="0" height="29.52708361381658" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" width="14.63437599356729" x="0.1989573397660482" y="0.2229163861834174"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.3333333333333313" x2="14.75" y1="0.4166666666666625" y2="29.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.66666666666666" x2="0.3333333333333357" y1="0.3333333333333286" y2="29.66666666666666"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.3333333333333357" x2="14.66666666666666" y1="15" y2="15"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,15,60">
   <use terminal-index="0" type="0" x="7.494791666467985" xlink:href="#terminal" y="2.214848350827236"/>
   <use terminal-index="1" type="0" x="7.494791666467985" xlink:href="#terminal" y="58.06885754967991"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="40.0021772523011" y2="52.87673556655578"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="8.129795084085242" y2="21.08285680269513"/>
   <path d="M 14.6344 14.8576 L 7.47927 8.11171 L 0.665625 15.2" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="19.10249505976812" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.10000053787234" x="0.4999997329711796" y="20.95201779543644"/>
   <path d="M 15.1 8.58403 L 7.41719 1.90666 L 0.665625 8.41282" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.6344 46.7535 L 7.41719 52.9396 L 0.2 46.4" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.7896 52.0948 L 7.51031 58.4577 L 0.2 52.4483" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,15,60">
   <use terminal-index="0" type="0" x="7.494791666467985" xlink:href="#terminal" y="2.214848350827236"/>
   <use terminal-index="1" type="0" x="7.494791666467985" xlink:href="#terminal" y="58.06885754967991"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="2.199998939514114" y2="21.08285680269513"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="40.0021772523011" y2="58.00000106811528"/>
   <rect fill="rgb(255,0,0)" height="19.10249505976812" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.10000053787234" x="0.4999997329711796" y="20.95201779543644"/>
   <path d="M 15.1 8.58403 L 7.41719 1.90666 L 0.665625 8.41282" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.7896 52.0948 L 7.51031 58.4577 L 0.2 52.4483" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,15,60">
   <use terminal-index="0" type="0" x="7.494791666467985" xlink:href="#terminal" y="2.214848350827236"/>
   <use terminal-index="1" type="0" x="7.494791666467985" xlink:href="#terminal" y="58.06885754967991"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="8.129795084085242" y2="21.08285680269513"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="40.0021772523011" y2="52.87673556655578"/>
   <path d="M 14.6344 14.8576 L 7.47927 8.11171 L 0.665625 15.2" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="19.10249505976812" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.10000053787234" x="0.4999997329711796" y="20.95201779543644"/>
   <path d="M 14.6344 46.7535 L 7.41719 52.9396 L 0.2 46.4" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 15.1 8.58403 L 7.41719 1.90666 L 0.665625 8.41282" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.7896 52.0948 L 7.51031 58.4577 L 0.2 52.4483" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4799997322082428" x2="14.50000026702882" y1="21.03999965820311" y2="40.0800003845215"/>
  </symbol>
  <symbol id="Breaker:手车开关_3" viewBox="0,0,15,60">
   <use terminal-index="0" type="0" x="7.494791666467985" xlink:href="#terminal" y="2.214848350827236"/>
   <use terminal-index="1" type="0" x="7.494791666467985" xlink:href="#terminal" y="58.06885754967991"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.50000026702882" x2="0.4799997322082428" y1="21.03999965820311" y2="40.0800003845215"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="40.0021772523011" y2="52.87673556655578"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4799997322082428" x2="14.50000026702882" y1="21.03999965820311" y2="40.0800003845215"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="8.129795084085242" y2="21.08285680269513"/>
   <path d="M 14.6344 14.8576 L 7.47927 8.11171 L 0.665625 15.2" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="19.10249505976812" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.10000053787234" x="0.4999997329711796" y="20.95201779543644"/>
   <path d="M 15.1 8.58403 L 7.41719 1.90666 L 0.665625 8.41282" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.6344 46.7535 L 7.41719 52.9396 L 0.2 46.4" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.7896 52.0948 L 7.51031 58.4577 L 0.2 52.4483" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_4" viewBox="0,0,15,60">
   <use terminal-index="0" type="0" x="7.494791666467985" xlink:href="#terminal" y="2.214848350827236"/>
   <use terminal-index="1" type="0" x="7.494791666467985" xlink:href="#terminal" y="58.06885754967991"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.50000026702882" x2="0.4799997322082428" y1="21.03999965820311" y2="40.0800003845215"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4799997322082428" x2="14.50000026702882" y1="21.03999965820311" y2="40.0800003845215"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="40.0021772523011" y2="52.87673556655578"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.507306705090934" x2="7.507306705090934" y1="8.129795084085242" y2="21.08285680269513"/>
   <path d="M 14.6344 14.8576 L 7.47927 8.11171 L 0.665625 15.2" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="19.10249505976812" rx="0" ry="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="14.10000053787234" x="0.4999997329711796" y="20.95201779543644"/>
   <path d="M 15.1 8.58403 L 7.41719 1.90666 L 0.665625 8.41282" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.6344 46.7535 L 7.41719 52.9396 L 0.2 46.4" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 14.7896 52.0948 L 7.51031 58.4577 L 0.2 52.4483" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.4999997329711778" x2="14.50000026702882" y1="30.36000001373291" y2="30.36000001373291"/>
  </symbol>
  <symbol id="Accessory:PT20_0" viewBox="0,0,12,15">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.880905815376279" x2="8.880905815376279" y1="3.016666666666667" y2="8.383333333333333"/>
   <rect fill-opacity="0" height="4.583333333333333" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" width="2.216666666666668" x="7.764239148709613" y="2.933333333333333"/>
   <path d="M 2.91424 8.16667 L 2.91424 0.833333 L 8.91424 0.833333 L 8.91424 2.83333" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="5.032438957580935" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" width="2.63090581537628" x="1.833333333333333" y="4.916666666666667"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.98400275644812" x2="2.98400275644812" y1="10.1" y2="12.62987422923119"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.416666666666666" x2="4.551338846229572" y1="12.72038294939949" y2="12.72038294939949"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.013305488919511" x2="4.103086941961449" y1="13.41697676708013" y2="13.41697676708013"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.467997777296497" x2="3.425814276607381" y1="13.93942213034062" y2="13.93942213034062"/>
   <ellipse cx="8.766735036264185" cy="9.960768829438555" fill-opacity="0" rx="1.54094793971251" ry="1.54094793971251" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="10.01673503626419" cy="11.71076882943856" fill-opacity="0" rx="1.54094793971251" ry="1.54094793971251" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="7.766735036264185" cy="11.71076882943856" fill-opacity="0" rx="1.54094793971251" ry="1.54094793971251" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.927179487179489" xlink:href="#terminal" y="29.23670605612998"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.966951566951569" x2="5.966951566951569" y1="5.58333333333333" y2="29.23670605612999"/>
   <path d="M 5.85 1.98333 L 3.41667 8.75 L 5.93333 5.31667 L 8.58333 8.58333 L 5.88571 1.76667" fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer:可调两卷变_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="1" x="9" xlink:href="#terminal" y="3.800000000000001"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10" x2="5.764697059015274" y1="11" y2="7.155681862840975"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.38666666666667" x2="8.199999999999999" y1="6.955681862840972" y2="11.13333333333333"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.053333333333333" x2="9.053333333333333" y1="10.15568186284097" y2="13.35568186284097"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.76" x2="15.82666666666666" y1="4.999999999999998" y2="7.266666666666665"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.3333333333333357" x2="16.8" y1="16.06666666666666" y2="4.933333333333332"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.08" x2="16.68" y1="4.960000000000001" y2="4.960000000000001"/>
   <ellipse cx="9.075681862840971" cy="10.55568186284097" fill-opacity="0" rx="6.689015196174304" ry="6.689015196174304" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer:可调两卷变_1" viewBox="0,0,18,30">
   <use terminal-index="1" type="1" x="9" xlink:href="#terminal" y="26.2"/>
   <path d="M 5.6 23.6667 L 12.8667 23.6667 L 9.06667 17.8 z" fill-opacity="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.075681862840971" cy="19.55568186284097" fill-opacity="0" rx="6.689015196174304" ry="6.689015196174304" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer:D-Y_0" viewBox="0,0,18,30">
   <ellipse cx="9" cy="9.5" fill-opacity="0" rx="8.36126899521788" ry="8.36126899521788" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 4.66667 11.0833 L 13.75 11.0833 L 9 3.75 z" fill-opacity="0" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="9" xlink:href="#terminal" y="1.25"/>
  </symbol>
  <symbol id="PowerTransformer:D-Y_1" viewBox="0,0,18,30">
   <ellipse cx="9" cy="20.75" fill-opacity="0" rx="8.36126899521788" ry="8.36126899521788" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="9" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.083333333333332" x2="13.08333333333333" y1="20.08333333333334" y2="26.08333333333334"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5" x2="9" y1="26" y2="20"/>
   <line fill="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9" x2="9" y1="20" y2="18"/>
   <use terminal-index="2" type="2" x="9" xlink:href="#terminal" y="20.3"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV大城变" fill="rgb(0,0,0)" height="1100" width="1650" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  
  <rect fill="rgb(0,0,0)" height="32" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" width="190" x="76" y="844.0000000059605" zvalue="2091230827"/>
  <rect fill="rgb(0,0,0)" height="32" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" width="190" x="74" y="921.0000000059605" zvalue="2091230828"/>
 </g>
 <g id="TextClass">
  <text fill="rgb(0,0,0)" font-family="SimSun" font-size="30" stroke="rgb(0,0,0)" text-anchor="middle" writing-mode="lr" x="186.5165994024636" xml:space="preserve" y="126.8102110649712" zvalue="2091230721"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="27" stroke="rgb(0,0,0)" text-anchor="middle" writing-mode="lr" x="236.0165994024637" xml:space="preserve" y="124.1551055324855" zvalue="2091230722">35kV大城站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="171" xml:space="preserve" y="864.0000000059605" zvalue="2091230827">母线信息</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="169" xml:space="preserve" y="941.0000000059605" zvalue="2091230828">公用、直流信息</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="846.0485620578135" xml:space="preserve" y="567.3102110649712" zvalue="2091230720">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="304" xml:space="preserve" y="715.1435393120414" zvalue="2091230724">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1256.799987774365" xml:space="preserve" y="954.9999999722961" zvalue="2091230726">八甫线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1093.293762379599" xml:space="preserve" y="960.0000000025401" zvalue="2091230728">义德线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="764.2206117448336" xml:space="preserve" y="955.00000000254" zvalue="2091230730">文华线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="597.5666808793551" xml:space="preserve" y="955.00000000254" zvalue="2091230732">三并线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="653.5331929770643" xml:space="preserve" y="437.3333326975505" zvalue="2091230734">0351</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="627.3499297698338" xml:space="preserve" y="546" zvalue="2091230737">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="459.5626161111958" xml:space="preserve" y="386.3102110649712" zvalue="2091230739">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="25" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="958.041176011973" xml:space="preserve" y="640" zvalue="2091230742">901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1428.811909724584" xml:space="preserve" y="952" zvalue="2091230769">10kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="25" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="957.483253479004" xml:space="preserve" y="488.0910219982024" zvalue="2091230774">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="952.3373565673828" xml:space="preserve" y="428.5601011397941" zvalue="2091230777">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="25" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="889.7420818601643" xml:space="preserve" y="305.1125548784014" zvalue="2091230781">303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="889.0064779879629" xml:space="preserve" y="238.9902434310851" zvalue="2091230783">3033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="888.7196914997843" xml:space="preserve" y="352.5183982686725" zvalue="2091230785">3031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="971.3883202120883" xml:space="preserve" y="132.8197631835936" zvalue="2091230787">03037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="949.2766404241765" xml:space="preserve" y="272.8197669310533" zvalue="2091230793">30337</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="948.2766404241765" xml:space="preserve" y="332.8197669310533" zvalue="2091230796">30317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1019.276640424177" xml:space="preserve" y="455.4788164016688" zvalue="2091230798">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="909.029156169441" xml:space="preserve" y="167.7612304687501" zvalue="2091230808">0303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1011" xml:space="preserve" y="183.7785118151027" zvalue="2091230811">PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="start" writing-mode="lr" x="998" xml:space="preserve" y="541" zvalue="2091230823">档位:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="start" writing-mode="lr" x="998" xml:space="preserve" y="567.3333320617676" zvalue="2091230824">油温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="135" xml:space="preserve" y="262.5" zvalue="2091230825">总有功(MW)：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130" xml:space="preserve" y="306.1000061035157" zvalue="2091230826">总无功(Mvar)：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="860.8292048211979" xml:space="preserve" y="138.75" zvalue="2091230830">都巴红线大城支线</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="787.8749999857307" xml:space="preserve" y="42.66668701171804" zvalue="2091230830">P(MW):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="784.6332997012319" xml:space="preserve" y="62.33334350585898" zvalue="2091230832">Q(Mvar):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="799.1415994167328" xml:space="preserve" y="82" zvalue="2091230833">I(A):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="481.6818181818178" xml:space="preserve" y="362.4452641241476" zvalue="2091230836">Uca(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="481.6818181818178" xml:space="preserve" y="336.2671635382101" zvalue="2091230837">Ubc(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="480.9999999999997" xml:space="preserve" y="310.0890629522728" zvalue="2091230838">Uab(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="488.5000000000003" xml:space="preserve" y="283.9109318487569" zvalue="2091230839">Uc(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="487.8181818181822" xml:space="preserve" y="257.7328312628192" zvalue="2091230840">Ub(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="487.8181818181822" xml:space="preserve" y="231.5547306768819" zvalue="2091230841">Ua(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="376.6818181818178" xml:space="preserve" y="692.4452717535421" zvalue="2091230842">Uca(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="376.6818181818178" xml:space="preserve" y="666.2671635382101" zvalue="2091230843">Ubc(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="375.9999999999997" xml:space="preserve" y="640.089055322878" zvalue="2091230844">Uab(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="383.5000000000003" xml:space="preserve" y="613.9109471075459" zvalue="2091230845">Uc(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="382.8181818181822" xml:space="preserve" y="587.7328388922139" zvalue="2091230846">Ub(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="382.8181818181822" xml:space="preserve" y="561.5547306768818" zvalue="2091230847">Ua(kV):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="821.8749999857307" xml:space="preserve" y="470" zvalue="2091230873">P(MW):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="818.6332997012319" xml:space="preserve" y="493" zvalue="2091230874">Q(Mvar):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="833.1415994167328" xml:space="preserve" y="516" zvalue="2091230875">I(A):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="831.8749999857307" xml:space="preserve" y="623" zvalue="2091230876">P(MW):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="828.6332997012319" xml:space="preserve" y="646" zvalue="2091230877">Q(Mvar):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="843.1415994167328" xml:space="preserve" y="669" zvalue="2091230878">I(A):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="378.8749999857307" xml:space="preserve" y="994" zvalue="2091230879">P(MW):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="375.6332997012319" xml:space="preserve" y="1017" zvalue="2091230880">Q(Mvar):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="390.1415994167328" xml:space="preserve" y="1040" zvalue="2091230881">I(A):</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1232.340909090909" xml:space="preserve" y="423.4937128626336" zvalue="2091237314">3911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="14" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1356.107847855209" xml:space="preserve" y="455.3181818181818" zvalue="2091237318">39118</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1281.245178883501" xml:space="preserve" y="605.7914739955489" zvalue="2091237322">35kV2号站变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="717.7766404241765" xml:space="preserve" y="415.4788164016688" zvalue="2091237329">3117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="717.7766404241765" xml:space="preserve" y="460.4788164016688" zvalue="2091237331">03517</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="949.2766404241765" xml:space="preserve" y="212.8197669310533" zvalue="2091237342">30338</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="927.5475770768649" xml:space="preserve" y="955.00000000254" zvalue="2091237350">箐盛线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="25" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1185.742081860164" xml:space="preserve" y="308.445888196322" zvalue="2091237386">305</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1185.006477987963" xml:space="preserve" y="242.3235767490057" zvalue="2091237388">3053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1184.719691499784" xml:space="preserve" y="355.8517315865931" zvalue="2091237390">3051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1252.388320212088" xml:space="preserve" y="136.1530965015142" zvalue="2091237392">03057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1245.276640424177" xml:space="preserve" y="276.1531002489739" zvalue="2091237397">30537</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1244.276640424177" xml:space="preserve" y="336.1531002489739" zvalue="2091237400">30517</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1205.029156169441" xml:space="preserve" y="171.0945637866707" zvalue="2091237403">0305</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1307" xml:space="preserve" y="187.1118451330233" zvalue="2091237406">PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="22" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1156.829204821198" xml:space="preserve" y="142.0833333179206" zvalue="2091237410">琳大线</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="1083.874999985731" xml:space="preserve" y="42.66668701171878" zvalue="2091237410">P(MW):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="1080.633299701232" xml:space="preserve" y="62.33334341868022" zvalue="2091237412">Q(Mvar):</text>
  <text fill="rgb(0,170,0)" font-family="SimSun" font-size="16" stroke="rgb(0,170,0)" text-anchor="middle" writing-mode="lr" x="1095.141599416733" xml:space="preserve" y="82.00000006406009" zvalue="2091237413">I(A):</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1245.276640424177" xml:space="preserve" y="216.1531002489739" zvalue="2091237418">30538</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="14" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="952.2612831233034" xml:space="preserve" y="690" zvalue="2091237469">9011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1281.245178883501" xml:space="preserve" y="620.7914739955489" zvalue="2091237471">S11-M-501/35</text>
 </g>
 <g id="ButtonClass">
  <g href="hc_35kV乙圩站_母线测控信息.svg"><rect fill-opacity="0" height="32" width="190" x="76" y="844.0000000059605" zvalue="2091230827"/></g>
  <g href="hc_35kV乙圩站_公用测控保护信息.svg"><rect fill-opacity="0" height="32" width="190" x="74" y="921.0000000059605" zvalue="2091230828"/></g>
 </g>
 <g id="PowerTransformerClass">
  <g id="152">
   <g id="1520">
    <use class="kv35" height="30" transform="rotate(0,896.337,507) scale(3.22222,3.03333) translate(-618.164,-339.857)" width="18" x="896.3373565673828" xlink:href="#PowerTransformer:可调两卷变_0" y="507" zvalue="2091230719"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1521">
    <use class="kv10" height="30" transform="rotate(0,896.337,507) scale(3.22222,3.03333) translate(-618.164,-339.857)" width="18" x="896.3373565673828" xlink:href="#PowerTransformer:可调两卷变_1" y="507" zvalue="2091230719"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="5348024646631426" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="5348024646631426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,896.337,507) scale(3.22222,3.03333) translate(-618.164,-339.857)" width="18" x="896.3373565673828" y="507"/></g>
  <g id="165">
   <g id="1650">
    <use class="kv35" height="30" transform="rotate(0,1250.34,511.532) scale(2.52271,2.65063) translate(-754.704,-318.547)" width="18" x="1250.336373681974" xlink:href="#PowerTransformer:D-Y_0" y="511.5320643246449" zvalue="2091237321"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1651">
    <use class="v400" height="30" transform="rotate(0,1250.34,511.532) scale(2.52271,2.65063) translate(-754.704,-318.547)" width="18" x="1250.336373681974" xlink:href="#PowerTransformer:D-Y_1" y="511.5320643246449" zvalue="2091237321"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="5348024633917442" ObjectName="35kV2号站变"/>
   <cge:TPSR_Ref TObjectID="5348024633917442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1250.34,511.532) scale(2.52271,2.65063) translate(-754.704,-318.547)" width="18" x="1250.336373681974" y="511.5320643246449"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="149">
   <path class="kv10" d="M 382 708.144 L 1543 708.144" stroke-width="5" zvalue="2091230723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="14918173840703490" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="14918173840703490"/></metadata>
  <path d="M 382 708.144 L 1543 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv35" d="M 516 380.31 L 1471 380.31" stroke-width="5" zvalue="2091230738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="14918173840637954" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="14918173840637954"/></metadata>
  <path d="M 516 380.31 L 1471 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="148">
   <use class="kv10" height="30" transform="rotate(180,1251.8,905.415) scale(0.833333,0.842495) translate(238.36,139.267)" width="12" x="1251.799987684958" xlink:href="#EnergyConsumer:负荷_0" y="905.4149287410394" zvalue="2091230725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999566852" ObjectName="八甫线"/>
   <cge:TPSR_Ref TObjectID="4785077999566852"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1251.8,905.415) scale(0.833333,0.842495) translate(238.36,139.267)" width="12" x="1251.799987684958" y="905.4149287410394"/></g>
  <g id="260">
   <use class="kv10" height="30" transform="rotate(180,1088.47,910.415) scale(0.472235,0.842495) translate(1204.47,140.202)" width="12" x="1088.474451874034" xlink:href="#EnergyConsumer:负荷_0" y="910.4149284827524" zvalue="2091230727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999501316" ObjectName="义德线"/>
   <cge:TPSR_Ref TObjectID="4785077999501316"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1088.47,910.415) scale(0.472235,0.842495) translate(1204.47,140.202)" width="12" x="1088.474451874034" y="910.4149284827524"/></g>
  <g id="147">
   <use class="kv10" height="30" transform="rotate(180,761.821,905.415) scale(0.472235,0.842495) translate(839.403,139.267)" width="12" x="761.820521209972" xlink:href="#EnergyConsumer:负荷_0" y="905.4149284827524" zvalue="2091230729"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999435780" ObjectName="文华线"/>
   <cge:TPSR_Ref TObjectID="4785077999435780"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,761.821,905.415) scale(0.472235,0.842495) translate(839.403,139.267)" width="12" x="761.820521209972" y="905.4149284827524"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(180,595.167,905.415) scale(0.472235,0.842495) translate(653.152,139.267)" width="12" x="595.1665903444934" xlink:href="#EnergyConsumer:负荷_0" y="905.4149284827524" zvalue="2091230731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078004744196" ObjectName="三并线"/>
   <cge:TPSR_Ref TObjectID="4785078004744196"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,595.167,905.415) scale(0.472235,0.842495) translate(653.152,139.267)" width="12" x="595.1665903444934" y="905.4149284827524"/></g>
  <g id="336">
   <use class="kv10" height="30" transform="rotate(180,925.147,905.415) scale(0.472235,0.842495) translate(1021.94,139.267)" width="12" x="925.1474865420032" xlink:href="#EnergyConsumer:负荷_0" y="905.4149284827524" zvalue="2091237349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077404827652" ObjectName="箐盛线"/>
   <cge:TPSR_Ref TObjectID="4785077404827652"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,925.147,905.415) scale(0.472235,0.842495) translate(1021.94,139.267)" width="12" x="925.1474865420032" y="905.4149284827524"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="145">
   <use class="kv35" height="30" transform="rotate(0,620.2,420) scale(0.866667,0.833333) translate(95.4154,84)" width="15" x="620.1998596437311" xlink:href="#Disconnector:刀闸_0" y="419.999999364217" zvalue="2091230733"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078000418820" ObjectName="0351"/>
   <cge:TPSR_Ref TObjectID="4785078000418820"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,620.2,420) scale(0.866667,0.833333) translate(95.4154,84)" width="15" x="620.1998596437311" y="419.999999364217"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(0,920.337,411.802) scale(0.666667,0.817231) translate(460.169,92.0969)" width="15" x="920.3373565673828" xlink:href="#Disconnector:刀闸_0" y="411.8016317576439" zvalue="2091230776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999894532" ObjectName="3011"/>
   <cge:TPSR_Ref TObjectID="4785077999894532"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920.337,411.802) scale(0.666667,0.817231) translate(460.169,92.0969)" width="15" x="920.3373565673828" y="411.8016317576439"/></g>
  <g id="123">
   <use class="kv35" height="30" transform="rotate(0,855.771,219.802) scale(0.666667,0.817231) translate(427.885,49.1573)" width="15" x="855.7706867872907" xlink:href="#Disconnector:刀闸_0" y="219.801631757644" zvalue="2091230781"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999828996" ObjectName="3033"/>
   <cge:TPSR_Ref TObjectID="4785077999828996"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.771,219.802) scale(0.666667,0.817231) translate(427.885,49.1573)" width="15" x="855.7706867872907" y="219.801631757644"/></g>
  <g id="122">
   <use class="kv35" height="30" transform="rotate(0,855.22,335.76) scale(0.666667,-0.817231) translate(427.61,-776.61)" width="15" x="855.2196914997842" xlink:href="#Disconnector:刀闸_0" y="335.7599288865223" zvalue="2091230784"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999763460" ObjectName="3031"/>
   <cge:TPSR_Ref TObjectID="4785077999763460"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.22,335.76) scale(0.666667,-0.817231) translate(427.61,-776.61)" width="15" x="855.2196914997842" y="335.7599288865223"/></g>
  <g id="103">
   <use class="kv35" height="30" transform="rotate(270,898.512,173.261) scale(-0.666667,0.817231) translate(-2246.28,38.7488)" width="15" x="898.5122174051404" xlink:href="#Disconnector:刀闸_0" y="173.2612304687501" zvalue="2091230807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078303326212" ObjectName="0303"/>
   <cge:TPSR_Ref TObjectID="4785078303326212"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,898.512,173.261) scale(-0.666667,0.817231) translate(-2246.28,38.7488)" width="15" x="898.5122174051404" y="173.2612304687501"/></g>
  <g id="846">
   <use class="kv35" height="30" transform="rotate(0,1268,412.69) scale(0.666667,0.817231) translate(634,92.2955)" width="15" x="1268" xlink:href="#Disconnector:刀闸_0" y="412.689788935029" zvalue="2091237313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077999697924" ObjectName="3911"/>
   <cge:TPSR_Ref TObjectID="4785077999697924"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1268,412.69) scale(0.666667,0.817231) translate(634,92.2955)" width="15" x="1268" y="412.689788935029"/></g>
  <g id="221">
   <use class="kv35" height="30" transform="rotate(0,1151.77,223.135) scale(0.666667,0.817231) translate(575.885,49.9028)" width="15" x="1151.770686787291" xlink:href="#Disconnector:刀闸_0" y="223.1349650755646" zvalue="2091237386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009645571" ObjectName="3053"/>
   <cge:TPSR_Ref TObjectID="4785077009645571"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1151.77,223.135) scale(0.666667,0.817231) translate(575.885,49.9028)" width="15" x="1151.770686787291" y="223.1349650755646"/></g>
  <g id="220">
   <use class="kv35" height="30" transform="rotate(0,1151.22,339.093) scale(0.666667,-0.817231) translate(575.61,-784.023)" width="15" x="1151.219691499784" xlink:href="#Disconnector:刀闸_0" y="339.0932622044429" zvalue="2091237389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009252355" ObjectName="3051"/>
   <cge:TPSR_Ref TObjectID="4785077009252355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1151.22,339.093) scale(0.666667,-0.817231) translate(575.61,-784.023)" width="15" x="1151.219691499784" y="339.0932622044429"/></g>
  <g id="211">
   <use class="kv35" height="30" transform="rotate(270,1194.51,176.595) scale(-0.666667,0.817231) translate(-2986.28,39.4943)" width="15" x="1194.512217405141" xlink:href="#Disconnector:刀闸_0" y="176.5945637866707" zvalue="2091237402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009383427" ObjectName="0305"/>
   <cge:TPSR_Ref TObjectID="4785077009383427"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1194.51,176.595) scale(-0.666667,0.817231) translate(-2986.28,39.4943)" width="15" x="1194.512217405141" y="176.5945637866707"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,918.761,674) scale(0.866667,0.833333) translate(141.348,134.8)" width="15" x="918.7612831233034" xlink:href="#Disconnector:刀闸_0" y="674" zvalue="2091237468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077789982723" ObjectName="9011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,918.761,674) scale(0.866667,0.833333) translate(141.348,134.8)" width="15" x="918.7612831233034" y="674"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="143">
   <use class="kv35" height="15" transform="rotate(0,604.7,473.5) scale(3.66667,3.66667) translate(-439.782,-344.364)" width="12" x="604.6998595396676" xlink:href="#Accessory:PT20_0" y="473.5" zvalue="2091230736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078003892228" ObjectName="35kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,604.7,473.5) scale(3.66667,3.66667) translate(-439.782,-344.364)" width="12" x="604.6998595396676" y="473.5"/></g>
  <g id="273">
   <use class="kv10" height="15" transform="rotate(0,1398.06,818.5) scale(3.66667,3.66667) translate(-1016.77,-595.273)" width="12" x="1398.060000002289" xlink:href="#Accessory:PT20_0" y="818.5" zvalue="2091230768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078000812036" ObjectName="10VⅠ母PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1398.06,818.5) scale(3.66667,3.66667) translate(-1016.77,-595.273)" width="12" x="1398.060000002289" y="818.5"/></g>
  <g id="101">
   <use class="kv35" height="30" transform="rotate(270,947,154.279) scale(1.60157,1.60157) translate(-385.704,-57.9487)" width="30" x="947" xlink:href="#Accessory:PT7_0" y="154.2785118151028" zvalue="2091230810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077399715844" ObjectName="都巴红线大城支线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,947,154.279) scale(1.60157,1.60157) translate(-385.704,-57.9487)" width="30" x="947" y="154.2785118151028"/></g>
  <g id="209">
   <use class="kv35" height="30" transform="rotate(270,1243,157.612) scale(1.60157,1.60157) translate(-496.885,-59.2008)" width="30" x="1243" xlink:href="#Accessory:PT7_0" y="157.6118451330234" zvalue="2091237405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009317891" ObjectName="琳大线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1243,157.612) scale(1.60157,1.60157) translate(-496.885,-59.2008)" width="30" x="1243" y="157.6118451330234"/></g>
 </g>
 <g id="BreakerClass">
  <g id="229">
   <use class="kv10" height="60" transform="rotate(0,919.841,601) scale(0.733333,1.06667) translate(334.488,-37.5625)" width="15" x="919.8411760119729" xlink:href="#Breaker:手车开关_0" y="601" zvalue="2091230741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="5066550094135301" ObjectName="901"/>
   <cge:TPSR_Ref TObjectID="5066550094135301"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,919.841,601) scale(0.733333,1.06667) translate(334.488,-37.5625)" width="15" x="919.8411760119729" y="601"/></g>
  <g id="129">
   <use class="kv35" height="30" transform="rotate(0,920.337,468.833) scale(0.666667,-0.817231) translate(460.169,-1072.52)" width="15" x="920.337356567383" xlink:href="#Breaker:开关_0" y="468.8325526160521" zvalue="2091230773"/>
   <metadata>
    <cge:PSR_Ref ObjectID="5066550154625029" ObjectName="301"/>
   <cge:TPSR_Ref TObjectID="5066550154625029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920.337,468.833) scale(0.666667,-0.817231) translate(460.169,-1072.52)" width="15" x="920.337356567383" y="468.8325526160521"/></g>
  <g id="124">
   <use class="kv35" height="30" transform="rotate(0,855.337,284.833) scale(0.666667,-0.817231) translate(427.669,-663.366)" width="15" x="855.337356567383" xlink:href="#Breaker:开关_0" y="284.8325526160522" zvalue="2091230780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="5066550154559493" ObjectName="303"/>
   <cge:TPSR_Ref TObjectID="5066550154559493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.337,284.833) scale(0.666667,-0.817231) translate(427.669,-663.366)" width="15" x="855.337356567383" y="284.8325526160522"/></g>
  <g id="222">
   <use class="kv35" height="30" transform="rotate(0,1151.34,288.166) scale(0.666667,-0.817231) translate(575.669,-670.778)" width="15" x="1151.337356567383" xlink:href="#Breaker:开关_0" y="288.1658859339728" zvalue="2091237385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="5066550263414789" ObjectName="305"/>
   <cge:TPSR_Ref TObjectID="5066550263414789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1151.34,288.166) scale(0.666667,-0.817231) translate(575.669,-670.778)" width="15" x="1151.337356567383" y="288.1658859339728"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="128">
   <path class="kv35" d="M 925.337 518.527 L 925.337 493.145" stroke-width="2" zvalue="2091230774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.337 518.527 L 925.337 493.145" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 925.371 468.996 L 925.378 436.109" stroke-width="2" zvalue="2091230777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.371 468.996 L 925.378 436.109" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 925.396 412.207 L 925.396 380.31" stroke-width="2" zvalue="2091230779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="142@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.396 412.207 L 925.396 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 860.811 244.109 L 860.811 284.996" stroke-width="2" zvalue="2091230788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@1" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.811 244.109 L 860.811 284.996" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 860.337 309.145 L 860.26 335.97" stroke-width="2" zvalue="2091230789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.337 309.145 L 860.26 335.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 860.829 220.207 L 860.829 173.89" stroke-width="2" zvalue="2091230790"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.829 220.207 L 860.829 173.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 899.312 268.31 L 860.811 268.31" stroke-width="2" zvalue="2091230793"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 899.312 268.31 L 860.811 268.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 969.312 450.969 L 925.375 450.969" stroke-width="2" zvalue="2091230799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.312 450.969 L 925.375 450.969" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 899.312 328.31 L 860.282 328.3" stroke-width="2" zvalue="2091230806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 899.312 328.31 L 860.282 328.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 898.918 178.32 L 860.829 178.32" stroke-width="2" zvalue="2091230808"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.918 178.32 L 860.829 178.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 922.82 178.302 L 948.868 178.302" stroke-width="2" zvalue="2091230811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 922.82 178.302 L 948.868 178.302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 860.278 359.872 L 860.278 380.31" stroke-width="2" zvalue="2091230822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="142@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.278 359.872 L 860.278 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 925.337 603.363 L 925.337 586.473" stroke-width="2" zvalue="2091230858"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.337 603.363 L 925.337 586.473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 954.312 143.31 L 932 143.31 L 932 178.302" stroke-width="2" zvalue="2091230887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.312 143.31 L 932 143.31 L 932 178.302" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv35" d="M 626.776 420.413 L 626.776 380.31" stroke-width="1" zvalue="2091237337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="142@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.776 420.413 L 626.776 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="438">
   <path class="kv35" d="M 626.753 444.786 L 626.7 476.25" stroke-width="1" zvalue="2091237338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.753 444.786 L 626.7 476.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv35" d="M 669.312 410.969 L 626.776 410.969" stroke-width="1" zvalue="2091237339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="437" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.312 410.969 L 626.776 410.969" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv35" d="M 669.312 455.969 L 626.734 455.969" stroke-width="1" zvalue="2091237340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="438" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.312 455.969 L 626.734 455.969" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv35" d="M 899.312 208.31 L 860.829 208.31" stroke-width="1" zvalue="2091237344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 899.312 208.31 L 860.829 208.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv35" d="M 1156.81 247.442 L 1156.81 288.329" stroke-width="2" zvalue="2091237393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.81 247.442 L 1156.81 288.329" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 1156.34 312.479 L 1156.26 339.303" stroke-width="2" zvalue="2091237394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.34 312.479 L 1156.26 339.303" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 1156.83 223.54 L 1156.83 177.223" stroke-width="2" zvalue="2091237395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.83 223.54 L 1156.83 177.223" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv35" d="M 1195.31 271.644 L 1156.81 271.644" stroke-width="2" zvalue="2091237397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.31 271.644 L 1156.81 271.644" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 1195.31 331.644 L 1156.28 331.633" stroke-width="2" zvalue="2091237401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.31 331.644 L 1156.28 331.633" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv35" d="M 1194.92 181.653 L 1156.83 181.653" stroke-width="2" zvalue="2091237403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.92 181.653 L 1156.83 181.653" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 1218.82 181.635 L 1244.87 181.635" stroke-width="2" zvalue="2091237406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.82 181.635 L 1244.87 181.635" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv35" d="M 1156.28 363.205 L 1156.28 380.31" stroke-width="2" zvalue="2091237408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="142@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.28 363.205 L 1156.28 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 1250.31 146.644 L 1228 146.644 L 1228 181.635" stroke-width="2" zvalue="2091237416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="208" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.31 146.644 L 1228 146.644 L 1228 181.635" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 1195.31 211.644 L 1156.83 211.644" stroke-width="1" zvalue="2091237419"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.31 211.644 L 1156.83 211.644" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="555">
   <path class="kv35" d="M 1273.06 413.095 L 1273.06 380.31" stroke-width="2" zvalue="2091237465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="846@0" LinkObjectIDznd="142@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.06 413.095 L 1273.06 380.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="556">
   <path class="kv35" d="M 1273.04 436.997 L 1273.04 514.845" stroke-width="2" zvalue="2091237466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="846@1" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.04 436.997 L 1273.04 514.845" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="557">
   <path class="kv35" d="M 1307.37 451.809 L 1273.04 451.809" stroke-width="2" zvalue="2091237467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="684@0" LinkObjectIDznd="556" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.37 451.809 L 1273.04 451.809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 925.337 674.413 L 925.337 662.94" stroke-width="2" zvalue="2091237469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.337 674.413 L 925.337 662.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 925.314 698.786 L 925.314 708.144" stroke-width="2" zvalue="2091237470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="149@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.314 698.786 L 925.314 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 598.034 906.058 L 598.034 708.144" stroke-width="2" zvalue="2091237475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 598.034 906.058 L 598.034 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 764.688 906.058 L 764.688 708.144" stroke-width="2" zvalue="2091237476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="149@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.688 906.058 L 764.688 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 928.015 906.058 L 928.015 708.144" stroke-width="1" zvalue="2091237477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="149@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.015 906.058 L 928.015 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 1091.34 911.058 L 1091.34 708.144" stroke-width="2" zvalue="2091237478"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="149@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.34 911.058 L 1091.34 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1256.86 906.058 L 1256.86 708.144" stroke-width="2" zvalue="2091237479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="149@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1256.86 906.058 L 1256.86 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1420.06 821.25 L 1420.06 708.144" stroke-width="2" zvalue="2091237480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="149@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1420.06 821.25 L 1420.06 708.144" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="121">
   <use class="kv35" height="30" transform="rotate(270,953.76,138.32) scale(-0.833333,0.817231) translate(-2098.27,30.9344)" width="12" x="953.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="138.3197631835936" zvalue="2091230786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785075388547076" ObjectName="03037"/>
   <cge:TPSR_Ref TObjectID="4785075388547076"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,953.76,138.32) scale(-0.833333,0.817231) translate(-2098.27,30.9344)" width="12" x="953.7597016598761" y="138.3197631835936"/></g>
  <g id="115">
   <use class="kv35" height="30" transform="rotate(270,898.76,263.32) scale(-0.833333,0.817231) translate(-1977.27,58.8898)" width="12" x="898.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="263.3197669310533" zvalue="2091230792"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785075374391300" ObjectName="30337"/>
   <cge:TPSR_Ref TObjectID="4785075374391300"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,898.76,263.32) scale(-0.833333,0.817231) translate(-1977.27,58.8898)" width="12" x="898.7597016598761" y="263.3197669310533"/></g>
  <g id="113">
   <use class="kv35" height="30" transform="rotate(270,898.76,323.32) scale(-0.833333,0.817231) translate(-1977.27,72.3085)" width="12" x="898.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="323.3197669310533" zvalue="2091230795"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077405548548" ObjectName="30317"/>
   <cge:TPSR_Ref TObjectID="4785077405548548"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,898.76,323.32) scale(-0.833333,0.817231) translate(-1977.27,72.3085)" width="12" x="898.7597016598761" y="323.3197669310533"/></g>
  <g id="112">
   <use class="kv35" height="30" transform="rotate(270,968.76,445.979) scale(-0.833333,0.817231) translate(-2131.27,99.7404)" width="12" x="968.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="445.9788164016688" zvalue="2091230797"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077405745156" ObjectName="30117"/>
   <cge:TPSR_Ref TObjectID="4785077405745156"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,968.76,445.979) scale(-0.833333,0.817231) translate(-2131.27,99.7404)" width="12" x="968.7597016598761" y="445.9788164016688"/></g>
  <g id="684">
   <use class="kv35" height="30" transform="rotate(270,1306.82,446.818) scale(-0.833333,0.817231) translate(-2875,99.9281)" width="12" x="1306.818181818182" xlink:href="#GroundDisconnector:地刀_0" y="446.8181818181818" zvalue="2091237317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078004809732" ObjectName="39118"/>
   <cge:TPSR_Ref TObjectID="4785078004809732"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1306.82,446.818) scale(-0.833333,0.817231) translate(-2875,99.9281)" width="12" x="1306.818181818182" y="446.8181818181818"/></g>
  <g id="322">
   <use class="kv35" height="30" transform="rotate(270,668.76,405.979) scale(-0.833333,0.817231) translate(-1471.27,90.7946)" width="12" x="668.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="405.9788164016688" zvalue="2091237328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078003105796" ObjectName="3117"/>
   <cge:TPSR_Ref TObjectID="4785078003105796"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,668.76,405.979) scale(-0.833333,0.817231) translate(-1471.27,90.7946)" width="12" x="668.7597016598761" y="405.9788164016688"/></g>
  <g id="342">
   <use class="kv35" height="30" transform="rotate(270,668.76,450.979) scale(-0.833333,0.817231) translate(-1471.27,100.859)" width="12" x="668.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="450.9788164016688" zvalue="2091237330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078003236868" ObjectName="03517"/>
   <cge:TPSR_Ref TObjectID="4785078003236868"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,668.76,450.979) scale(-0.833333,0.817231) translate(-1471.27,100.859)" width="12" x="668.7597016598761" y="450.9788164016688"/></g>
  <g id="466">
   <use class="kv35" height="30" transform="rotate(270,898.76,203.32) scale(-0.833333,0.817231) translate(-1977.27,45.4712)" width="12" x="898.7597016598761" xlink:href="#GroundDisconnector:地刀_0" y="203.3197669310533" zvalue="2091237341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078005006340" ObjectName="30338"/>
   <cge:TPSR_Ref TObjectID="4785078005006340"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,898.76,203.32) scale(-0.833333,0.817231) translate(-1977.27,45.4712)" width="12" x="898.7597016598761" y="203.3197669310533"/></g>
  <g id="219">
   <use class="kv35" height="30" transform="rotate(270,1249.76,141.653) scale(-0.833333,0.817231) translate(-2749.47,31.6798)" width="12" x="1249.759701659876" xlink:href="#GroundDisconnector:地刀_0" y="141.6530965015142" zvalue="2091237391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077008990211" ObjectName="03057"/>
   <cge:TPSR_Ref TObjectID="4785077008990211"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1249.76,141.653) scale(-0.833333,0.817231) translate(-2749.47,31.6798)" width="12" x="1249.759701659876" y="141.6530965015142"/></g>
  <g id="215">
   <use class="kv35" height="30" transform="rotate(270,1194.76,266.653) scale(-0.833333,0.817231) translate(-2628.47,59.6353)" width="12" x="1194.759701659876" xlink:href="#GroundDisconnector:地刀_0" y="266.6531002489739" zvalue="2091237396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077008793603" ObjectName="30537"/>
   <cge:TPSR_Ref TObjectID="4785077008793603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1194.76,266.653) scale(-0.833333,0.817231) translate(-2628.47,59.6353)" width="12" x="1194.759701659876" y="266.6531002489739"/></g>
  <g id="213">
   <use class="kv35" height="30" transform="rotate(270,1194.76,326.653) scale(-0.833333,0.817231) translate(-2628.47,73.0539)" width="12" x="1194.759701659876" xlink:href="#GroundDisconnector:地刀_0" y="326.6531002489739" zvalue="2091237399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009514499" ObjectName="30517"/>
   <cge:TPSR_Ref TObjectID="4785077009514499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1194.76,326.653) scale(-0.833333,0.817231) translate(-2628.47,73.0539)" width="12" x="1194.759701659876" y="326.6531002489739"/></g>
  <g id="200">
   <use class="kv35" height="30" transform="rotate(270,1194.76,206.653) scale(-0.833333,0.817231) translate(-2628.47,46.2167)" width="12" x="1194.759701659876" xlink:href="#GroundDisconnector:地刀_0" y="206.6531002489739" zvalue="2091237417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009121283" ObjectName="30538"/>
   <cge:TPSR_Ref TObjectID="4785077009121283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1194.76,206.653) scale(-0.833333,0.817231) translate(-2628.47,46.2167)" width="12" x="1194.759701659876" y="206.6531002489739"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,857.329,152) scale(1,0.733333) translate(0,55.2727)" width="7" x="857.3292048211979" xlink:href="#ACLineSegment:线路_0" y="152" zvalue="2091230829"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785078004285444" ObjectName="都巴红线大城支线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,857.329,152) scale(1,0.733333) translate(0,55.2727)" width="7" x="857.3292048211979" y="152"/></g>
  <g id="207">
   <use class="kv35" height="30" transform="rotate(0,1153.33,155.333) scale(1,0.733333) translate(0,56.4848)" width="7" x="1153.329204821198" xlink:href="#ACLineSegment:线路_0" y="155.3333333179206" zvalue="2091237409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="4785077009186819" ObjectName="琳大线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1153.33,155.333) scale(1,0.733333) translate(0,56.4848)" width="7" x="1153.329204821198" y="155.3333333179206"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="391">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="863.8625381545313" xml:space="preserve" y="44.47113145616325" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="P"/>
   </metadata>
  </g>
  <g id="392">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="863.8625381545313" xml:space="preserve" y="64.13778795030385" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="393">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="863.8625381545313" xml:space="preserve" y="83.80444444444447" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="394">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="1159.862538154531" xml:space="preserve" y="44.47113152022337" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="P"/>
   </metadata>
  </g>
  <g id="395">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="1159.862538154531" xml:space="preserve" y="64.13778795475935" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="396">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="1159.862538154531" xml:space="preserve" y="83.80444450850456" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="397">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="230.8044443277751" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="398">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="256.9825449137126" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="399">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="283.1606454996501" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="400">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="309.338776722375" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="401">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="335.5168771891032" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="402">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="582.0333333333334" xml:space="preserve" y="361.6949777750407" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uac"/>
   </metadata>
  </g>
  <g id="403">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="893.8706899007162" xml:space="preserve" y="470.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="404">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="893.8706899007162" xml:space="preserve" y="493.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="405">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="898.8706899007162" xml:space="preserve" y="623.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="406">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="898.8706899007162" xml:space="preserve" y="646.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="407">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="893.8706899007162" xml:space="preserve" y="516.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="408">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="1074.870689900716" xml:space="preserve" y="569.137776506212" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="409">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="1074.870689900716" xml:space="preserve" y="542.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="410">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="898.8706899007162" xml:space="preserve" y="669.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="411">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="560.8044443277749" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="412">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="586.982552543107" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="413">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="613.1606608776483" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="414">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="639.338768973771" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="415">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="665.5168771891031" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="416">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="466.0333333333334" xml:space="preserve" y="691.694985404435" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName="Uac"/>
   </metadata>
  </g>
  <g id="434">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="232.5333333333333" xml:space="preserve" y="261.8044444444445" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName=""/>
   </metadata>
  </g>
  <g id="435">
   <text Format="f6.2" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" stroke="rgb(255,255,0)" text-anchor="end" writing-mode="lr" x="232.5333333333333" xml:space="preserve" y="305.40445054796" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="null" ObjectName=""/>
   </metadata>
  </g>
 </g>
</svg>