<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="8444249346277378_5066549675753473" MapType="line" StationID="5066549675753473" height="1050" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1050" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(232,190,4);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.kv6{stroke:rgb(255,170,255);fill:none}
.v400{stroke:rgb(227,86,255);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器-线路_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.99087893864013" xlink:href="#terminal" y="0.7550975177305101"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="8.997927031509123" y1="10.28767730496455" y2="6.757092198581571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="0.9708554964538898" y2="3.22650709219857"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="21.18698175787728" y1="10.28767730496455" y2="6.757092198581571"/>
   <rect fill-opacity="0" height="18.83" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.04,12.64) scale(1,1) translate(0,0)" width="20.42" x="4.83" y="3.23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="3.226507092198597" y2="10.36613475177306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="14.99512411347518" y2="22.05629432624114"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.18626773539708" x2="19.32476506357104" y1="29.60782358156029" y2="29.60782358156029"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="8.997927031509123" y1="14.99512411347518" y2="18.52570921985816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.23940482771328" x2="20.62267366869358" y1="27.98189622993656" y2="27.98189622993656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.643587617468211" x2="23.21849087893864" y1="26.35596887831283" y2="26.35596887831283"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="21.18698175787728" y1="14.99512411347518" y2="18.52570921985816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.0924543946932" x2="15.0924543946932" y1="22.05629432624115" y2="26.29299645390072"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,17">
   <use terminal-index="0" type="0" x="7.05" xlink:href="#terminal" y="0.9666666666666641"/>
   <ellipse cx="7.15" cy="6.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="11.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV普和站" InitShowingPlane="" fill="rgb(0,0,0)" height="1050" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034,63.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1034" xml:space="preserve" y="75.875" zvalue="173">35kV普和站_35kV齐普线325</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312.699,697.13) scale(1,1) translate(0,0)" writing-mode="lr" x="312.7" xml:space="preserve" y="703.63" zvalue="1625">325</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.133,277.287) scale(1,1) translate(0,0)" writing-mode="lr" x="246.13" xml:space="preserve" y="284.79" zvalue="1628">35kV齐普线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,170.073,566.102) scale(1,1) translate(0,0)" writing-mode="lr" x="170.07" xml:space="preserve" y="572.6" zvalue="1630">32538</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,331.145,379.721) scale(1,1) translate(0,0)" writing-mode="lr" x="331.14" xml:space="preserve" y="384.22" zvalue="2721">0325</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,548.662,842.721) scale(1,1) translate(0,0)" writing-mode="lr" x="548.66" xml:space="preserve" y="847.22" zvalue="2727">35kV#1M</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="173">
   <path class="kv35" d="M 68.84 842.72 L 515.66 842.72" stroke-width="6" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674382184451" ObjectName="35kV#1M"/>
   <cge:TPSR_Ref TObjectID="9288674382184451"/></metadata>
  <path d="M 68.84 842.72 L 515.66 842.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="179">
   <use class="kv35" height="22" transform="rotate(0,247.685,777.479) scale(1.54545,-2.63636) translate(-81.4183,-1054.39)" width="22" x="230.6851933259977" xlink:href="#DollyBreaker:手车_0" y="748.4794977649622" zvalue="1623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699815938" ObjectName="35kV齐普线325手车2"/>
   <cge:TPSR_Ref TObjectID="6192452699815938"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,247.685,777.479) scale(1.54545,-2.63636) translate(-81.4183,-1054.39)" width="22" x="230.6851933259977" y="748.4794977649622"/></g>
  <g id="175">
   <use class="kv35" height="22" transform="rotate(0,246.112,625.11) scale(1.54545,2.63636) translate(-80.8631,-369.999)" width="22" x="229.1119800379416" xlink:href="#DollyBreaker:手车_0" y="596.1102350696501" zvalue="1626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699750402" ObjectName="35kV齐普线325手车"/>
   <cge:TPSR_Ref TObjectID="6192452699750402"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,246.112,625.11) scale(1.54545,2.63636) translate(-80.8631,-369.999)" width="22" x="229.1119800379416" y="596.1102350696501"/></g>
 </g>
 <g id="BreakerClass">
  <g id="177">
   <use class="kv35" height="20" transform="rotate(0,248.022,696.908) scale(-3.4,2.9) translate(-308.97,-437.595)" width="10" x="231.022412997499" xlink:href="#Breaker:开关_0" y="667.9080604742262" zvalue="1624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924986994690" ObjectName="35kV齐普线325"/>
   <cge:TPSR_Ref TObjectID="6473924986994690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,248.022,696.908) scale(-3.4,2.9) translate(-308.97,-437.595)" width="10" x="231.022412997499" y="667.9080604742262"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="174">
   <use class="kv35" height="30" transform="rotate(0,246.133,339.045) scale(3.47576,2.31717) translate(-166.654,-172.969)" width="7" x="233.9678486736916" xlink:href="#ACLineSegment:线路_0" y="304.2870316074557" zvalue="1627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249346277378" ObjectName="35kV齐普线"/>
   <cge:TPSR_Ref TObjectID="8444249346277378_5066549675753473"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,246.133,339.045) scale(3.47576,2.31717) translate(-166.654,-172.969)" width="7" x="233.9678486736916" y="304.2870316074557"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="12">
   <path class="kv35" d="M 247.77 724.93 L 247.77 776.71" stroke-width="1" zvalue="1628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 247.77 724.93 L 247.77 776.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv35" d="M 246.19 625.88 L 246.19 669.13" stroke-width="1" zvalue="1632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 246.19 625.88 L 246.19 669.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 246.13 598.77 L 246.13 373.45" stroke-width="1" zvalue="1633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="174@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 246.13 598.77 L 246.13 373.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 275.57 458.7 L 246.13 458.7" stroke-width="1" zvalue="1634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 275.57 458.7 L 246.13 458.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 247.71 803.82 L 247.71 842.72" stroke-width="1" zvalue="1635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 247.71 803.82 L 247.71 842.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 210.72 530.12 L 246.13 530.12" stroke-width="1" zvalue="1637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 210.72 530.12 L 246.13 530.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 183.71 677.18 L 183.71 660.49 L 246.19 660.49" stroke-width="1" zvalue="2595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 183.71 677.18 L 183.71 660.49 L 246.19 660.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 411.84 422.97 L 359.64 422.97" stroke-width="1" zvalue="2721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 411.84 422.97 L 359.64 422.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 308.64 421.55 L 246.13 421.55" stroke-width="1" zvalue="2722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 308.64 421.55 L 246.13 421.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="171">
   <use class="kv35" height="20" transform="rotate(90,177.57,529.88) scale(3.4,3.4) translate(-113.344,-350.033)" width="10" x="160.5701996914751" xlink:href="#GroundDisconnector:地刀_0" y="495.8800104602751" zvalue="1629"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699619330" ObjectName="35kV齐普线32538"/>
   <cge:TPSR_Ref TObjectID="6192452699619330"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,177.57,529.88) scale(3.4,3.4) translate(-113.344,-350.033)" width="10" x="160.5701996914751" y="495.8800104602751"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="170">
   <use class="kv35" height="30" transform="rotate(270,300.622,458.694) scale(0.733333,1.75833) translate(105.317,-186.45)" width="30" x="289.6220797846846" xlink:href="#Accessory:避雷器-线路_0" y="432.3187311634001" zvalue="1631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699488258" ObjectName="35kV齐普线避雷器"/>
   <cge:TPSR_Ref TObjectID="6192452699488258"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,300.622,458.694) scale(0.733333,1.75833) translate(105.317,-186.45)" width="30" x="289.6220797846846" y="432.3187311634001"/></g>
  <g id="166">
   <use class="kv35" height="17" transform="rotate(90,438.427,422.757) scale(4.28571,-3.52941) translate(-313.127,-521.039)" width="14" x="408.4266628121396" xlink:href="#Accessory:PT7_0" y="392.7573077237936" zvalue="1636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699422722" ObjectName="35kV齐普线325PT"/>
   <cge:TPSR_Ref TObjectID="6192452699422722"/></metadata>
  <rect fill="white" height="17" opacity="0" stroke="white" transform="rotate(90,438.427,422.757) scale(4.28571,-3.52941) translate(-313.127,-521.039)" width="14" x="408.4266628121396" y="392.7573077237936"/></g>
  <g id="161">
   <use class="kv35" height="30" transform="rotate(0,183.699,702.232) scale(-0.733333,1.75833) translate(-438.198,-291.483)" width="30" x="172.6990028616077" xlink:href="#Accessory:避雷器-线路_0" y="675.8571927018615" zvalue="1642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452699291650" ObjectName="35kV齐普线325避雷器"/>
   <cge:TPSR_Ref TObjectID="6192452699291650"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,183.699,702.232) scale(-0.733333,1.75833) translate(-438.198,-291.483)" width="30" x="172.6990028616077" y="675.8571927018615"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="68">
   <use class="kv35" height="30" transform="rotate(270,335.145,421.721) scale(2,2) translate(-160.072,-195.86)" width="15" x="320.1448070574119" xlink:href="#Disconnector:令克_0" y="391.7208918079314" zvalue="2720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004768771" ObjectName="35kV普和站0325令克"/>
   <cge:TPSR_Ref TObjectID="6192454004768771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,335.145,421.721) scale(2,2) translate(-160.072,-195.86)" width="15" x="320.1448070574119" y="391.7208918079314"/></g>
 </g>
</svg>