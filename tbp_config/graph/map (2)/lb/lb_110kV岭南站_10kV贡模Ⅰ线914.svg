<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="6192452170547202" MapType="line" StationID="5066549672345601" height="1050" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1050" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(232,190,4);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.kv6{stroke:rgb(255,170,255);fill:none}
.v400{stroke:rgb(227,86,255);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV岭南站" InitShowingPlane="" fill="rgb(0,0,0)" height="1050" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034,63.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1034" xml:space="preserve" y="75.875" zvalue="173">110kV岭南站_10kV贡模Ⅰ线914</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.497,337.635) scale(1,1) translate(0,0)" writing-mode="lr" x="326.5" xml:space="preserve" y="343.63" zvalue="2373">914</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.046,596.1) scale(1,1) translate(0,0)" writing-mode="lr" x="275.05" xml:space="preserve" y="602.1" zvalue="2376">贡模Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,329.002,509.367) scale(1,1) translate(0,0)" writing-mode="lr" x="329" xml:space="preserve" y="513.87" zvalue="2383">91438</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,450.694,212.9) scale(1,1) translate(0,0)" writing-mode="lr" x="450.69" xml:space="preserve" y="218.9" zvalue="3171">10kV#1M</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="179">
   <path class="kv10" d="M 182.81 212.9 L 401.69 212.9" stroke-width="5" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674369863683" ObjectName="10kV#1M"/>
   <cge:TPSR_Ref TObjectID="9288674369863683"/></metadata>
  <path d="M 182.81 212.9 L 401.69 212.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="814">
   <use class="kv10" height="20" transform="rotate(0,276.497,339.635) scale(3.4,2.9625) translate(-183.174,-205.365)" width="10" x="259.4965244988703" xlink:href="#Breaker:开关_0" y="310.00955294049" zvalue="2372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920934402" ObjectName="10kV贡模Ⅰ线914"/>
   <cge:TPSR_Ref TObjectID="6473924920934402"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,276.497,339.635) scale(3.4,2.9625) translate(-183.174,-205.365)" width="10" x="259.4965244988703" y="310.00955294049"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="813">
   <use class="kv10" height="22" transform="rotate(180,277.667,415.713) scale(1.54545,2.63636) translate(-92.0002,-240.029)" width="22" x="260.6673693685073" xlink:href="#DollyBreaker:手车_0" y="386.7132272568964" zvalue="2374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452170612738" ObjectName="10kV贡模Ⅰ线914手车2"/>
   <cge:TPSR_Ref TObjectID="6192452170612738"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,277.667,415.713) scale(1.54545,2.63636) translate(-92.0002,-240.029)" width="22" x="260.6673693685073" y="386.7132272568964"/></g>
  <g id="811">
   <use class="kv10" height="22" transform="rotate(0,276.704,273.731) scale(1.54545,2.63636) translate(-91.6602,-151.902)" width="22" x="259.703998544886" xlink:href="#DollyBreaker:手车_0" y="244.731476768615" zvalue="2377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452170481666" ObjectName="10kV贡模Ⅰ线914手车"/>
   <cge:TPSR_Ref TObjectID="6192452170481666"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,276.704,273.731) scale(1.54545,2.63636) translate(-91.6602,-151.902)" width="22" x="259.703998544886" y="244.731476768615"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="812">
   <use class="kv10" height="30" transform="rotate(180,277.646,530.418) scale(1.9,2.53333) translate(-126.117,-298.042)" width="12" x="266.2463439985283" xlink:href="#EnergyConsumer:负荷_0" y="492.417633147467" zvalue="2375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452170547202" ObjectName="10kV贡模Ⅰ线"/>
   <cge:TPSR_Ref TObjectID="6192452170547202"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,277.646,530.418) scale(1.9,2.53333) translate(-126.117,-298.042)" width="12" x="266.2463439985283" y="492.417633147467"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="2">
   <path class="kv10" d="M 276.92 311.25 L 276.92 274.5" stroke-width="2" zvalue="2378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="814@0" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 276.92 311.25 L 276.92 274.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 277.65 496.22 L 277.65 442.06" stroke-width="2" zvalue="2379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="812@0" LinkObjectIDznd="813@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 277.65 496.22 L 277.65 442.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 276.75 368.26 L 276.75 414.95" stroke-width="2" zvalue="2380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="814@1" LinkObjectIDznd="813@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 276.75 368.26 L 276.75 414.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 285.65 473.72 L 277.65 473.72" stroke-width="2" zvalue="2382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="807@0" LinkObjectIDznd="5" MaxPinNum="2"/>
   </metadata>
  <path d="M 285.65 473.72 L 277.65 473.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 276.73 247.39 L 276.73 212.9" stroke-width="2" zvalue="2384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 276.73 247.39 L 276.73 212.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="807">
   <use class="kv10" height="20" transform="rotate(270,318.8,473.959) scale(3.4,3.4) translate(-213.035,-310.56)" width="10" x="301.8002629417701" xlink:href="#GroundDisconnector:地刀_0" y="439.9593210068962" zvalue="2381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452170416130" ObjectName="10kV贡模Ⅰ线91438"/>
   <cge:TPSR_Ref TObjectID="6192452170416130"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,318.8,473.959) scale(3.4,3.4) translate(-213.035,-310.56)" width="10" x="301.8002629417701" y="439.9593210068962"/></g>
 </g>
</svg>