<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="6192451546841093" MapType="line" StationID="5066549670248449" height="1050" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1050" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(232,190,4);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.kv6{stroke:rgb(255,170,255);fill:none}
.v400{stroke:rgb(227,86,255);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV古良站" InitShowingPlane="" fill="rgb(0,0,0)" height="1050" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034,63.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1034" xml:space="preserve" y="75.875" zvalue="173">110kV古良站_10kV小平阳线918</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(179,336.597,408.522) scale(1,1) translate(0,0)" writing-mode="lr" x="336.6" xml:space="preserve" y="414.52" zvalue="2091323204">918</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(179,344.079,502.874) scale(1,1) translate(0,0)" writing-mode="lr" x="344.08" xml:space="preserve" y="507.37" zvalue="2091323206">91838</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.095,711.268) scale(1,1) translate(0,0)" writing-mode="lr" x="277.09" xml:space="preserve" y="717.77" zvalue="2091323213">小平阳线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,464.091,277.732) scale(1,1) translate(0,0)" writing-mode="lr" x="464.09" xml:space="preserve" y="283.73" zvalue="2091323745">10kV#1M</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="116">
   <path class="kv10" d="M 170.86 277.73 L 413.64 277.73" stroke-width="6" zvalue="2091321928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674359640067" ObjectName="10kV#1M"/>
   <cge:TPSR_Ref TObjectID="9288674359640067"/></metadata>
  <path d="M 170.86 277.73 L 413.64 277.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="269">
   <use class="kv10" height="20" transform="rotate(271,338.959,541.582) scale(-3.4,3.4) translate(-426.653,-358.293)" width="10" x="321.9589023379406" xlink:href="#GroundDisconnector:地刀_0" y="507.5820774574926" zvalue="2091323205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451547103237" ObjectName="10kV小平阳线91838"/>
   <cge:TPSR_Ref TObjectID="6192451547103237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(271,338.959,541.582) scale(-3.4,3.4) translate(-426.653,-358.293)" width="10" x="321.9589023379406" y="507.5820774574926"/></g>
 </g>
 <g id="BreakerClass">
  <g id="268">
   <use class="kv10" height="20" transform="rotate(179,279.264,405.803) scale(3.4,2.9) translate(-185.127,-246.871)" width="10" x="262.2636736801279" xlink:href="#Breaker:开关_0" y="376.8027850412327" zvalue="2091323207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924850548738" ObjectName="10kV小平阳线918"/>
   <cge:TPSR_Ref TObjectID="6473924850548738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(179,279.264,405.803) scale(3.4,2.9) translate(-185.127,-246.871)" width="10" x="262.2636736801279" y="376.8027850412327"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="267">
   <use class="kv10" height="22" transform="rotate(359,278.547,339.812) scale(1.54545,2.63636) translate(-92.3107,-192.918)" width="22" x="261.5470724238191" xlink:href="#DollyBreaker:手车_0" y="310.812123153998" zvalue="2091323208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452411457538" ObjectName="10kV小平阳线918手车"/>
   <cge:TPSR_Ref TObjectID="6192452411457538"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(359,278.547,339.812) scale(1.54545,2.63636) translate(-92.3107,-192.918)" width="22" x="261.5470724238191" y="310.812123153998"/></g>
  <g id="261">
   <use class="kv10" height="22" transform="rotate(179,280.28,479.794) scale(1.54545,2.63636) translate(-92.9224,-279.803)" width="22" x="263.2801108464025" xlink:href="#DollyBreaker:手车_0" y="450.7938745959536" zvalue="2091323209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452411392002" ObjectName="10kV小平阳线918手车2"/>
   <cge:TPSR_Ref TObjectID="6192452411392002"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(179,280.28,479.794) scale(1.54545,2.63636) translate(-92.9224,-279.803)" width="22" x="263.2801108464025" y="450.7938745959536"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="2">
   <path class="kv10" d="M 279.33 433.59 L 280.19 479.03" stroke-width="2" zvalue="2091323210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="261@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 279.33 433.59 L 280.19 479.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 278.52 377.79 L 278.64 340.58" stroke-width="2" zvalue="2091323211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 278.52 377.79 L 278.64 340.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 279.1 590.67 L 279.1 506.13" stroke-width="2" zvalue="2091323214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 279.1 590.67 L 279.1 506.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 305.81 541.25 L 279.1 541.25" stroke-width="1" zvalue="2091323215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 305.81 541.25 L 279.1 541.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 278.11 313.47 L 278.11 277.73" stroke-width="1" zvalue="2091323244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 278.11 313.47 L 278.11 277.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="253">
   <use class="kv10" height="30" transform="rotate(0,279.095,624.875) scale(1.83333,-2.53333) translate(-121.861,-848.536)" width="12" x="268.0950886809462" xlink:href="#EnergyConsumer:负荷_0" y="586.8748735189438" zvalue="2091323212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192451546841093" ObjectName="10kV小平阳线"/>
   <cge:TPSR_Ref TObjectID="6192451546841093"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,279.095,624.875) scale(1.83333,-2.53333) translate(-121.861,-848.536)" width="12" x="268.0950886809462" y="586.8748735189438"/></g>
 </g>
</svg>