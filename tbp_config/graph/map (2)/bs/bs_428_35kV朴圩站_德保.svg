<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549698756609" height="1800" id="thSvg" source="NR-PCS9000" viewBox="0 0 2300 1800" width="2300">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,0,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(255,170,199);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(227,227,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(85,170,0);fill:none}
.kv6{stroke:rgb(85,170,0);fill:none}
.v400{stroke:rgb(255,85,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4661605870522134" x2="7.700506079614452" y1="7.128423176033875" y2="24.03824349063279"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="12" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="2" x="12.08333333333333" xlink:href="#terminal" y="8"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="12" xlink:href="#terminal" y="29"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(0,170,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:前置通道状态_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:前置通道状态_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_2" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_3" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(80,80,80)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_4" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,0,255)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,13,15">
   <use terminal-index="0" type="0" x="6.320083175780933" xlink:href="#terminal" y="1.13911202992715"/>
   <path d="M 3.3 3.15 L 3.3 1.15 L 9.3 1.15 L 9.3 3.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 2.5 5.5 L 3.5 5.5 L 3.5 6 L 4.46667 6" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.5" x2="4.5" y1="4.75" y2="4.75"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(0,9.26,5.54) scale(1,1) translate(0,0)" width="2.22" x="8.15" y="3.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.266666666666666" x2="9.266666666666666" y1="3.333333333333334" y2="8.699999999999999"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(-90,3.33,6.74) scale(1,1) translate(0,0)" width="7.05" x="-0.19" y="5.22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.369763607738507" x2="3.369763607738507" y1="10.41666666666667" y2="12.94654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.802427517957054" x2="4.93709969751996" y1="13.03704961606615" y2="13.03704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.399066340209898" x2="4.488847793251836" y1="13.73364343374679" y2="13.73364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.853758628586885" x2="3.811575127897769" y1="14.25608879700729" y2="14.25608879700729"/>
   <ellipse cx="9.15" cy="10.28" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="8.15" cy="12.03" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="10.4" cy="12.03" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <path d="M 2.5 7 L 3.5 7 L 3.5 7.5 L 4.46667 7.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.5" x2="3.5" y1="8.283333333333333" y2="8.283333333333333"/>
  </symbol>
  <symbol id="State:正常供电_0" viewBox="0,0,31,14">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="12.83" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.5,6.67) scale(1,1) translate(0,0)" width="30.5" x="0.25" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.5" xml:space="preserve" y="9.166666666666668">   正常供电   </text>
  </symbol>
  <symbol id="State:正常供电_1" viewBox="0,0,31,14">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="13.33" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.42,7) scale(1,1) translate(0,0)" width="30.5" x="0.17" y="0.33"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.41666666666667" xml:space="preserve" y="9.5"> 全站保供电 </text>
  </symbol>
  <symbol id="State:全站正常运行_0" viewBox="0,0,30,10">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警启用</text>
  </symbol>
  <symbol id="State:全站正常运行_1" viewBox="0,0,30,10">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警退出</text>
  </symbol>
  <symbol id="Compensator:并联电容器2_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9.033256528417819" xlink:href="#terminal" y="3.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.09999999999999076" x2="17.93410138248849" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.11589861751151" x2="17.95000000000001" y1="14.11666666666667" y2="14.11666666666667"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.686947459912016" y2="21.52315435646375"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.000000000000001" x2="1.078947368421052" y1="1.607730263157897" y2="6.541118421052633"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.92105263157895" x2="7.037006578947368" y1="6.467105263157893" y2="1.607730263157894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.078947368421053" x2="6.962993421052632" y1="19.44588815789474" y2="24.37023026315789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.037006578947368" x2="12.95805921052632" y1="24.37023026315789" y2="19.48289473684211"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7" x2="7" y1="1.666666666666664" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <ellipse cx="6.99" cy="3.03" fill-opacity="0" rx="2.9" ry="2.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.01" cy="23.12" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.99506578947368" x2="1.078947368421053" y1="6.042763157894737" y2="22.0296052631579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9679276315789469" x2="12.95805921052632" y1="6.00575657894737" y2="21.95559210526316"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="9.08" cy="8.92" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.02" cy="21.08" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":bs电缆_0" viewBox="0,0,30,30">
   <path d="M 10.9166 4.25 L 19.25 4.25 L 15.0833 8.09722 L 10.9166 4.25 z" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08327650869417" x2="15.08327650869417" y1="8.097222222222227" y2="23.48611111111108"/>
   <path d="M 10.8333 27.3333 L 19.1668 27.3333 L 15.0001 23.4861 L 10.8333 27.3333" fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:接地电阻器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="15" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="22" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.5" x2="10.5" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="23" y2="23"/>
   <ellipse cx="10.08" cy="18.75" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="11" y2="11"/>
   <rect fill-opacity="0" height="12" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9) scale(1,1) translate(0,0)" width="4" x="8" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="6" y2="6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV朴圩站" InitShowingPlane="5" fill="rgb(0,0,0)" height="1800" width="2300" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
  
  
  
  
  
  
  
  
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.94,241.742) scale(1,1) translate(0,0)" writing-mode="lr" x="194.94" xml:space="preserve" y="246.24" zvalue="1204"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,232.09,242.393) scale(1,1) translate(0,0)" writing-mode="lr" x="232.09" xml:space="preserve" y="251.39" zvalue="1205">35kV朴圩站(德保)</text>
  <rect fill="none" fill-opacity="0" height="1373.75" id="380" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,196.044,896.875) scale(1,1) translate(-1.69284e-14,0)" width="315.85" x="38.12" y="210" zvalue="1207"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="39" id="377" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,122.343,1022.66) scale(1,1) translate(-1.24316e-14,2.22746e-13)" width="132.71" x="55.99" y="1003.16" zvalue="1208"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,122.343,1022.66) scale(1,1) translate(0,0)" writing-mode="lr" x="122.34" xml:space="preserve" y="1032.16" zvalue="1208">网络状态</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="39" id="367" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,122.343,971.276) scale(1,1) translate(-1.24316e-14,-2.11336e-13)" width="132.71" x="55.99" y="951.77" zvalue="1215"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,122.343,971.276) scale(1,1) translate(0,0)" writing-mode="lr" x="122.34" xml:space="preserve" y="980.78" zvalue="1215">公用信号</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="39" id="277" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,274.343,971.276) scale(1,1) translate(1.38547e-13,-2.11336e-13)" width="132.71" x="207.99" y="951.77" zvalue="1250"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,274.343,971.276) scale(1,1) translate(0,0)" writing-mode="lr" x="274.34" xml:space="preserve" y="980.78" zvalue="1250">间隔索引</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="353.449952602518" y1="1447.796655158416" y2="1447.796655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="353.449952602518" y1="1485.513055158416" y2="1485.513055158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="36.24895260251787" y1="1447.796655158416" y2="1485.513055158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.449952602518" x2="353.449952602518" y1="1447.796655158416" y2="1485.513055158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="92.16585260251804" y1="1485.512855158416" y2="1485.512855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="92.16585260251804" y1="1532.492655158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="36.24895260251787" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16585260251804" x2="92.16585260251804" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="148.0838526025179" y1="1485.512855158416" y2="1485.512855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="148.0838526025179" y1="1532.492655158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="92.16695260251799" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0838526025179" x2="148.0838526025179" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="211.930252602518" y1="1485.512855158416" y2="1485.512855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="211.930252602518" y1="1532.492655158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="148.0833526025179" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930252602518" x2="211.930252602518" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="275.777252602518" y1="1485.512855158416" y2="1485.512855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="275.777252602518" y1="1532.492655158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="211.930352602518" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777252602518" x2="275.777252602518" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="353.451052602518" y1="1485.512855158416" y2="1485.512855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="353.451052602518" y1="1532.492655158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="275.777352602518" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.451052602518" x2="353.451052602518" y1="1485.512855158416" y2="1532.492655158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="92.16585260251804" y1="1532.492755158416" y2="1532.492755158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="92.16585260251804" y1="1583.335855158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="36.24895260251787" x2="36.24895260251787" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16585260251804" x2="92.16585260251804" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="148.0838526025179" y1="1532.492755158416" y2="1532.492755158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="148.0838526025179" y1="1583.335855158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.16695260251799" x2="92.16695260251799" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0838526025179" x2="148.0838526025179" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="211.930252602518" y1="1532.492755158416" y2="1532.492755158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="211.930252602518" y1="1583.335855158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="148.0833526025179" x2="148.0833526025179" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930252602518" x2="211.930252602518" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="275.777252602518" y1="1532.492755158416" y2="1532.492755158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="275.777252602518" y1="1583.335855158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="211.930352602518" x2="211.930352602518" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777252602518" x2="275.777252602518" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="353.451052602518" y1="1532.492755158416" y2="1532.492755158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="353.451052602518" y1="1583.335855158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.777352602518" x2="275.777352602518" y1="1532.492755158416" y2="1583.335855158416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.451052602518" x2="353.451052602518" y1="1532.492755158416" y2="1583.335855158416"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.1654,1507.53) scale(1,1) translate(0,0)" writing-mode="lr" x="66.17" xml:space="preserve" y="1513.53" zvalue="1252">直采１</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,122.85,1507.53) scale(1,1) translate(0,0)" writing-mode="lr" x="122.85" xml:space="preserve" y="1513.53" zvalue="1253">直采２</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,316.064,1509.74) scale(1,1) translate(0,0)" writing-mode="lr" x="316.06" xml:space="preserve" y="1515.24" zvalue="1254">值班节点</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="39" id="162" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,274.343,1022.66) scale(1,1) translate(1.38547e-13,2.22746e-13)" width="132.71" x="207.99" y="1003.16" zvalue="1804"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,274.343,1022.66) scale(1,1) translate(0,0)" writing-mode="lr" x="274.34" xml:space="preserve" y="1032.16" zvalue="1804">状态监视</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1965.63,548.78) scale(1,1) translate(0,0)" writing-mode="lr" x="1965.63" xml:space="preserve" y="556.28" zvalue="2">35kV1号母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="32" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.95,800) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.95" xml:space="preserve" y="811.5" zvalue="145">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.38,838.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.375" xml:space="preserve" y="845.1428571428571" zvalue="157">SZ11-2000/35</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1969.8,1112.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1969.8" xml:space="preserve" y="1119.03" zvalue="160">10kV1号母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="495" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.481,292.702) scale(1,1) translate(0,0)" writing-mode="lr" x="192.48" xml:space="preserve" y="299.2" zvalue="566">图纸版本：朴圩站2023-1</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="356.6266769119769" y2="356.6266769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="385.1168769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="356.6266769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="356.6266769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="356.6266769119769" y2="356.6266769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="385.1168769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="356.6266769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="356.6266769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="385.1168769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="413.6070769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="385.1168769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="385.1168769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="385.1168769119769" y2="385.1168769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="413.6070769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="385.1168769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="385.1168769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="413.6070769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="442.0972769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="413.6070769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="413.6070769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="413.6070769119769" y2="413.6070769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="442.0972769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="413.6070769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="413.6070769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="442.0972769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="470.5874769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="442.0972769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="442.0972769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="442.0972769119769" y2="442.0972769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="470.5874769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="442.0972769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="442.0972769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="470.5874769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="499.0776769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="470.5874769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="470.5874769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="470.5874769119769" y2="470.5874769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="499.0776769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="470.5874769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="470.5874769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="499.0776769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="527.5678769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="499.0776769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="499.0776769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="499.0776769119769" y2="499.0776769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="527.5678769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="499.0776769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="499.0776769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="527.5678769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="691.75" y1="556.0580769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="618.0313" x2="618.0313" y1="527.5678769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="527.5678769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="527.5678769119769" y2="527.5678769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="765.4687" y1="556.0580769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="691.75" x2="691.75" y1="527.5678769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="765.4687" x2="765.4687" y1="527.5678769119769" y2="556.0580769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="916.6286769119769" y2="916.6286769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="943.0468769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="916.6286769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="916.6286769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="916.6286769119769" y2="916.6286769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="943.0468769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="916.6286769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="916.6286769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="943.0468769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="969.4650769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="943.0468769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="943.0468769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="943.0468769119769" y2="943.0468769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="969.4650769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="943.0468769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="943.0468769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="969.4650769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="995.883276911977" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="969.4650769119769" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="969.4650769119769" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="969.4650769119769" y2="969.4650769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="995.883276911977" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="969.4650769119769" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="969.4650769119769" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="995.883276911977" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1022.301476911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="995.883276911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="995.883276911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="995.883276911977" y2="995.883276911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1022.301476911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="995.883276911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="995.883276911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1022.301476911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1048.719676911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="1022.301476911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1022.301476911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1022.301476911977" y2="1022.301476911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1048.719676911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1022.301476911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="1022.301476911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1048.719676911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1075.137876911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="1048.719676911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1048.719676911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1048.719676911977" y2="1048.719676911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1075.137876911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1048.719676911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="1048.719676911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1075.137876911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1904.590909090909" y1="1101.556076911977" y2="1101.556076911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1827.643409090909" x2="1827.643409090909" y1="1075.137876911977" y2="1101.556076911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1075.137876911977" y2="1101.556076911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1075.137876911977" y2="1075.137876911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1981.538409090909" y1="1101.556076911977" y2="1101.556076911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1904.590909090909" x2="1904.590909090909" y1="1075.137876911977" y2="1101.556076911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1981.538409090909" x2="1981.538409090909" y1="1075.137876911977" y2="1101.556076911977"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.87,800.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.87" xml:space="preserve" y="808" zvalue="696">1号母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,649.286,889.5) scale(1,1) translate(0,0)" writing-mode="lr" x="649.29" xml:space="preserve" y="897" zvalue="812">1号母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="444" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.667,827.667) scale(1,1) translate(0,0)" writing-mode="lr" x="745.67" xml:space="preserve" y="834.17" zvalue="845">油温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="443" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.667,799.667) scale(1,1) translate(0,0)" writing-mode="lr" x="744.67" xml:space="preserve" y="806.17" zvalue="846">绕温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="445" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.667,852.667) scale(1,1) translate(0,0)" writing-mode="lr" x="747.67" xml:space="preserve" y="859.17" zvalue="847">档位：</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="195.9824710860149" y1="740.6654845341769" y2="740.6654845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="195.9824710860149" y1="777.3748845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="57.49747108601491" y1="740.6654845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="195.9824710860149" y1="740.6654845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="334.4674710860149" y1="740.6654845341769" y2="740.6654845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="334.4674710860149" y1="777.3748845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="195.9824710860149" y1="740.6654845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="334.4674710860149" x2="334.4674710860149" y1="740.6654845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="195.9824710860149" y1="777.3748845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="195.9824710860149" y1="814.084284534177" y2="814.084284534177"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="57.49747108601491" x2="57.49747108601491" y1="777.3748845341769" y2="814.084284534177"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="195.9824710860149" y1="777.3748845341769" y2="814.084284534177"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="334.4674710860149" y1="777.3748845341769" y2="777.3748845341769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="334.4674710860149" y1="814.084284534177" y2="814.084284534177"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.9824710860149" x2="195.9824710860149" y1="777.3748845341769" y2="814.084284534177"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="334.4674710860149" x2="334.4674710860149" y1="777.3748845341769" y2="814.084284534177"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.007,761.138) scale(1,1) translate(0,0)" writing-mode="lr" x="128.01" xml:space="preserve" y="767.64" zvalue="1209">主变有功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="374" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.007,794.779) scale(1,1) translate(0,0)" writing-mode="lr" x="128.01" xml:space="preserve" y="801.28" zvalue="1210">主变无功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,164.837,411.467) scale(1,1) translate(0,0)" writing-mode="lr" x="164.84" xml:space="preserve" y="420.97" zvalue="1211">全站事故总信号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.896,1104.53) scale(1,1) translate(0,0)" writing-mode="lr" x="192.9" xml:space="preserve" y="1114.03" zvalue="1216">AVC状态</text>
  <line fill="none" id="354" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.8835584356973" x2="352.583382654291" y1="913.4897552159455" y2="913.4897552159455" zvalue="1218"/>
  <rect fill="none" fill-opacity="0" height="38.98" id="353" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,190.404,1106.95) scale(1,1) translate(-4.75312e-14,-7.24394e-13)" width="166.75" x="107.03" y="1087.46" zvalue="1219"/>
  <line fill="none" id="352" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="49.83443461030356" x2="99.49043693636258" y1="1224.945988755569" y2="1224.945988755569" zvalue="1221"/>
  <line fill="none" id="351" stroke="rgb(255,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="49.83443461030356" x2="99.49043693636258" y1="1246.971671653431" y2="1246.971671653431" zvalue="1222"/>
  <line fill="none" id="349" stroke="rgb(232,155,232)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="49.83443461030356" x2="99.49043693636258" y1="1268.997354551291" y2="1268.997354551291" zvalue="1223"/>
  <line fill="none" id="348" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="49.83443461030356" x2="99.49043693636258" y1="1291.023037449155" y2="1291.023037449155" zvalue="1224"/>
  <line fill="none" id="347" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="49.83443461030356" x2="99.49043693636258" y1="1313.048720347017" y2="1313.048720347017" zvalue="1225"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.945,1224.13) scale(1,1) translate(0,0)" writing-mode="lr" x="137.95" xml:space="preserve" y="1230.13" zvalue="1226">500kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.945,1246.16) scale(1,1) translate(0,0)" writing-mode="lr" x="137.95" xml:space="preserve" y="1252.16" zvalue="1227">220kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.228,1268.18) scale(1,1) translate(0,0)" writing-mode="lr" x="138.23" xml:space="preserve" y="1274.18" zvalue="1228">110kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,134.557,1290.21) scale(1,1) translate(0,0)" writing-mode="lr" x="134.56" xml:space="preserve" y="1296.21" zvalue="1229">35kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,133.992,1312.23) scale(1,1) translate(0,0)" writing-mode="lr" x="133.99" xml:space="preserve" y="1318.23" zvalue="1230">10kV</text>
  <rect fill="rgb(255,85,128)" fill-opacity="1" height="10.6" id="336" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,224.734,1225.35) scale(1,1) translate(2.25615e-13,5.95993e-12)" width="43.04" x="203.22" y="1220.05" zvalue="1231"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.175,1245.75) scale(1,1) translate(0,0)" writing-mode="lr" x="295.17" xml:space="preserve" y="1251.75" zvalue="1232">人工变位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="313" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.327,1266.55) scale(1,1) translate(0,0)" writing-mode="lr" x="294.33" xml:space="preserve" y="1272.55" zvalue="1233">停电状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="312" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.598,1287.35) scale(1,1) translate(0,0)" writing-mode="lr" x="295.6" xml:space="preserve" y="1293.35" zvalue="1234">接地状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.751,1308.15) scale(1,1) translate(0,0)" writing-mode="lr" x="294.75" xml:space="preserve" y="1314.15" zvalue="1235">检修状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,140.161,1363.08) scale(1,1) translate(0,0)" writing-mode="lr" x="140.16" xml:space="preserve" y="1369.08" zvalue="1236">数据封锁</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.89,1383.75) scale(1,1) translate(0,0)" writing-mode="lr" x="138.89" xml:space="preserve" y="1389.75" zvalue="1237">线路对端</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.209,1404.41) scale(1,1) translate(0,0)" writing-mode="lr" x="148.21" xml:space="preserve" y="1410.41" zvalue="1238">数据不更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.314,1342.42) scale(1,1) translate(0,0)" writing-mode="lr" x="139.31" xml:space="preserve" y="1348.42" zvalue="1239">被旁路带</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.598,1224.95) scale(1,1) translate(0,0)" writing-mode="lr" x="295.6" xml:space="preserve" y="1230.95" zvalue="1240">事故变位</text>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.6" id="290" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,224.734,1248.2) scale(1,1) translate(2.25615e-13,6.07151e-12)" width="43.04" x="203.22" y="1242.89" zvalue="1241"/>
  <rect fill="rgb(0,0,255)" fill-opacity="1" height="10.6" id="289" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,224.734,1266.96) scale(1,1) translate(2.25615e-13,6.16316e-12)" width="43.04" x="203.22" y="1261.66" zvalue="1242"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.6" id="288" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,224.734,1287.35) scale(1,1) translate(2.25615e-13,6.26279e-12)" width="43.04" x="203.22" y="1282.05" zvalue="1243"/>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.6" id="287" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,224.734,1307.75) scale(1,1) translate(2.25615e-13,6.36241e-12)" width="43.04" x="203.22" y="1302.44" zvalue="1244"/>
  <rect fill="rgb(84,250,247)" fill-opacity="1" height="10.6" id="286" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,70.2486,1360.77) scale(1,1) translate(5.41022e-14,6.62144e-12)" width="43.04" x="48.73" y="1355.47" zvalue="1245"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.6" id="285" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,70.2486,1403.19) scale(1,1) translate(5.41022e-14,6.82866e-12)" width="43.04" x="48.73" y="1397.89" zvalue="1246"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.6" id="284" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,70.2486,1381.98) scale(1,1) translate(5.41022e-14,6.72505e-12)" width="43.04" x="48.73" y="1376.68" zvalue="1247"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.6" id="278" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,70.2486,1342.82) scale(1,1) translate(5.41022e-14,6.53377e-12)" width="43.04" x="48.73" y="1337.52" zvalue="1248"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.575,1469.83) scale(1,1) translate(0,0)" writing-mode="lr" x="202.57" xml:space="preserve" y="1475.83" zvalue="1255">远动通道工况</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.61,1516.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.61" xml:space="preserve" y="1526.67" zvalue="1488">朴圩线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.38,428) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.38" xml:space="preserve" y="437.5" zvalue="1579">311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.35,130.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.35" xml:space="preserve" y="141.33" zvalue="1586">东朴线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1429.33,295.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1429.33" xml:space="preserve" y="301.83" zvalue="1589">31168</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.58,663.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.58" xml:space="preserve" y="673.33" zvalue="1594">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.17,1018.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.17" xml:space="preserve" y="1028.33" zvalue="1605">901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.218,1244.5) scale(1,1) translate(0,0)" writing-mode="lr" x="781.22" xml:space="preserve" y="1254" zvalue="1615">913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.777,1062) scale(1,1) translate(0,0)" writing-mode="lr" x="685.78" xml:space="preserve" y="1068.5" zvalue="1620">0951</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.333,1321.33) scale(1,1) translate(0,0)" writing-mode="lr" x="798.33" xml:space="preserve" y="1327.83" zvalue="1624">91368</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.884,1441.67) scale(1,1) translate(0,0)" writing-mode="lr" x="673.88" xml:space="preserve" y="1449.17" zvalue="1626">9139</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.333,1480.67) scale(1,1) translate(0,0)" writing-mode="lr" x="798.33" xml:space="preserve" y="1487.17" zvalue="1628">91398</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.014,1614.56) scale(1,1) translate(0,0)" writing-mode="lr" x="720.01" xml:space="preserve" y="1625.06" zvalue="1635">1号电容</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.928,1371.62) scale(1,1) translate(0,0)" writing-mode="lr" x="585.9299999999999" xml:space="preserve" y="1380.63" zvalue="1644">3号站变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,633.825,1197.75) scale(1,1) translate(0,0)" writing-mode="lr" x="633.83" xml:space="preserve" y="1205.25" zvalue="1646">9121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.58,1244.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.58" xml:space="preserve" y="1254" zvalue="1650">914</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1166.83,1335.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1166.83" xml:space="preserve" y="1342.25" zvalue="1656">91468</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.5,1518.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.5" xml:space="preserve" y="1529.11" zvalue="1662">多脉线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.25,1246.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.25" xml:space="preserve" y="1256" zvalue="1664">915</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.5,1337.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.5" xml:space="preserve" y="1344.25" zvalue="1669">91568</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.61,1510.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.61" xml:space="preserve" y="1521.11" zvalue="1675">双汉线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.36,1245.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.36" xml:space="preserve" y="1255.11" zvalue="1677">916</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.61,1336.86) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.61" xml:space="preserve" y="1343.36" zvalue="1682">91668</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1210,635.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1210" xml:space="preserve" y="643" zvalue="1688">0351</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="19" id="12" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,197.5,827.564) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="834.0599999999999" zvalue="1704">注：显示为叉号的开关刀闸</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="19" id="20" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,197.5,845.129) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="851.63" zvalue="1705">表示遥信点现场无法上送</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.5,862.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.5" xml:space="preserve" y="869" zvalue="1709">35±3x2.5%/10.5kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.79,885.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.785714285714" xml:space="preserve" y="891.8571428571429" zvalue="1711">Ud=7.1%,Yd11</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="32" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.01,801.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.01" xml:space="preserve" y="812.79" zvalue="1714">2号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.5,667.69) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.5" xml:space="preserve" y="677.1900000000001" zvalue="1716">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1614.08,1038.69) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.08" xml:space="preserve" y="1048.19" zvalue="1724">902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.714,1643.36) scale(1,1) translate(0,0)" writing-mode="lr" x="726.7142857142858" xml:space="preserve" y="1649.857142857143" zvalue="1733">0.6Mvar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1841.83,1510.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1841.83" xml:space="preserve" y="1521.17" zvalue="1738">平交线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1890.58,1245.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1890.58" xml:space="preserve" y="1255.17" zvalue="1740">918</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1913.83,1336.92) scale(1,1) translate(0,0)" writing-mode="lr" x="1913.83" xml:space="preserve" y="1343.42" zvalue="1746">91868</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1680.82,841.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1680.819444444444" xml:space="preserve" y="847.8888888888889" zvalue="1751">SZ11-5000/35</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698.94,865.246) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.944444444444" xml:space="preserve" y="871.7460317460317" zvalue="1752">35±3x2.5%/10.5kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1685.23,888.103) scale(1,1) translate(0,0)" writing-mode="lr" x="1685.230158730159" xml:space="preserve" y="894.6031746031746" zvalue="1753">Ud=6.85%,Yd11</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="29" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.944,1512.67) scale(1,1) translate(0,0)" writing-mode="lr" x="917.9400000000001" xml:space="preserve" y="1523.17" zvalue="1757">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.696,1247.67) scale(1,1) translate(0,0)" writing-mode="lr" x="966.7" xml:space="preserve" y="1257.17" zvalue="1759">911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.946,1338.92) scale(1,1) translate(0,0)" writing-mode="lr" x="989.95" xml:space="preserve" y="1345.42" zvalue="1765">91168</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="27" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.25,1245.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.25" xml:space="preserve" y="1254.89" zvalue="1773">917</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.5,1336.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.5" xml:space="preserve" y="1343.14" zvalue="1779">91768</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645,1556.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1645" xml:space="preserve" y="1562.72" zvalue="1785">1号接地电阻器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1377.75,831.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1377.75" xml:space="preserve" y="838.25" zvalue="1790">油温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.75,803.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.75" xml:space="preserve" y="810.25" zvalue="1791">绕温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.75,856.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.75" xml:space="preserve" y="863.25" zvalue="1792">档位：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,370.81) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="376.81" zvalue="3066">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,399.465) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="405.46" zvalue="3067">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,428.12) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="434.12" zvalue="3068">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,542.74) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="548.74" zvalue="3070">U0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,456.775) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="462.77" zvalue="3071">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,485.43) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="491.43" zvalue="3072">Ubc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.255,514.085) scale(1,1) translate(0,0)" writing-mode="lr" x="653.25" xml:space="preserve" y="520.08" zvalue="3073">Uca</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,929.78) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="935.78" zvalue="3083">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,956.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="962.35" zvalue="3084">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,982.922) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="988.92" zvalue="3085">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,1089.21) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="1095.21" zvalue="3087">U0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,1009.49) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="1015.49" zvalue="3088">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,1036.06) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="1042.06" zvalue="3089">Ubc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1864.41,1062.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.41" xml:space="preserve" y="1068.64" zvalue="3090">Uca</text>
 </g>
 <g id="ButtonClass">
  <g href="bs_35kV朴圩站_网络状态.svg"><rect fill-opacity="0" height="39" width="132.71" x="55.99" y="1003.16" zvalue="1208"/></g>
  <g href="bs_35kV朴圩站_公用信号.svg"><rect fill-opacity="0" height="39" width="132.71" x="55.99" y="951.77" zvalue="1215"/></g>
  <g href="bs_35kV朴圩站_间隔索引.svg"><rect fill-opacity="0" height="39" width="132.71" x="207.99" y="951.77" zvalue="1250"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="27.68" width="89.33" x="21.5" y="1493.68" zvalue="1252"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="27.68" width="93.37" x="76.16" y="1493.68" zvalue="1253"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="27.68" width="93.37" x="269.38" y="1495.9" zvalue="1254"/></g>
  <g href="bs_35kV朴圩站_状态监视.svg"><rect fill-opacity="0" height="39" width="132.71" x="207.99" y="1003.16" zvalue="1804"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv35" d="M 556.09 569 L 2010.38 569" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674487107587" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674487107587"/></metadata>
  <path d="M 556.09 569 L 2010.38 569" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 543.25 1128 L 2017 1128" stroke-width="6" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674487173123" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674487173123"/></metadata>
  <path d="M 543.25 1128 L 2017 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="123">
   <g id="1230">
    <use class="kv35" height="30" transform="rotate(0,983.133,824.504) scale(4.27778,4.29972) translate(-713.976,-583.251)" width="24" x="931.8" xlink:href="#PowerTransformer2:可调两卷变_0" y="760.01" zvalue="144"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874632069122" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1231">
    <use class="kv10" height="30" transform="rotate(0,983.133,824.504) scale(4.27778,4.29972) translate(-713.976,-583.251)" width="24" x="931.8" xlink:href="#PowerTransformer2:可调两卷变_1" y="760.01" zvalue="144"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874632134658" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399558692866" ObjectName="35kV#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399558692866"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,983.133,824.504) scale(4.27778,4.29972) translate(-713.976,-583.251)" width="24" x="931.8" y="760.01"/></g>
  <g id="45">
   <g id="450">
    <use class="kv35" height="30" transform="rotate(0,1557.2,825.79) scale(4.27778,4.29972) translate(-1153.84,-584.237)" width="24" x="1505.86" xlink:href="#PowerTransformer2:可调两卷变_0" y="761.29" zvalue="1713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874659266562" ObjectName="35"/>
    </metadata>
   </g>
   <g id="451">
    <use class="kv10" height="30" transform="rotate(0,1557.2,825.79) scale(4.27778,4.29972) translate(-1153.84,-584.237)" width="24" x="1505.86" xlink:href="#PowerTransformer2:可调两卷变_1" y="761.29" zvalue="1713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874659332098" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399570489346" ObjectName="35kV#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399570489346"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1557.2,825.79) scale(4.27778,4.29972) translate(-1153.84,-584.237)" width="24" x="1505.86" y="761.29"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="125">
   <use class="kv35" height="15" transform="rotate(0,1253.22,735.974) scale(6.60205,5.12984) translate(-1026.98,-561.531)" width="13" x="1210.305702190351" xlink:href="#Accessory:PT8_0" y="697.5" zvalue="695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978057730" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1253.22,735.974) scale(6.60205,5.12984) translate(-1026.98,-561.531)" width="13" x="1210.305702190351" y="697.5"/></g>
  <g id="359">
   <use class="kv10" height="15" transform="rotate(180,646.603,956.749) scale(6.43347,6.43347) translate(-510.779,-767.284)" width="13" x="604.7855132507977" xlink:href="#Accessory:PT8_0" y="908.4979947167109" zvalue="811"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455977992194" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(180,646.603,956.749) scale(6.43347,6.43347) translate(-510.779,-767.284)" width="13" x="604.7855132507977" y="908.4979947167109"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,1642.78,1504.72) scale(3.27778,3.27778) translate(-1118.81,-1011.49)" width="20" x="1610" xlink:href="#Accessory:接地电阻器_0" y="1455.555555555556" zvalue="1784"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456908013569" ObjectName="10kV1号接地电阻器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1642.78,1504.72) scale(3.27778,3.27778) translate(-1118.81,-1011.49)" width="20" x="1610" y="1455.555555555556"/></g>
 </g>
 <g id="StateClass">
  <g id="550">
   <use height="30" transform="rotate(0,298.096,414.475) scale(1.3913,1.01971) translate(-77.9689,-7.71481)" width="30" x="277.23" xlink:href="#State:红绿圆_0" y="399.18" zvalue="1212"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,298.096,414.475) scale(1.3913,1.01971) translate(-77.9689,-7.71481)" width="30" x="277.23" y="399.18"/></g>
  <g id="533">
   <use height="10" transform="rotate(0,193.94,478.072) scale(6.80471,3.75252) translate(-78.3683,-336.909)" width="30" x="91.87" xlink:href="#State:全站正常运行_0" y="459.31" zvalue="1220"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549698756609" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,193.94,478.072) scale(6.80471,3.75252) translate(-78.3683,-336.909)" width="30" x="91.87" y="459.31"/></g>
  <g id="540">
   <use height="14" transform="rotate(0,191.457,533.544) scale(6.42502,2.56384) translate(-77.5705,-314.493)" width="31" x="91.87" xlink:href="#State:正常供电_0" y="515.6" zvalue="1249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762603614011395" ObjectName=""/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,191.457,533.544) scale(6.42502,2.56384) translate(-77.5705,-314.493)" width="31" x="91.87" y="515.6"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="EnergyConsumerClass">
  <g id="64">
   <use class="kv10" height="30" transform="rotate(180,1092.58,1453.61) scale(0.916667,1.75926) translate(98.8256,-615.959)" width="12" x="1087.081745774372" xlink:href="#EnergyConsumer:负荷_0" y="1427.222222222222" zvalue="1487"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978123266" ObjectName="10kV朴圩线"/>
   <cge:TPSR_Ref TObjectID="6192455978123266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1092.58,1453.61) scale(0.916667,1.75926) translate(98.8256,-615.959)" width="12" x="1087.081745774372" y="1427.222222222222"/></g>
  <g id="171">
   <use class="kv10" height="30" transform="rotate(0,587.039,1306.19) scale(2.68492,2.77083) translate(-353.232,-808.219)" width="18" x="562.875" xlink:href="#EnergyConsumer:站用变_0" y="1264.625" zvalue="1643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979565058" ObjectName="3号站变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,587.039,1306.19) scale(2.68492,2.77083) translate(-353.232,-808.219)" width="18" x="562.875" y="1264.625"/></g>
  <g id="200">
   <use class="kv10" height="30" transform="rotate(180,1265.25,1459.61) scale(0.916667,1.75926) translate(114.523,-618.549)" width="12" x="1259.75" xlink:href="#EnergyConsumer:负荷_0" y="1433.222222222222" zvalue="1661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980220418" ObjectName="10kV多脉线"/>
   <cge:TPSR_Ref TObjectID="6192455980220418"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1265.25,1459.61) scale(0.916667,1.75926) translate(114.523,-618.549)" width="12" x="1259.75" y="1433.222222222222"/></g>
  <g id="212">
   <use class="kv10" height="30" transform="rotate(180,1436.36,1454.72) scale(0.916667,1.75926) translate(130.078,-616.439)" width="12" x="1430.861111111111" xlink:href="#EnergyConsumer:负荷_0" y="1428.333333333333" zvalue="1674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980548098" ObjectName="10kV双汉线"/>
   <cge:TPSR_Ref TObjectID="6192455980548098"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1436.36,1454.72) scale(0.916667,1.75926) translate(130.078,-616.439)" width="12" x="1430.861111111111" y="1428.333333333333"/></g>
  <g id="68">
   <use class="kv10" height="30" transform="rotate(180,1839.58,1454.78) scale(0.916667,1.75926) translate(166.735,-616.463)" width="12" x="1834.083333333333" xlink:href="#EnergyConsumer:负荷_0" y="1428.388888888889" zvalue="1737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907227137" ObjectName="10kV平交线(918)"/>
   <cge:TPSR_Ref TObjectID="6192456907227137"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1839.58,1454.78) scale(0.916667,1.75926) translate(166.735,-616.463)" width="12" x="1834.083333333333" y="1428.388888888889"/></g>
  <g id="133">
   <use class="kv10" height="30" transform="rotate(180,915.694,1456.78) scale(0.916667,1.75926) translate(82.7449,-617.326)" width="12" x="910.1944444444446" xlink:href="#EnergyConsumer:负荷_0" y="1430.388888888889" zvalue="1756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907554817" ObjectName="10kV备用线(911)"/>
   <cge:TPSR_Ref TObjectID="6192456907554817"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,915.694,1456.78) scale(0.916667,1.75926) translate(82.7449,-617.326)" width="12" x="910.1944444444446" y="1430.388888888889"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="16">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="16" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1097.58,1545) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.58" xml:space="preserve" y="1551.76" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940611589" ObjectName="P"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="44" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1097.58,1565) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.58" xml:space="preserve" y="1571.76" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940677125" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="53" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1099.83,1586.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.83" xml:space="preserve" y="1593.51" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940742661" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,727.594,372.25) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="379.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133403930628" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="73" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,727.594,401.25) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="408.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133403996165" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="74" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,727.594,426.5) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="433.26" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405044740" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="75" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,727.594,510.75) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="517.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405110277" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="76" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,727.594,455.25) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="462.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405175812" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="77" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,727.594,484.25) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="491.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405241348" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="78" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,727.594,541.25) scale(1,1) translate(0,0)" writing-mode="lr" x="727.59" xml:space="preserve" y="548.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405372420" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="115" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1120.3,634.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.3" xml:space="preserve" y="641.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124942839813" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="116" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1120.3,658.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.3" xml:space="preserve" y="665.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124942905349" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="117" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1117.3,976) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.3" xml:space="preserve" y="982.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939431941" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="118" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1117.3,1000) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.3" xml:space="preserve" y="1006.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939497477" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="119" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1120.3,682.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.3" xml:space="preserve" y="689.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939563013" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="121" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,816.3,803.25) scale(1,1) translate(0,0)" writing-mode="lr" x="816.3" xml:space="preserve" y="810.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939759621" ObjectName="油温1"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="122" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,816.3,851.25) scale(1,1) translate(0,0)" writing-mode="lr" x="816.3" xml:space="preserve" y="858.01" zvalue="1">ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128800354307" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="126" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1117.3,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.3" xml:space="preserve" y="1030.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940283909" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="129" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,816.3,827.25) scale(1,1) translate(0,0)" writing-mode="lr" x="816.3" xml:space="preserve" y="834.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940546053" ObjectName="绕温"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="135" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1942.25,931.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="938.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405437957" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="136" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1942.25,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="962.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405503493" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="137" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1942.25,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="986.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405569029" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="141" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1942.25,1061) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="1067.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405634565" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="142" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1942.25,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="1014.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405700101" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="143" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1942.25,1032) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="1038.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405765637" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1942.25,1089) scale(1,1) translate(0,0)" writing-mode="lr" x="1942.25" xml:space="preserve" y="1095.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133359235076" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="233" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1265.25,1551.5) scale(1,1) translate(-2.68285e-13,0)" writing-mode="lr" x="1265.25" xml:space="preserve" y="1558.27" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124932026375" ObjectName="P"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1436.36,1546.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.36" xml:space="preserve" y="1553.38" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124932812805" ObjectName="P"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="237" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1265.25,1576.5) scale(1,1) translate(-2.68285e-13,0)" writing-mode="lr" x="1265.25" xml:space="preserve" y="1583.27" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124935106565" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="238" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1436.36,1571.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.36" xml:space="preserve" y="1578.38" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124932878341" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="239" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1265.25,1601.5) scale(1,1) translate(-2.68285e-13,0)" writing-mode="lr" x="1265.25" xml:space="preserve" y="1608.27" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124935172101" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="241" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1436.36,1596.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.36" xml:space="preserve" y="1603.38" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124932943877" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="242" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,730.657,1669.13) scale(1,1) translate(0,0)" writing-mode="lr" x="730.66" xml:space="preserve" y="1675.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124944150533" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="247" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733.657,1693.13) scale(1,1) translate(0,0)" writing-mode="lr" x="733.66" xml:space="preserve" y="1699.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124944216069" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="248" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1337.18,47.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.18" xml:space="preserve" y="54.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945199109" ObjectName="P"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="249" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1337.18,72.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.18" xml:space="preserve" y="79.59999999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945264645" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="250" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1337.18,97.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.18" xml:space="preserve" y="104.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945330181" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="154" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1448.38,807.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.38" xml:space="preserve" y="814.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132856573956" ObjectName="绕温"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1448.38,855.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.38" xml:space="preserve" y="862.09" zvalue="1">ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132850872325" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="134" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1448.38,831.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.38" xml:space="preserve" y="838.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132856246276" ObjectName="油温1"/>
   </metadata>
  </g>
  <g id="761">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="761" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,265.158,759.767) scale(1,1) translate(0,0)" writing-mode="lr" x="265.16" xml:space="preserve" y="766.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124932419592" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="760">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="760" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,262.655,792.54) scale(1,1) translate(0,-1.74052e-13)" writing-mode="lr" x="262.65" xml:space="preserve" y="799.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124934320133" ObjectName="HYW3"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="163" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1708.2,641.794) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.2" xml:space="preserve" y="648.61" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852510724" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="164" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1708.2,670.794) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.2" xml:space="preserve" y="677.61" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852576260" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1720.2,1004.79) scale(1,1) translate(0,0)" writing-mode="lr" x="1720.2" xml:space="preserve" y="1011.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852641796" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="166" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1720.2,1033.79) scale(1,1) translate(0,0)" writing-mode="lr" x="1720.2" xml:space="preserve" y="1040.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852707332" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="168" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1708.2,699.794) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.2" xml:space="preserve" y="706.61" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852772868" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="169" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1720.7,1057.79) scale(1,1) translate(0,0)" writing-mode="lr" x="1720.7" xml:space="preserve" y="1064.56" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132856311812" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="BreakerClass">
  <g id="81">
   <use class="kv35" height="20" transform="rotate(0,1340.05,429) scale(2.83333,2.41667) translate(-857.925,-237.316)" width="10" x="1325.884590592946" xlink:href="#Breaker:开关_0" y="404.8333333333334" zvalue="1578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925393907715" ObjectName="35kV东朴线311"/>
   <cge:TPSR_Ref TObjectID="6473925393907715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1340.05,429) scale(2.83333,2.41667) translate(-857.925,-237.316)" width="10" x="1325.884590592946" y="404.8333333333334"/></g>
  <g id="18">
   <use class="kv35" height="20" transform="rotate(0,982.833,664.833) scale(2.83333,2.41667) translate(-626.784,-375.563)" width="10" x="968.6666666666666" xlink:href="#Breaker:开关_0" y="640.6666666666666" zvalue="1593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925393973251" ObjectName="35kV#1主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473925393973251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,982.833,664.833) scale(2.83333,2.41667) translate(-626.784,-375.563)" width="10" x="968.6666666666666" y="640.6666666666666"/></g>
  <g id="88">
   <use class="kv10" height="20" transform="rotate(0,982.833,1019.83) scale(2.83333,2.41667) translate(-626.784,-583.667)" width="10" x="968.6666666666666" xlink:href="#Breaker:开关_0" y="995.6666666666665" zvalue="1604"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925394038787" ObjectName="35kV#1主变10kV侧901"/>
   <cge:TPSR_Ref TObjectID="6473925394038787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,982.833,1019.83) scale(2.83333,2.41667) translate(-626.784,-583.667)" width="10" x="968.6666666666666" y="995.6666666666665"/></g>
  <g id="95">
   <use class="kv10" height="20" transform="rotate(0,726.051,1245.5) scale(2.83333,2.41667) translate(-460.631,-715.954)" width="10" x="711.8843236166796" xlink:href="#Breaker:开关_0" y="1221.333333333333" zvalue="1614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925394104323" ObjectName="10kV#1电容913"/>
   <cge:TPSR_Ref TObjectID="6473925394104323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,726.051,1245.5) scale(2.83333,2.41667) translate(-460.631,-715.954)" width="10" x="711.8843236166796" y="1221.333333333333"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(0,1092.42,1245.5) scale(2.83333,2.41667) translate(-697.691,-715.954)" width="10" x="1078.25" xlink:href="#Breaker:开关_0" y="1221.333333333333" zvalue="1649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925394169859" ObjectName="10kV朴圩线914"/>
   <cge:TPSR_Ref TObjectID="6473925394169859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1092.42,1245.5) scale(2.83333,2.41667) translate(-697.691,-715.954)" width="10" x="1078.25" y="1221.333333333333"/></g>
  <g id="199">
   <use class="kv10" height="20" transform="rotate(0,1265.08,1247.5) scale(2.83333,2.41667) translate(-809.418,-717.126)" width="10" x="1250.918254225628" xlink:href="#Breaker:开关_0" y="1223.333333333333" zvalue="1663"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925394235395" ObjectName="10kV多脉线915"/>
   <cge:TPSR_Ref TObjectID="6473925394235395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1265.08,1247.5) scale(2.83333,2.41667) translate(-809.418,-717.126)" width="10" x="1250.918254225628" y="1223.333333333333"/></g>
  <g id="211">
   <use class="kv10" height="20" transform="rotate(0,1436.2,1246.61) scale(2.83333,2.41667) translate(-920.137,-716.605)" width="10" x="1422.029365336738" xlink:href="#Breaker:开关_0" y="1222.444444444444" zvalue="1676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925394300931" ObjectName="10kV双汉线916"/>
   <cge:TPSR_Ref TObjectID="6473925394300931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1436.2,1246.61) scale(2.83333,2.41667) translate(-920.137,-716.605)" width="10" x="1422.029365336738" y="1222.444444444444"/></g>
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,1556.75,668.69) scale(2.83333,2.41667) translate(-998.141,-377.824)" width="10" x="1542.581355324528" xlink:href="#Breaker:开关_0" y="644.5238095238095" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925539332099" ObjectName="35kV#2主变35kV侧302"/>
   <cge:TPSR_Ref TObjectID="6473925539332099"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1556.75,668.69) scale(2.83333,2.41667) translate(-998.141,-377.824)" width="10" x="1542.581355324528" y="644.5238095238095"/></g>
  <g id="35">
   <use class="kv10" height="20" transform="rotate(0,1556.75,1039.69) scale(2.83333,2.41667) translate(-998.141,-595.307)" width="10" x="1542.581355324528" xlink:href="#Breaker:开关_0" y="1015.523809523809" zvalue="1723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925539266563" ObjectName="35kV#2主变10kV侧902"/>
   <cge:TPSR_Ref TObjectID="6473925539266563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1556.75,1039.69) scale(2.83333,2.41667) translate(-998.141,-595.307)" width="10" x="1542.581355324528" y="1015.523809523809"/></g>
  <g id="67">
   <use class="kv10" height="20" transform="rotate(0,1839.42,1246.67) scale(2.83333,2.41667) translate(-1181.05,-716.638)" width="10" x="1825.251587558961" xlink:href="#Breaker:开关_0" y="1222.5" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925539397635" ObjectName="10kV平交线918"/>
   <cge:TPSR_Ref TObjectID="6473925539397635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1839.42,1246.67) scale(2.83333,2.41667) translate(-1181.05,-716.638)" width="10" x="1825.251587558961" y="1222.5"/></g>
  <g id="132">
   <use class="kv10" height="20" transform="rotate(0,915.529,1248.67) scale(2.83333,2.41667) translate(-583.235,-717.81)" width="10" x="901.3626986700721" xlink:href="#Breaker:开关_0" y="1224.5" zvalue="1758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925539463171" ObjectName="10kV备用线911"/>
   <cge:TPSR_Ref TObjectID="6473925539463171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,915.529,1248.67) scale(2.83333,2.41667) translate(-583.235,-717.81)" width="10" x="901.3626986700721" y="1224.5"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(0,1641.08,1246.39) scale(2.83333,2.41667) translate(-1052.71,-716.475)" width="10" x="1626.918254225628" xlink:href="#Breaker:开关_0" y="1222.222222222222" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925539528707" ObjectName="10kV1号接地电阻器917"/>
   <cge:TPSR_Ref TObjectID="6473925539528707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1641.08,1246.39) scale(2.83333,2.41667) translate(-1052.71,-716.475)" width="10" x="1626.918254225628" y="1222.222222222222"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="82">
   <use class="kv35" height="22" transform="rotate(0,1340.33,368.667) scale(1.28788,2.19697) translate(-296.437,-187.693)" width="22" x="1326.166666507721" xlink:href="#DollyBreaker:手车_0" y="344.5000000000001" zvalue="1579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978188802" ObjectName="35kV东朴线311手车"/>
   <cge:TPSR_Ref TObjectID="6192455978188802"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1340.33,368.667) scale(1.28788,2.19697) translate(-296.437,-187.693)" width="22" x="1326.166666507721" y="344.5000000000001"/></g>
  <g id="83">
   <use class="kv35" height="22" transform="rotate(0,1340.48,490.5) scale(1.28788,-2.19697) translate(-296.47,-700.595)" width="22" x="1326.314224473777" xlink:href="#DollyBreaker:手车_0" y="466.3333333333334" zvalue="1581"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978254338" ObjectName="35kV东朴线311手车下"/>
   <cge:TPSR_Ref TObjectID="6192455978254338"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1340.48,490.5) scale(1.28788,-2.19697) translate(-296.47,-700.595)" width="22" x="1326.314224473777" y="466.3333333333334"/></g>
  <g id="19">
   <use class="kv35" height="22" transform="rotate(0,983.115,604.5) scale(1.28788,2.19697) translate(-216.589,-316.182)" width="22" x="968.9487425814413" xlink:href="#DollyBreaker:手车_0" y="580.3333333333334" zvalue="1595"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978582018" ObjectName="35kV#1主变35kV侧301手车"/>
   <cge:TPSR_Ref TObjectID="6192455978582018"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,983.115,604.5) scale(1.28788,2.19697) translate(-216.589,-316.182)" width="22" x="968.9487425814413" y="580.3333333333334"/></g>
  <g id="17">
   <use class="kv35" height="22" transform="rotate(0,983.263,726.333) scale(1.28788,-2.19697) translate(-216.622,-1043.77)" width="22" x="969.0963005474979" xlink:href="#DollyBreaker:手车_0" y="702.1666666666666" zvalue="1596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978516482" ObjectName="35kV#1主变35kV侧301手车下"/>
   <cge:TPSR_Ref TObjectID="6192455978516482"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,983.263,726.333) scale(1.28788,-2.19697) translate(-216.622,-1043.77)" width="22" x="969.0963005474979" y="702.1666666666666"/></g>
  <g id="87">
   <use class="kv10" height="22" transform="rotate(0,983.115,967.5) scale(1.28788,2.19697) translate(-216.589,-513.954)" width="22" x="968.9487425814413" xlink:href="#DollyBreaker:手车_0" y="943.3333333333334" zvalue="1606"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978713090" ObjectName="35kV#1主变10kV侧901手车"/>
   <cge:TPSR_Ref TObjectID="6192455978713090"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,983.115,967.5) scale(1.28788,2.19697) translate(-216.589,-513.954)" width="22" x="968.9487425814413" y="943.3333333333334"/></g>
  <g id="86">
   <use class="kv10" height="22" transform="rotate(0,983.263,1077.33) scale(1.28788,-2.19697) translate(-216.622,-1554.54)" width="22" x="969.0963005474979" xlink:href="#DollyBreaker:手车_0" y="1053.166666666667" zvalue="1607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978647554" ObjectName="35kV#1主变10kV侧901手车下"/>
   <cge:TPSR_Ref TObjectID="6192455978647554"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,983.263,1077.33) scale(1.28788,-2.19697) translate(-216.622,-1554.54)" width="22" x="969.0963005474979" y="1053.166666666667"/></g>
  <g id="96">
   <use class="kv10" height="22" transform="rotate(0,726.333,1185.17) scale(1.28788,2.19697) translate(-159.19,-632.545)" width="22" x="712.1663995314541" xlink:href="#DollyBreaker:手车_0" y="1160.999989827474" zvalue="1615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978844162" ObjectName="10kV#1电容913手车"/>
   <cge:TPSR_Ref TObjectID="6192455978844162"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,726.333,1185.17) scale(1.28788,2.19697) translate(-159.19,-632.545)" width="22" x="712.1663995314541" y="1160.999989827474"/></g>
  <g id="94">
   <use class="kv10" height="22" transform="rotate(0,726.199,1305.89) scale(1.28788,-2.19697) translate(-159.16,-1887.13)" width="22" x="712.0318815827361" xlink:href="#DollyBreaker:手车_0" y="1281.72221883138" zvalue="1616"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978778626" ObjectName="10kV#1电容913手车下"/>
   <cge:TPSR_Ref TObjectID="6192455978778626"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,726.199,1305.89) scale(1.28788,-2.19697) translate(-159.16,-1887.13)" width="22" x="712.0318815827361" y="1281.72221883138"/></g>
  <g id="175">
   <use class="kv10" height="22" transform="rotate(0,1092.7,1185.17) scale(1.28788,2.19697) translate(-241.084,-632.545)" width="22" x="1078.532075914774" xlink:href="#DollyBreaker:手车_0" y="1160.999989748001" zvalue="1650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979696130" ObjectName="10kV朴圩线914手车"/>
   <cge:TPSR_Ref TObjectID="6192455979696130"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1092.7,1185.17) scale(1.28788,2.19697) translate(-241.084,-632.545)" width="22" x="1078.532075914774" y="1160.999989748001"/></g>
  <g id="179">
   <use class="kv10" height="22" transform="rotate(0,1092.56,1305.89) scale(1.28788,-2.19697) translate(-241.054,-1887.13)" width="22" x="1078.397557966057" xlink:href="#DollyBreaker:手车_0" y="1281.722218884362" zvalue="1651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979761666" ObjectName="10kV朴圩线914手车下"/>
   <cge:TPSR_Ref TObjectID="6192455979761666"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1092.56,1305.89) scale(1.28788,-2.19697) translate(-241.054,-1887.13)" width="22" x="1078.397557966057" y="1281.722218884362"/></g>
  <g id="198">
   <use class="kv10" height="22" transform="rotate(0,1265.37,1187.17) scale(1.28788,2.19697) translate(-279.68,-633.634)" width="22" x="1251.200330140402" xlink:href="#DollyBreaker:手车_0" y="1162.999989748001" zvalue="1664"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980154882" ObjectName="10kV多脉线915手车"/>
   <cge:TPSR_Ref TObjectID="6192455980154882"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1265.37,1187.17) scale(1.28788,2.19697) translate(-279.68,-633.634)" width="22" x="1251.200330140402" y="1162.999989748001"/></g>
  <g id="197">
   <use class="kv10" height="22" transform="rotate(0,1265.23,1307.89) scale(1.28788,-2.19697) translate(-279.65,-1890.04)" width="22" x="1251.065812191684" xlink:href="#DollyBreaker:手车_0" y="1283.722218884362" zvalue="1665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980089346" ObjectName="10kV多脉线915手车下"/>
   <cge:TPSR_Ref TObjectID="6192455980089346"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1265.23,1307.89) scale(1.28788,-2.19697) translate(-279.65,-1890.04)" width="22" x="1251.065812191684" y="1283.722218884362"/></g>
  <g id="210">
   <use class="kv10" height="22" transform="rotate(0,1436.48,1186.28) scale(1.28788,2.19697) translate(-317.928,-633.15)" width="22" x="1422.311441251513" xlink:href="#DollyBreaker:手车_0" y="1162.111100859112" zvalue="1677"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980482562" ObjectName="10kV双汉线916手车"/>
   <cge:TPSR_Ref TObjectID="6192455980482562"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1436.48,1186.28) scale(1.28788,2.19697) translate(-317.928,-633.15)" width="22" x="1422.311441251513" y="1162.111100859112"/></g>
  <g id="209">
   <use class="kv10" height="22" transform="rotate(0,1436.34,1307) scale(1.28788,-2.19697) translate(-317.898,-1888.74)" width="22" x="1422.176923302795" xlink:href="#DollyBreaker:手车_0" y="1282.833329995473" zvalue="1678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980417026" ObjectName="10kV双汉线916手车下"/>
   <cge:TPSR_Ref TObjectID="6192455980417026"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1436.34,1307) scale(1.28788,-2.19697) translate(-317.898,-1888.74)" width="22" x="1422.176923302795" y="1282.833329995473"/></g>
  <g id="42">
   <use class="kv35" height="22" transform="rotate(0,1557.03,608.357) scale(1.28788,2.19697) translate(-344.875,-318.283)" width="22" x="1542.863431239303" xlink:href="#DollyBreaker:手车_0" y="584.1904761904763" zvalue="1717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456906899457" ObjectName="35kV#2主变35kV侧302手车"/>
   <cge:TPSR_Ref TObjectID="6192456906899457"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1557.03,608.357) scale(1.28788,2.19697) translate(-344.875,-318.283)" width="22" x="1542.863431239303" y="584.1904761904763"/></g>
  <g id="41">
   <use class="kv35" height="22" transform="rotate(0,1557.18,730.19) scale(1.28788,-2.19697) translate(-344.908,-1049.39)" width="22" x="1543.01098920536" xlink:href="#DollyBreaker:手车_0" y="706.0238095238095" zvalue="1718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456906833921" ObjectName="35kV#2主变35kV侧302手车下"/>
   <cge:TPSR_Ref TObjectID="6192456906833921"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1557.18,730.19) scale(1.28788,-2.19697) translate(-344.908,-1049.39)" width="22" x="1543.01098920536" y="706.0238095238095"/></g>
  <g id="34">
   <use class="kv10" height="22" transform="rotate(0,1557.03,979.357) scale(1.28788,2.19697) translate(-344.875,-520.414)" width="22" x="1542.863431239303" xlink:href="#DollyBreaker:手车_0" y="955.1904761904761" zvalue="1725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456906768385" ObjectName="35kV#2主变10kV侧902手车"/>
   <cge:TPSR_Ref TObjectID="6192456906768385"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1557.03,979.357) scale(1.28788,2.19697) translate(-344.875,-520.414)" width="22" x="1542.863431239303" y="955.1904761904761"/></g>
  <g id="33">
   <use class="kv10" height="22" transform="rotate(0,1557.18,1093.19) scale(1.28788,-2.19697) translate(-344.908,-1577.61)" width="22" x="1543.01098920536" xlink:href="#DollyBreaker:手车_0" y="1069.02380952381" zvalue="1726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456906702849" ObjectName="35kV#2主变10kV侧902手车下"/>
   <cge:TPSR_Ref TObjectID="6192456906702849"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1557.18,1093.19) scale(1.28788,-2.19697) translate(-344.908,-1577.61)" width="22" x="1543.01098920536" y="1069.02380952381"/></g>
  <g id="66">
   <use class="kv10" height="22" transform="rotate(0,1839.7,1186.33) scale(1.28788,2.19697) translate(-408.06,-633.18)" width="22" x="1825.533663473735" xlink:href="#DollyBreaker:手车_0" y="1162.166656414668" zvalue="1741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907161601" ObjectName="10kV平交线918手车"/>
   <cge:TPSR_Ref TObjectID="6192456907161601"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1839.7,1186.33) scale(1.28788,2.19697) translate(-408.06,-633.18)" width="22" x="1825.533663473735" y="1162.166656414668"/></g>
  <g id="65">
   <use class="kv10" height="22" transform="rotate(0,1839.57,1307.06) scale(1.28788,-2.19697) translate(-408.03,-1888.82)" width="22" x="1825.399145525017" xlink:href="#DollyBreaker:手车_0" y="1282.888885551029" zvalue="1742"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907096065" ObjectName="10kV平交线918手车下"/>
   <cge:TPSR_Ref TObjectID="6192456907096065"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1839.57,1307.06) scale(1.28788,-2.19697) translate(-408.03,-1888.82)" width="22" x="1825.399145525017" y="1282.888885551029"/></g>
  <g id="131">
   <use class="kv10" height="22" transform="rotate(0,915.811,1188.33) scale(1.28788,2.19697) translate(-201.544,-634.27)" width="22" x="901.6447745848465" xlink:href="#DollyBreaker:手车_0" y="1164.166656414668" zvalue="1760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907489281" ObjectName="10kV备用线911手车"/>
   <cge:TPSR_Ref TObjectID="6192456907489281"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,915.811,1188.33) scale(1.28788,2.19697) translate(-201.544,-634.27)" width="22" x="901.6447745848465" y="1164.166656414668"/></g>
  <g id="130">
   <use class="kv10" height="22" transform="rotate(0,915.677,1309.06) scale(1.28788,-2.19697) translate(-201.514,-1891.73)" width="22" x="901.5102566361285" xlink:href="#DollyBreaker:手车_0" y="1284.888885551029" zvalue="1761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907423745" ObjectName="10kV备用线911手车下"/>
   <cge:TPSR_Ref TObjectID="6192456907423745"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,915.677,1309.06) scale(1.28788,-2.19697) translate(-201.514,-1891.73)" width="22" x="901.5102566361285" y="1284.888885551029"/></g>
  <g id="150">
   <use class="kv10" height="22" transform="rotate(0,1641.37,1186.06) scale(1.28788,2.19697) translate(-363.727,-633.029)" width="22" x="1627.200330140402" xlink:href="#DollyBreaker:手车_0" y="1161.88887863689" zvalue="1774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907816961" ObjectName="10kV1号接地电阻器917手车"/>
   <cge:TPSR_Ref TObjectID="6192456907816961"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1641.37,1186.06) scale(1.28788,2.19697) translate(-363.727,-633.029)" width="22" x="1627.200330140402" y="1161.88887863689"/></g>
  <g id="149">
   <use class="kv10" height="22" transform="rotate(0,1641.23,1306.78) scale(1.28788,-2.19697) translate(-363.697,-1888.42)" width="22" x="1627.065812191684" xlink:href="#DollyBreaker:手车_0" y="1282.611107773251" zvalue="1775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907751425" ObjectName="10kV1号接地电阻器917手车下"/>
   <cge:TPSR_Ref TObjectID="6192456907751425"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1641.23,1306.78) scale(1.28788,-2.19697) translate(-363.697,-1888.42)" width="22" x="1627.065812191684" y="1282.611107773251"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="84">
   <path class="kv35" d="M 1340.55 489.86 L 1340.55 452.35" stroke-width="1" zvalue="1582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.55 489.86 L 1340.55 452.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1340.4 405.85 L 1340.4 369.31" stroke-width="1" zvalue="1583"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.4 405.85 L 1340.4 369.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 1340.5 512.45 L 1340.5 569" stroke-width="1" zvalue="1586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.5 512.45 L 1340.5 569" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 1340.35 346.71 L 1340.35 217.16" stroke-width="1" zvalue="1587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.35 346.71 L 1340.35 217.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 1397.71 319.46 L 1340.35 319.46" stroke-width="1" zvalue="1589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.71 319.46 L 1340.35 319.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 983.33 725.69 L 983.33 688.19" stroke-width="1" zvalue="1597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@1" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 725.69 L 983.33 688.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 983.18 641.68 L 983.18 605.14" stroke-width="1" zvalue="1598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.18 641.68 L 983.18 605.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 983.28 748.29 L 983.28 764.31" stroke-width="1" zvalue="1601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.28 748.29 L 983.28 764.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 983.13 582.55 L 983.13 569" stroke-width="1" zvalue="1602"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.13 582.55 L 983.13 569" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 983.33 1076.69 L 983.33 1043.19" stroke-width="1" zvalue="1608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 1076.69 L 983.33 1043.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 983.18 996.68 L 983.18 968.14" stroke-width="1" zvalue="1609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="87@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.18 996.68 L 983.18 968.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 983.13 945.55 L 983.13 884.7" stroke-width="1" zvalue="1610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="123@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.13 945.55 L 983.13 884.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 726.27 1305.25 L 726.27 1268.85" stroke-width="1" zvalue="1617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="95@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.27 1305.25 L 726.27 1268.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 726.4 1222.35 L 726.4 1185.81" stroke-width="1" zvalue="1618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.4 1222.35 L 726.4 1185.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 647.81 1128 L 647.81 1101.74" stroke-width="1" zvalue="1620"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.81 1128 L 647.81 1101.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 647.76 1024.32 L 647.76 997.67" stroke-width="1" zvalue="1621"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="359@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.76 1024.32 L 647.76 997.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 726.35 1163.21 L 726.35 1128" stroke-width="1" zvalue="1635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="139@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.35 1163.21 L 726.35 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 726.22 1327.84 L 726.22 1417.27" stroke-width="1" zvalue="1636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.22 1327.84 L 726.22 1417.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv10" d="M 766.71 1345.46 L 726.22 1345.46" stroke-width="1" zvalue="1637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.71 1345.46 L 726.22 1345.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 766.71 1504.8 L 726.14 1504.8" stroke-width="1" zvalue="1639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.71 1504.8 L 726.14 1504.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 587.81 1160.07 L 587.81 1128" stroke-width="1" zvalue="1646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="139@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.81 1160.07 L 587.81 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 587.86 1237.49 L 587.86 1265.32" stroke-width="1" zvalue="1647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.86 1237.49 L 587.86 1265.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1092.63 1305.25 L 1092.63 1268.85" stroke-width="1" zvalue="1652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@1" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.63 1305.25 L 1092.63 1268.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 1092.77 1222.35 L 1092.77 1185.81" stroke-width="1" zvalue="1653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="175@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.77 1222.35 L 1092.77 1185.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1092.72 1163.21 L 1092.72 1128" stroke-width="1" zvalue="1657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="139@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.72 1163.21 L 1092.72 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1092.58 1327.84 L 1092.58 1429.86" stroke-width="1" zvalue="1658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.58 1327.84 L 1092.58 1429.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1135.21 1359.88 L 1092.58 1359.88" stroke-width="1" zvalue="1659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1135.21 1359.88 L 1092.58 1359.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1265.3 1307.25 L 1265.3 1270.85" stroke-width="1" zvalue="1666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@1" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.3 1307.25 L 1265.3 1270.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 1265.43 1224.35 L 1265.43 1187.81" stroke-width="1" zvalue="1667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="198@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.43 1224.35 L 1265.43 1187.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1265.38 1165.21 L 1265.38 1128" stroke-width="1" zvalue="1670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="139@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.38 1165.21 L 1265.38 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1265.25 1329.84 L 1265.25 1435.86" stroke-width="1" zvalue="1671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.25 1329.84 L 1265.25 1435.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1307.87 1361.88 L 1265.25 1361.88" stroke-width="1" zvalue="1672"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.87 1361.88 L 1265.25 1361.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 1436.41 1306.36 L 1436.41 1269.96" stroke-width="1" zvalue="1679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@1" LinkObjectIDznd="211@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.41 1306.36 L 1436.41 1269.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 1436.54 1223.46 L 1436.54 1186.92" stroke-width="1" zvalue="1680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="210@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.54 1223.46 L 1436.54 1186.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 1436.5 1164.32 L 1436.5 1128" stroke-width="1" zvalue="1683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="139@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.5 1164.32 L 1436.5 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 1436.36 1328.95 L 1436.36 1430.97" stroke-width="1" zvalue="1684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.36 1328.95 L 1436.36 1430.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1478.98 1360.99 L 1436.36 1360.99" stroke-width="1" zvalue="1685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.98 1360.99 L 1436.36 1360.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv35" d="M 1251.98 597.82 L 1251.98 569" stroke-width="1" zvalue="1688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1251.98 597.82 L 1251.98 569" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 1252.03 675.24 L 1252.03 703.34" stroke-width="1" zvalue="1689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1252.03 675.24 L 1252.03 703.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 1557.24 729.55 L 1557.24 692.04" stroke-width="1" zvalue="1719"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.24 729.55 L 1557.24 692.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 1557.1 645.54 L 1557.1 609" stroke-width="1" zvalue="1720"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.1 645.54 L 1557.1 609" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 1557.2 752.14 L 1557.2 765.59" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.2 752.14 L 1557.2 765.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1557.05 586.4 L 1557.05 569" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.05 586.4 L 1557.05 569" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1557.24 1092.55 L 1557.24 1063.04" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.24 1092.55 L 1557.24 1063.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 1557.1 1016.54 L 1557.1 980" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.1 1016.54 L 1557.1 980" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1557.05 957.4 L 1557.05 885.99" stroke-width="1" zvalue="1729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="45@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.05 957.4 L 1557.05 885.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 1557.2 1115.14 L 1557.2 1128" stroke-width="1" zvalue="1730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="139@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.2 1115.14 L 1557.2 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 726.12 1542.46 L 726.17 1472.52" stroke-width="1" zvalue="1734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="102@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.12 1542.46 L 726.17 1472.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 726 1554.29 L 726 1572.86 L 763.14 1572.86 L 763.14 1504.8" stroke-width="1" zvalue="1735"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 726 1554.29 L 726 1572.86 L 763.14 1572.86 L 763.14 1504.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1839.63 1306.42 L 1839.63 1270.02" stroke-width="1" zvalue="1743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="67@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1839.63 1306.42 L 1839.63 1270.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 1839.77 1223.52 L 1839.77 1186.97" stroke-width="1" zvalue="1744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="66@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1839.77 1223.52 L 1839.77 1186.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1839.72 1164.38 L 1839.72 1128" stroke-width="1" zvalue="1747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="139@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1839.72 1164.38 L 1839.72 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1839.58 1329.01 L 1839.58 1431.03" stroke-width="1" zvalue="1748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1839.58 1329.01 L 1839.58 1431.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1882.21 1361.05 L 1839.58 1361.05" stroke-width="1" zvalue="1749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1882.21 1361.05 L 1839.58 1361.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 983.28 1099.29 L 983.28 1128" stroke-width="1" zvalue="1754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="139@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.28 1099.29 L 983.28 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 915.74 1308.42 L 915.74 1272.02" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.74 1308.42 L 915.74 1272.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 915.88 1225.52 L 915.88 1188.97" stroke-width="1" zvalue="1763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="131@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.88 1225.52 L 915.88 1188.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 915.83 1166.38 L 915.83 1128" stroke-width="1" zvalue="1766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="139@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.83 1166.38 L 915.83 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 915.69 1331.01 L 915.69 1433.03" stroke-width="1" zvalue="1767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.69 1331.01 L 915.69 1433.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 958.32 1363.05 L 915.69 1363.05" stroke-width="1" zvalue="1768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.32 1363.05 L 915.69 1363.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1641.3 1306.14 L 1641.3 1269.74" stroke-width="1" zvalue="1776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@1" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.3 1306.14 L 1641.3 1269.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1641.43 1223.24 L 1641.43 1186.7" stroke-width="1" zvalue="1777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="150@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.43 1223.24 L 1641.43 1186.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1641.38 1164.1 L 1641.38 1128" stroke-width="1" zvalue="1780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="139@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.38 1164.1 L 1641.38 1128" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 1683.87 1360.77 L 1641.25 1360.77" stroke-width="1" zvalue="1782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1683.87 1360.77 L 1641.25 1360.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1641.25 1328.73 L 1641.25 1465.39" stroke-width="1" zvalue="1788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1641.25 1328.73 L 1641.25 1465.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="2">
   <use class="kv35" height="30" transform="rotate(180,1340.35,194.364) scale(1.90476,-1.53535) translate(-633.5,-312.925)" width="7" x="1333.68418764937" xlink:href="#ACLineSegment:线路_0" y="171.3333333333335" zvalue="1585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249369280516" ObjectName="35kV东朴线"/>
   <cge:TPSR_Ref TObjectID="8444249369280516_5066549698756609"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1340.35,194.364) scale(1.90476,-1.53535) translate(-633.5,-312.925)" width="7" x="1333.68418764937" y="171.3333333333335"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="5">
   <use class="kv35" height="20" transform="rotate(270,1425.33,319.667) scale(2.83333,2.83333) translate(-913.108,-188.51)" width="10" x="1411.166666666667" xlink:href="#GroundDisconnector:地刀_0" y="291.3333333333333" zvalue="1588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978450946" ObjectName="35kV东朴线31168"/>
   <cge:TPSR_Ref TObjectID="6192455978450946"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1425.33,319.667) scale(2.83333,2.83333) translate(-913.108,-188.51)" width="10" x="1411.166666666667" y="291.3333333333333"/></g>
  <g id="101">
   <use class="kv10" height="20" transform="rotate(270,794.333,1345.67) scale(2.83333,2.83333) translate(-504.814,-852.392)" width="10" x="780.1666615804033" xlink:href="#GroundDisconnector:地刀_0" y="1317.333333651225" zvalue="1623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979040770" ObjectName="10kV#1电容91368"/>
   <cge:TPSR_Ref TObjectID="6192455979040770"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,794.333,1345.67) scale(2.83333,2.83333) translate(-504.814,-852.392)" width="10" x="780.1666615804033" y="1317.333333651225"/></g>
  <g id="104">
   <use class="kv10" height="20" transform="rotate(270,794.333,1505) scale(2.83333,2.83333) translate(-504.814,-955.49)" width="10" x="780.1666615804036" xlink:href="#GroundDisconnector:地刀_0" y="1476.666666984558" zvalue="1627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979237378" ObjectName="10kV#1电容91398"/>
   <cge:TPSR_Ref TObjectID="6192455979237378"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,794.333,1505) scale(2.83333,2.83333) translate(-504.814,-955.49)" width="10" x="780.1666615804036" y="1476.666666984558"/></g>
  <g id="181">
   <use class="kv10" height="20" transform="rotate(270,1162.83,1360.08) scale(2.83333,2.83333) translate(-743.255,-861.721)" width="10" x="1148.666666666667" xlink:href="#GroundDisconnector:地刀_0" y="1331.750000317892" zvalue="1655"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979892738" ObjectName="10kV朴圩线91468"/>
   <cge:TPSR_Ref TObjectID="6192455979892738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1162.83,1360.08) scale(2.83333,2.83333) translate(-743.255,-861.721)" width="10" x="1148.666666666667" y="1331.750000317892"/></g>
  <g id="194">
   <use class="kv10" height="20" transform="rotate(270,1335.5,1362.08) scale(2.83333,2.83333) translate(-854.981,-863.015)" width="10" x="1321.334920892294" xlink:href="#GroundDisconnector:地刀_0" y="1333.750000317892" zvalue="1668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980023810" ObjectName="10kV多脉线91568"/>
   <cge:TPSR_Ref TObjectID="6192455980023810"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1335.5,1362.08) scale(2.83333,2.83333) translate(-854.981,-863.015)" width="10" x="1321.334920892294" y="1333.750000317892"/></g>
  <g id="206">
   <use class="kv10" height="20" transform="rotate(270,1506.61,1361.19) scale(2.83333,2.83333) translate(-965.7,-862.44)" width="10" x="1492.446032003405" xlink:href="#GroundDisconnector:地刀_0" y="1332.861111429003" zvalue="1681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980351490" ObjectName="10kV双汉线91668"/>
   <cge:TPSR_Ref TObjectID="6192455980351490"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1506.61,1361.19) scale(2.83333,2.83333) translate(-965.7,-862.44)" width="10" x="1492.446032003405" y="1332.861111429003"/></g>
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,1909.83,1361.25) scale(2.83333,2.83333) translate(-1226.61,-862.475)" width="10" x="1895.668254225627" xlink:href="#GroundDisconnector:地刀_0" y="1332.916666984559" zvalue="1745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907030529" ObjectName="10kV平交线91868"/>
   <cge:TPSR_Ref TObjectID="6192456907030529"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1909.83,1361.25) scale(2.83333,2.83333) translate(-1226.61,-862.475)" width="10" x="1895.668254225627" y="1332.916666984559"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(270,985.946,1363.25) scale(2.83333,2.83333) translate(-628.798,-863.77)" width="10" x="971.7793653367386" xlink:href="#GroundDisconnector:地刀_0" y="1334.916666984559" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907358209" ObjectName="10kV备用线91168"/>
   <cge:TPSR_Ref TObjectID="6192456907358209"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,985.946,1363.25) scale(2.83333,2.83333) translate(-628.798,-863.77)" width="10" x="971.7793653367386" y="1334.916666984559"/></g>
  <g id="146">
   <use class="kv10" height="20" transform="rotate(270,1711.5,1360.97) scale(2.83333,2.83333) translate(-1098.28,-862.296)" width="10" x="1697.334920892294" xlink:href="#GroundDisconnector:地刀_0" y="1332.638889206781" zvalue="1778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456907685889" ObjectName="10kV1号接地电阻器91768"/>
   <cge:TPSR_Ref TObjectID="6192456907685889"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1711.5,1360.97) scale(2.83333,2.83333) translate(-1098.28,-862.296)" width="10" x="1697.334920892294" y="1332.638889206781"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="97">
   <use class="kv10" height="26" transform="rotate(0,647.777,1063) scale(1.42857,3.42308) translate(-191.333,-720.961)" width="14" x="637.777189217588" xlink:href="#Disconnector:联体手车刀闸_0" y="1018.5" zvalue="1619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455978909698" ObjectName="10kV母线PT0951"/>
   <cge:TPSR_Ref TObjectID="6192455978909698"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,647.777,1063) scale(1.42857,3.42308) translate(-191.333,-720.961)" width="14" x="637.777189217588" y="1018.5"/></g>
  <g id="102">
   <use class="kv10" height="30" transform="rotate(0,726.05,1444.67) scale(1.88889,1.88889) translate(-335.004,-666.51)" width="15" x="711.8836016283149" xlink:href="#Disconnector:刀闸_0" y="1416.333333333333" zvalue="1625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979106306" ObjectName="10kV#1电容9139"/>
   <cge:TPSR_Ref TObjectID="6192455979106306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,726.05,1444.67) scale(1.88889,1.88889) translate(-335.004,-666.51)" width="15" x="711.8836016283149" y="1416.333333333333"/></g>
  <g id="172">
   <use class="kv10" height="26" transform="rotate(0,587.825,1198.75) scale(1.42857,3.42308) translate(-173.348,-817.053)" width="14" x="577.8250199408799" xlink:href="#Disconnector:联体手车刀闸_0" y="1154.25" zvalue="1645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979630594" ObjectName="10kV3号站变9121"/>
   <cge:TPSR_Ref TObjectID="6192455979630594"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,587.825,1198.75) scale(1.42857,3.42308) translate(-173.348,-817.053)" width="14" x="577.8250199408799" y="1154.25"/></g>
  <g id="213">
   <use class="kv35" height="26" transform="rotate(0,1252,636.5) scale(1.42857,3.42308) translate(-372.6,-419.056)" width="14" x="1242" xlink:href="#Disconnector:联体手车刀闸_0" y="592" zvalue="1687"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455980613634" ObjectName="35kV母线PT0351"/>
   <cge:TPSR_Ref TObjectID="6192455980613634"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1252,636.5) scale(1.42857,3.42308) translate(-372.6,-419.056)" width="14" x="1242" y="592"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="108">
   <use class="kv10" height="18" transform="rotate(0,726.046,1548.13) scale(2.09877,1.11111) translate(-370.217,-153.813)" width="18" x="707.1568463397314" xlink:href="#Compensator:并联电容器2_0" y="1538.126984126984" zvalue="1634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192455979499522" ObjectName="10kV#1电容"/>
   <cge:TPSR_Ref TObjectID="6192455979499522"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,726.046,1548.13) scale(2.09877,1.11111) translate(-370.217,-153.813)" width="18" x="707.1568463397314" y="1538.126984126984"/></g>
 </g>
</svg>