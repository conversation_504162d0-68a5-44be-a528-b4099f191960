<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684862977" height="1601" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1601" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,0,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(255,170,199);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(227,227,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(85,170,0);fill:none}
.kv6{stroke:rgb(85,170,0);fill:none}
.v400{stroke:rgb(255,85,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4661605870522134" x2="7.700506079614452" y1="7.128423176033875" y2="24.03824349063279"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="12" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="2" x="12.08333333333333" xlink:href="#terminal" y="8"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="12" xlink:href="#terminal" y="29"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <path d="M 6.025 3.025 L 3.05 10 L 6.025 7.85833 L 9.025 10.05 L 6.025 3.025" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="7.877914951989029" y2="28.48902606310013"/>
  </symbol>
  <symbol id=":bs电缆_0" viewBox="0,0,30,30">
   <path d="M 10.9166 4.25 L 19.25 4.25 L 15.0833 8.09722 L 10.9166 4.25 z" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08327650869417" x2="15.08327650869417" y1="8.097222222222227" y2="23.48611111111108"/>
   <path d="M 10.8333 27.3333 L 19.1668 27.3333 L 15.0001 23.4861 L 10.8333 27.3333" fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="GroundDisconnector:手车式地刀_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="1.180481942670642" y2="5.990826770256849"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="4.186947459912011" y2="8.997292287498219"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="4.186947459912011" y2="8.997292287498219"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="1.180481942670642" y2="5.990826770256849"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="21.02315435646374" y2="16.21280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="23.82918883922238" y2="19.01884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="21.02315435646374" y2="16.21280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="23.82918883922238" y2="19.01884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="15.00133744855967" y1="4.186947459912016" y2="21.02315435646375"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.03141025641026" x2="15.03141025641026" y1="26.11666666666667" y2="23.7142916052417"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.61666666666667" x2="17.95" y1="26.19694619969017" y2="26.19694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.03333333333333" x2="16.95" y1="28.03157590710537" y2="28.03157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.72402184466163" x2="16.38079938068318" y1="29.78348701738202" y2="29.78348701738202"/>
  </symbol>
  <symbol id="GroundDisconnector:手车式地刀_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.03141025641026" x2="15.03141025641026" y1="26.11666666666667" y2="23.7142916052417"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.61666666666667" x2="17.95" y1="26.19694619969017" y2="26.19694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.03333333333333" x2="16.95" y1="28.03157590710537" y2="28.03157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.72402184466163" x2="16.38079938068318" y1="29.78348701738202" y2="29.78348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="9.078947368421051" y1="1.107730263157896" y2="6.041118421052634"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.92105263157895" x2="15.03700657894737" y1="5.967105263157894" y2="1.107730263157896"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.162280701754387" x2="15.04632675438597" y1="19.1125548245614" y2="24.03689692982456"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.03700657894737" x2="20.95805921052632" y1="24.12023026315789" y2="19.23289473684211"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="15" y1="1.166666666666663" y2="23.83333333333334"/>
  </symbol>
  <symbol id="GroundDisconnector:手车式地刀_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.03141025641026" x2="15.03141025641026" y1="26.11666666666667" y2="23.7142916052417"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.61666666666667" x2="17.95" y1="26.19694619969017" y2="26.19694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.03333333333333" x2="16.95" y1="28.03157590710537" y2="28.03157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.72402184466163" x2="16.38079938068318" y1="29.78348701738202" y2="29.78348701738202"/>
   <ellipse cx="14.99" cy="3.53" fill-opacity="0" rx="2.9" ry="2.9" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.01" cy="22.37" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.99506578947368" x2="9.078947368421053" y1="5.042763157894736" y2="21.0296052631579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.967927631578947" x2="20.95805921052632" y1="5.00575657894737" y2="20.95559210526316"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸(带熔断器)_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3.25"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.58,15) scale(1,1) translate(0,0)" width="4.33" x="5.42" y="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="3.249999999999996" y2="8.060344827586203"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="6.25646551724137" y2="11.06681034482758"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="6.25646551724137" y2="11.06681034482758"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="3.249999999999996" y2="8.060344827586203"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="23.0926724137931" y2="18.2823275862069"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="25.89870689655174" y2="21.08836206896553"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="23.0926724137931" y2="18.2823275862069"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="25.89870689655174" y2="21.08836206896553"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.45833333333333" x2="7.45833333333333" y1="3.25" y2="26"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸(带熔断器)_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3.25"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.58,15) scale(1,1) translate(0,0)" width="4.33" x="5.42" y="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="3.249999999999996" y2="8.060344827586203"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="3.249999999999996" y2="8.060344827586203"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.458333333333333" x2="2" y1="25.89870689655174" y2="21.08836206896553"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.45833333333333" x2="12.91666666666666" y1="25.89870689655174" y2="21.08836206896553"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.45833333333333" x2="7.45833333333333" y1="3.25" y2="26"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸(带熔断器)_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3.25"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <ellipse cx="7.44" cy="5.99" fill-opacity="0" rx="2.9" ry="2.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.46" cy="26.07" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.4438048245614" x2="1.527686403508772" y1="8.994933805154314" y2="24.98177591041747"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.25" x2="13.24013157894737" y1="9.20792722620695" y2="25.15776275252274"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.686947459912016" y2="21.52315435646375"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.000000000000001" x2="1.078947368421052" y1="1.607730263157897" y2="6.541118421052633"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.92105263157895" x2="7.037006578947368" y1="6.467105263157893" y2="1.607730263157894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.078947368421053" x2="6.962993421052632" y1="19.44588815789474" y2="24.37023026315789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.037006578947368" x2="12.95805921052632" y1="24.37023026315789" y2="19.48289473684211"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7" x2="7" y1="1.666666666666664" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <ellipse cx="6.99" cy="3.03" fill-opacity="0" rx="2.9" ry="2.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.01" cy="23.12" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.99506578947368" x2="1.078947368421053" y1="6.042763157894737" y2="22.0296052631579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9679276315789469" x2="12.95805921052632" y1="6.00575657894737" y2="21.95559210526316"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(0,170,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:前置通道状态_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:前置通道状态_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_2" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_3" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(80,80,80)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_4" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,0,255)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:bsPT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="1" xlink:href="#terminal" y="15"/>
   <ellipse cx="17.78" cy="15.05" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.38" cy="15.05" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,8.08,15) scale(1,1) translate(0,0)" width="4.92" x="5.62" y="11.29"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.7916666666666625" x2="12.79166666666666" y1="14.99166666666667" y2="14.99166666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,13,15">
   <use terminal-index="0" type="0" x="6.320083175780933" xlink:href="#terminal" y="1.13911202992715"/>
   <path d="M 3.3 3.15 L 3.3 1.15 L 9.3 1.15 L 9.3 3.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 2.5 5.5 L 3.5 5.5 L 3.5 6 L 4.46667 6" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.5" x2="4.5" y1="4.75" y2="4.75"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(0,9.26,5.54) scale(1,1) translate(0,0)" width="2.22" x="8.15" y="3.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.266666666666666" x2="9.266666666666666" y1="3.333333333333334" y2="8.699999999999999"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(-90,3.33,6.74) scale(1,1) translate(0,0)" width="7.05" x="-0.19" y="5.22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.369763607738507" x2="3.369763607738507" y1="10.41666666666667" y2="12.94654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.802427517957054" x2="4.93709969751996" y1="13.03704961606615" y2="13.03704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.399066340209898" x2="4.488847793251836" y1="13.73364343374679" y2="13.73364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.853758628586885" x2="3.811575127897769" y1="14.25608879700729" y2="14.25608879700729"/>
   <ellipse cx="9.15" cy="10.28" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="8.15" cy="12.03" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <ellipse cx="10.4" cy="12.03" fill-opacity="0" rx="1.54" ry="1.54" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
   <path d="M 2.5 7 L 3.5 7 L 3.5 7.5 L 4.46667 7.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.5" x2="3.5" y1="8.283333333333333" y2="8.283333333333333"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="9.08" cy="8.92" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.02" cy="21.08" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <ellipse cx="9.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.983423268289604" x2="9.983423268289604" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.983423268289606" x2="6.562760003291478" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.00726151837816" x2="13.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 6.68949 9.29402 L 13.7806 9.29402 L 10.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:正常供电_0" viewBox="0,0,31,14">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="12.83" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.5,6.67) scale(1,1) translate(0,0)" width="30.5" x="0.25" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.5" xml:space="preserve" y="9.166666666666668">   正常供电   </text>
  </symbol>
  <symbol id="State:正常供电_1" viewBox="0,0,31,14">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="13.33" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.42,7) scale(1,1) translate(0,0)" width="30.5" x="0.17" y="0.33"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.41666666666667" xml:space="preserve" y="9.5"> 全站保供电 </text>
  </symbol>
  <symbol id="State:全站正常运行_0" viewBox="0,0,30,10">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警启用</text>
  </symbol>
  <symbol id="State:全站正常运行_1" viewBox="0,0,30,10">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警退出</text>
  </symbol>
  <symbol id="Compensator:并联电容器接地_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.958333333333334" xlink:href="#terminal" y="0.4980091012514087"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.131944444444448" x2="5.833333333333334" y1="19.80019908987487" y2="19.80019908987487"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.892361111111116" x2="7.024305555555551" y1="18.31612627986349" y2="18.31612627986349"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.008333333333334" x2="5.008333333333334" y1="8.916666666666666" y2="0.332337883959033"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.652777777777779" x2="8.263888888888889" y1="16.74872013651877" y2="16.74872013651877"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.04999999999999893" x2="9.966666666666669" y1="12.52261092150171" y2="12.52261092150171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-8.881784197001252e-16" x2="9.916666666666668" y1="9.055460750853236" y2="9.055460750853236"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.008333333333334" x2="5.008333333333334" y1="12.57783466059917" y2="16.75"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV祷午站" InitShowingPlane="" fill="rgb(0,0,0)" height="1601" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
  
  
  
  
  
 </g>
 <g id="OtherClass">
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="403.8675726617296" y1="1370.331272377947" y2="1370.331272377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="403.8675726617296" y1="1409.267572377948" y2="1409.267572377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="81.01157266172959" y1="1370.331272377947" y2="1409.267572377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="403.8675726617296" x2="403.8675726617296" y1="1370.331272377947" y2="1409.267572377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="137.9254726617296" y1="1409.267372377947" y2="1409.267372377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="137.9254726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="81.01157266172959" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9254726617296" x2="137.9254726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="194.8404726617296" y1="1409.267372377947" y2="1409.267372377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="194.8404726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="137.9265726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8404726617296" x2="194.8404726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="259.8252726617295" y1="1409.267372377947" y2="1409.267372377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="259.8252726617295" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="194.8401726617295" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8252726617295" x2="259.8252726617295" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="324.8106726617295" y1="1409.267372377947" y2="1409.267372377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="324.8106726617295" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="259.8255726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8106726617295" x2="324.8106726617295" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="403.8694726617296" y1="1409.267372377947" y2="1409.267372377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="403.8694726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="324.8108726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="403.8694726617296" x2="403.8694726617296" y1="1409.267372377947" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="137.9254726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="137.9254726617296" y1="1510.254172377947" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="81.01157266172959" x2="81.01157266172959" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9254726617296" x2="137.9254726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="194.8404726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="194.8404726617296" y1="1510.254172377947" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="137.9265726617296" x2="137.9265726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8404726617296" x2="194.8404726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="259.8252726617295" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="259.8252726617295" y1="1510.254172377947" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.8401726617295" x2="194.8401726617295" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8252726617295" x2="259.8252726617295" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="324.8106726617295" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="324.8106726617295" y1="1510.254172377947" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="259.8255726617296" x2="259.8255726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8106726617295" x2="324.8106726617295" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="403.8694726617296" y1="1457.766672377948" y2="1457.766672377948"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="403.8694726617296" y1="1510.254172377947" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="324.8108726617296" x2="324.8108726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="403.8694726617296" x2="403.8694726617296" y1="1457.766672377948" y2="1510.254172377947"/>
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.533,125.268) scale(1,1) translate(0,0)" writing-mode="lr" x="242.53" xml:space="preserve" y="129.77" zvalue="883"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,280.158,125.94) scale(1,1) translate(0,0)" writing-mode="lr" x="280.16" xml:space="preserve" y="134.94" zvalue="884">35kV祷午站</text>
  <rect fill="none" fill-opacity="0" height="1418.18" id="234" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,243.656,801.591) scale(1,1) translate(1.8411e-14,0)" width="321.48" x="82.92" y="92.5" zvalue="885"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40.27" id="230" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,319.943,851.508) scale(1,1) translate(0,-1.84602e-13)" width="135.08" x="252.4" y="831.38" zvalue="886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.943,851.508) scale(1,1) translate(0,0)" writing-mode="lr" x="319.94" xml:space="preserve" y="861.01" zvalue="886">网络状态</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40.27" id="207" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,173.943,851.46) scale(1,1) translate(0,1.84592e-13)" width="135.08" x="106.4" y="831.33" zvalue="892"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,173.943,851.46) scale(1,1) translate(0,0)" writing-mode="lr" x="173.94" xml:space="preserve" y="860.96" zvalue="892">公用信号</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40.27" id="118" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,172.943,903.236) scale(1,1) translate(0,-1.96088e-13)" width="135.08" x="105.4" y="883.1" zvalue="925"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,172.943,903.236) scale(1,1) translate(0,0)" writing-mode="lr" x="172.94" xml:space="preserve" y="912.74" zvalue="925">间隔索引</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,111.462,1431.99) scale(1,1) translate(0,0)" writing-mode="lr" x="111.46" xml:space="preserve" y="1437.99" zvalue="927">数１</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169.157,1431.99) scale(1,1) translate(0,0)" writing-mode="lr" x="169.16" xml:space="preserve" y="1437.99" zvalue="928">数２</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,370.482,1425.75) scale(1,1) translate(0,0)" writing-mode="lr" x="370.48" xml:space="preserve" y="1431.25" zvalue="929">值班节点</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.875,1431.99) scale(1,1) translate(0,0)" writing-mode="lr" x="235.87" xml:space="preserve" y="1437.99" zvalue="932">专１</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,293.57,1431.99) scale(1,1) translate(0,0)" writing-mode="lr" x="293.57" xml:space="preserve" y="1437.99" zvalue="933">专2</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="40.27" id="330" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,320.943,902.508) scale(1,1) translate(0,-1.95927e-13)" width="135.08" x="253.4" y="882.38" zvalue="980"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.943,902.508) scale(1,1) translate(0,0)" writing-mode="lr" x="320.94" xml:space="preserve" y="912.01" zvalue="980">状态监视</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768,457) scale(1,1) translate(0,0)" writing-mode="lr" x="768" xml:space="preserve" y="464.5" zvalue="54">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.938,533.341) scale(1,1) translate(0,0)" writing-mode="lr" x="769.9400000000001" xml:space="preserve" y="539.34" zvalue="56">3121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.14,378.25) scale(1,1) translate(0,0)" writing-mode="lr" x="769.14" xml:space="preserve" y="384.25" zvalue="60">3123</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.25,398.25) scale(1,1) translate(0,0)" writing-mode="lr" x="859.25" xml:space="preserve" y="402.75" zvalue="63">31237</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.25,476.25) scale(1,1) translate(0,0)" writing-mode="lr" x="858.25" xml:space="preserve" y="480.75" zvalue="65">31217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.25,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="859.25" xml:space="preserve" y="329.75" zvalue="68">31238</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="2" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770,224.889) scale(1,1) translate(0,0)" writing-mode="tb" x="770" xml:space="preserve" y="224.89" zvalue="80">林祷线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.97,641.129) scale(1,1) translate(0,0)" writing-mode="lr" x="1488.97" xml:space="preserve" y="647.13" zvalue="87">3911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.36,790.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.36" xml:space="preserve" y="797.4299999999999" zvalue="126">3号站变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.7,815.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1458.7" xml:space="preserve" y="821.38" zvalue="130">S11-50/35</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.36,559.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.36" xml:space="preserve" y="566.64" zvalue="132">35kV1号母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.91,753.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.91" xml:space="preserve" y="761.14" zvalue="146">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032,694) scale(1,1) translate(0,0)" writing-mode="lr" x="1032" xml:space="preserve" y="701.5" zvalue="148">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.64,620) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.64" xml:space="preserve" y="626" zvalue="152">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.5,640) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.5" xml:space="preserve" y="644.5" zvalue="156">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.65,778.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.647727272727" xml:space="preserve" y="784.0454545454546" zvalue="158">SZ11-3150/35</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.1,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.1" xml:space="preserve" y="1141.95" zvalue="164">901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.013,1135.45) scale(1,1) translate(0,0)" writing-mode="lr" x="840.01" xml:space="preserve" y="1142.95" zvalue="175">907</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="80" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.112,1269.73) scale(1,1) translate(0,0)" writing-mode="tb" x="847.11" xml:space="preserve" y="1269.73" zvalue="182">备用Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.682,1215.45) scale(1,1) translate(0,0)" writing-mode="lr" x="911.6799999999999" xml:space="preserve" y="1219.95" zvalue="187">90738</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,948.013,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="948.01" xml:space="preserve" y="1141.95" zvalue="194">906</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="79" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.112,1266.73) scale(1,1) translate(0,0)" writing-mode="tb" x="959.11" xml:space="preserve" y="1266.73" zvalue="201">备用Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.68,1215.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.68" xml:space="preserve" y="1219.95" zvalue="202">90638</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.01,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.01" xml:space="preserve" y="1141.95" zvalue="208">905</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="83" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.11,1267.73) scale(1,1) translate(0,0)" writing-mode="tb" x="1065.11" xml:space="preserve" y="1267.73" zvalue="215">岩桃线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.68,1215.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.68" xml:space="preserve" y="1219.95" zvalue="217">90538</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.01,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.01" xml:space="preserve" y="1141.95" zvalue="223">904</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="100" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.11,1270.73) scale(1,1) translate(0,0)" writing-mode="tb" x="1181.11" xml:space="preserve" y="1270.73" zvalue="230">那桃线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.68,1215.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.68" xml:space="preserve" y="1219.95" zvalue="232">90438</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1187.65,801.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.647727272727" xml:space="preserve" y="807.0454545454545" zvalue="300"> 35±3*2.5%/10.5kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.65,823.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.647727272727" xml:space="preserve" y="829.0454545454545" zvalue="302"> Ud=6.89%,Yd11</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1719.77,1134.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1719.77" xml:space="preserve" y="1140.27" zvalue="304">0951</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1757.28,1278) scale(1,1) translate(0,0)" writing-mode="lr" x="1757.28" xml:space="preserve" y="1284.5" zvalue="307">1号母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1820.26,1134.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1820.26" xml:space="preserve" y="1139.33" zvalue="309">09518</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1421.55,1111.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1421.55" xml:space="preserve" y="1117.36" zvalue="313">9911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.25,1286.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.25" xml:space="preserve" y="1292.86" zvalue="315">4号站变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1470.25,1309.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1470.25" xml:space="preserve" y="1315.36" zvalue="319">Sc11-50/10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.649,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="703.65" xml:space="preserve" y="1141.95" zvalue="323">908</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.318,1214.45) scale(1,1) translate(0,0)" writing-mode="lr" x="777.3200000000001" xml:space="preserve" y="1218.95" zvalue="332">90838</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.318,1290.45) scale(1,1) translate(0,0)" writing-mode="lr" x="702.3200000000001" xml:space="preserve" y="1296.45" zvalue="337">9086</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.318,1315.45) scale(1,1) translate(0,0)" writing-mode="lr" x="776.3200000000001" xml:space="preserve" y="1319.95" zvalue="343">90868</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,608,1137) scale(1,1) translate(0,0)" writing-mode="lr" x="608" xml:space="preserve" y="1143" zvalue="509">9001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="495" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,248.731,167.452) scale(1,1) translate(0,0)" writing-mode="lr" x="248.73" xml:space="preserve" y="173.95" zvalue="567">图纸版本：祷午站2019-1</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="379.5938132756133" y2="379.5938132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="403.9830132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="379.5938132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="379.5938132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="379.5938132756133" y2="379.5938132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="403.9830132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="379.5938132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="379.5938132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="403.9830132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="428.3722132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="403.9830132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="403.9830132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="403.9830132756133" y2="403.9830132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="428.3722132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="403.9830132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="403.9830132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="428.3722132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="452.7614132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="428.3722132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="428.3722132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="428.3722132756133" y2="428.3722132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="452.7614132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="428.3722132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="428.3722132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="452.7614132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="477.1506132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="452.7614132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="452.7614132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="452.7614132756133" y2="452.7614132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="477.1506132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="452.7614132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="452.7614132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="477.1506132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="501.5398132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="477.1506132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="477.1506132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="477.1506132756133" y2="477.1506132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="501.5398132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="477.1506132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="477.1506132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="501.5398132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="525.9290132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="501.5398132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="501.5398132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="501.5398132756133" y2="501.5398132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="525.9290132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="501.5398132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="501.5398132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="525.9290132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="550" y1="550.3182132756133" y2="550.3182132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.5" x2="487.5" y1="525.9290132756133" y2="550.3182132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="525.9290132756133" y2="550.3182132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="525.9290132756133" y2="525.9290132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="612.5" y1="550.3182132756133" y2="550.3182132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="550" x2="550" y1="525.9290132756133" y2="550.3182132756133"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.5" x2="612.5" y1="525.9290132756133" y2="550.3182132756133"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="502" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.206,349.5) scale(1,1) translate(0,0)" writing-mode="lr" x="547.21" xml:space="preserve" y="355.5" zvalue="611">35kV1号母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="513" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.706,822.932) scale(1,1) translate(0,0)" writing-mode="lr" x="552.71" xml:space="preserve" y="828.9299999999999" zvalue="621">10kV1号母线</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="851.480176911977" y2="851.480176911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="875.8693769119769" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="851.480176911977" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="851.480176911977" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="851.480176911977" y2="851.480176911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="875.8693769119769" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="851.480176911977" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="851.480176911977" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="875.8693769119769" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="900.2585769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="875.8693769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="875.8693769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="875.8693769119769" y2="875.8693769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="900.2585769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="875.8693769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="875.8693769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="900.2585769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="924.647776911977" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="900.2585769119769" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="900.2585769119769" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="900.2585769119769" y2="900.2585769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="924.647776911977" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="900.2585769119769" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="900.2585769119769" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="924.647776911977" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="949.0369769119769" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="924.647776911977" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="924.647776911977" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="924.647776911977" y2="924.647776911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="949.0369769119769" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="924.647776911977" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="924.647776911977" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="949.0369769119769" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="973.4261769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="949.0369769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="949.0369769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="949.0369769119769" y2="949.0369769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="973.4261769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="949.0369769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="949.0369769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="973.4261769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="997.815376911977" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="973.4261769119769" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="973.4261769119769" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="973.4261769119769" y2="973.4261769119769"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="997.815376911977" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="973.4261769119769" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="973.4261769119769" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="997.815376911977" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="549.5909090909091" y1="1022.204576911977" y2="1022.204576911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="487.0909090909091" x2="487.0909090909091" y1="997.815376911977" y2="1022.204576911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="997.815376911977" y2="1022.204576911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="997.815376911977" y2="997.815376911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="612.0909090909091" y1="1022.204576911977" y2="1022.204576911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="549.5909090909091" x2="549.5909090909091" y1="997.815376911977" y2="1022.204576911977"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="612.0909090909091" x2="612.0909090909091" y1="997.815376911977" y2="1022.204576911977"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876,729.25) scale(1,1) translate(0,0)" writing-mode="lr" x="876" xml:space="preserve" y="735.25" zvalue="634">油温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876,758) scale(1,1) translate(0,0)" writing-mode="lr" x="876" xml:space="preserve" y="764" zvalue="636">绕温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.5,703.25) scale(1,1) translate(0,0)" writing-mode="lr" x="881.5" xml:space="preserve" y="709.25" zvalue="638">档位：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1245.36,458.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1245.36" xml:space="preserve" y="465.61" zvalue="652">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248.61,531.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.61" xml:space="preserve" y="537.36" zvalue="654">3111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1247.75,379.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1247.75" xml:space="preserve" y="385.36" zvalue="658">3113</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.61,399.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.61" xml:space="preserve" y="403.86" zvalue="661">31137</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.61,481.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.61" xml:space="preserve" y="485.86" zvalue="663">31117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.61,326.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.61" xml:space="preserve" y="330.86" zvalue="666">31138</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="381" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1243.72,219.636) scale(1,1) translate(0,0)" writing-mode="tb" x="1243.72" xml:space="preserve" y="219.64" zvalue="677">祷午线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="570" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.181,687.5) scale(1,1) translate(0,0)" writing-mode="lr" x="650.1799999999999" xml:space="preserve" y="693.5" zvalue="746">0351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="569" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.75,735.25) scale(1,1) translate(0,0)" writing-mode="lr" x="747.75" xml:space="preserve" y="739.75" zvalue="750">03517</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="568" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.25,619.5) scale(1,1) translate(0,0)" writing-mode="lr" x="750.25" xml:space="preserve" y="624" zvalue="752">3117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="567" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.353,852.023) scale(1,1) translate(0,0)" writing-mode="lr" x="686.35" xml:space="preserve" y="858.52" zvalue="756">1号母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="589" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,590.5,1231.09) scale(1,1) translate(0,0)" writing-mode="lr" x="590.5" xml:space="preserve" y="1238.59" zvalue="770">分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,843.659,238.689) scale(1,1) translate(0,0)" writing-mode="lr" x="843.66" xml:space="preserve" y="244.69" zvalue="791">0312</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.591,251.545) scale(1,1) translate(0,0)" writing-mode="lr" x="955.59" xml:space="preserve" y="257.55" zvalue="798">AB相</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.69,232.705) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.69" xml:space="preserve" y="238.7" zvalue="802">0311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1435.62,245.561) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.62" xml:space="preserve" y="251.56" zvalue="804">AB相</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="543" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1503.32,1141.55) scale(1,1) translate(0,0)" writing-mode="lr" x="1503.32" xml:space="preserve" y="1146.05" zvalue="817">99138</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.2,1034.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.2" xml:space="preserve" y="1042.02" zvalue="839">10kV1号母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1295.01,1134.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1295.01" xml:space="preserve" y="1141.95" zvalue="847">903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="18" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.11,1271.73) scale(1,1) translate(0,0)" writing-mode="tb" x="1306.11" xml:space="preserve" y="1271.73" zvalue="854">进化线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.68,1215.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.68" xml:space="preserve" y="1219.95" zvalue="856">90338</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.318,1431.29) scale(1,1) translate(0,0)" writing-mode="lr" x="734.3200000000001" xml:space="preserve" y="1435.79" zvalue="862">0.501Mvar</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="243.5932825804692" y1="613.4696112262153" y2="613.4696112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="243.5932825804692" y1="651.3664112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="102.6392825804692" y1="613.4696112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="243.5932825804692" y1="613.4696112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="384.5472825804693" y1="613.4696112262153" y2="613.4696112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="384.5472825804693" y1="651.3664112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="243.5932825804692" y1="613.4696112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="384.5472825804693" x2="384.5472825804693" y1="613.4696112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="243.5932825804692" y1="651.3664112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="243.5932825804692" y1="689.2632112262153" y2="689.2632112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.6392825804692" x2="102.6392825804692" y1="651.3664112262153" y2="689.2632112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="243.5932825804692" y1="651.3664112262153" y2="689.2632112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="384.5472825804693" y1="651.3664112262153" y2="651.3664112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="384.5472825804693" y1="689.2632112262153" y2="689.2632112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="243.5932825804692" x2="243.5932825804692" y1="651.3664112262153" y2="689.2632112262153"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="384.5472825804693" x2="384.5472825804693" y1="651.3664112262153" y2="689.2632112262153"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.406,634.876) scale(1,1) translate(0,0)" writing-mode="lr" x="174.41" xml:space="preserve" y="641.38" zvalue="887">主变有功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.406,669.606) scale(1,1) translate(0,0)" writing-mode="lr" x="174.41" xml:space="preserve" y="676.11" zvalue="888">主变无功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.065,300.483) scale(1,1) translate(0,0)" writing-mode="lr" x="220.06" xml:space="preserve" y="309.98" zvalue="889">全站事故总信号</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="208" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,240.442,212.085) scale(1,1) translate(0,0)" writing-mode="lr" x="240.44" xml:space="preserve" y="218.59" zvalue="891">无人值守站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.452,1015.96) scale(1,1) translate(0,0)" writing-mode="lr" x="240.45" xml:space="preserve" y="1025.46" zvalue="893">AVC状态</text>
  <line fill="none" id="196" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="83.69348626432441" x2="402.9861990574083" y1="818.7430428567294" y2="818.7430428567294" zvalue="895"/>
  <rect fill="none" fill-opacity="0" height="40.24" id="195" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,237.915,1018.46) scale(1,1) translate(-1.01956e-13,0)" width="169.72" x="153.06" y="998.34" zvalue="896"/>
  <line fill="none" id="194" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="106.8396033205295" x2="157.3809121294855" y1="1140.272846361942" y2="1140.272846361942" zvalue="897"/>
  <line fill="none" id="193" stroke="rgb(255,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="106.8396033205295" x2="157.3809121294855" y1="1163.010915893359" y2="1163.010915893359" zvalue="898"/>
  <line fill="none" id="192" stroke="rgb(232,155,232)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="106.8396033205295" x2="157.3809121294855" y1="1185.748985424777" y2="1185.748985424777" zvalue="899"/>
  <line fill="none" id="190" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="106.8396033205295" x2="157.3809121294855" y1="1208.487054956198" y2="1208.487054956198" zvalue="900"/>
  <line fill="none" id="189" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="106.8396033205295" x2="157.3809121294855" y1="1231.225124487616" y2="1231.225124487616" zvalue="901"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.521,1139.43) scale(1,1) translate(0,0)" writing-mode="lr" x="196.52" xml:space="preserve" y="1145.43" zvalue="902">500kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.521,1162.17) scale(1,1) translate(0,0)" writing-mode="lr" x="196.52" xml:space="preserve" y="1168.17" zvalue="903">220kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.809,1184.91) scale(1,1) translate(0,0)" writing-mode="lr" x="196.81" xml:space="preserve" y="1190.91" zvalue="904">110kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193.072,1207.64) scale(1,1) translate(0,0)" writing-mode="lr" x="193.07" xml:space="preserve" y="1213.64" zvalue="905">35kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.498,1230.38) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="1236.38" zvalue="906">10kV</text>
  <rect fill="rgb(255,85,128)" fill-opacity="1" height="10.95" id="183" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.857,1140.69) scale(1,1) translate(6.42268e-13,-5.54553e-12)" width="43.8" x="262.96" y="1135.22" zvalue="907"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,356.554,1161.75) scale(1,1) translate(0,0)" writing-mode="lr" x="356.55" xml:space="preserve" y="1167.75" zvalue="908">人工变位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,355.692,1183.22) scale(1,1) translate(0,0)" writing-mode="lr" x="355.69" xml:space="preserve" y="1189.22" zvalue="909">停电状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,356.985,1204.7) scale(1,1) translate(0,0)" writing-mode="lr" x="356.98" xml:space="preserve" y="1210.7" zvalue="910">接地状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,356.123,1226.17) scale(1,1) translate(0,0)" writing-mode="lr" x="356.12" xml:space="preserve" y="1232.17" zvalue="911">检修状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.777,1282.88) scale(1,1) translate(0,0)" writing-mode="lr" x="198.78" xml:space="preserve" y="1288.88" zvalue="912">数据封锁</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.483,1304.21) scale(1,1) translate(0,0)" writing-mode="lr" x="197.48" xml:space="preserve" y="1310.21" zvalue="913">线路对端</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.968,1325.55) scale(1,1) translate(0,0)" writing-mode="lr" x="206.97" xml:space="preserve" y="1331.55" zvalue="914">数据不更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.914,1261.54) scale(1,1) translate(0,0)" writing-mode="lr" x="197.91" xml:space="preserve" y="1267.54" zvalue="915">被旁路带</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,356.985,1140.27) scale(1,1) translate(0,0)" writing-mode="lr" x="356.98" xml:space="preserve" y="1146.27" zvalue="916">事故变位</text>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.95" id="173" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.857,1164.27) scale(1,1) translate(6.42268e-13,-5.66072e-12)" width="43.8" x="262.96" y="1158.8" zvalue="917"/>
  <rect fill="rgb(0,0,255)" fill-opacity="1" height="10.95" id="172" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.857,1183.64) scale(1,1) translate(6.42268e-13,-5.75534e-12)" width="43.8" x="262.96" y="1178.17" zvalue="918"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.95" id="171" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.857,1204.7) scale(1,1) translate(6.42268e-13,-5.85818e-12)" width="43.8" x="262.96" y="1199.22" zvalue="919"/>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.95" id="170" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.857,1225.75) scale(1,1) translate(6.42268e-13,-5.96103e-12)" width="43.8" x="262.96" y="1220.28" zvalue="920"/>
  <rect fill="rgb(84,250,247)" fill-opacity="1" height="10.95" id="150" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,127.618,1280.49) scale(1,1) translate(2.58211e-13,-6.22843e-12)" width="43.8" x="105.72" y="1275.02" zvalue="921"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.95" id="149" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,127.618,1324.28) scale(1,1) translate(2.58211e-13,7.32086e-12)" width="43.8" x="105.72" y="1318.81" zvalue="922"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.95" id="148" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,127.618,1302.39) scale(1,1) translate(2.58211e-13,-6.3354e-12)" width="43.8" x="105.72" y="1296.91" zvalue="923"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.95" id="140" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,127.618,1261.96) scale(1,1) translate(2.58211e-13,-6.13793e-12)" width="43.8" x="105.72" y="1256.49" zvalue="924"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.303,1393.07) scale(1,1) translate(0,0)" writing-mode="lr" x="250.3" xml:space="preserve" y="1399.07" zvalue="930">远动通道工况</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737.127,1412) scale(1,1) translate(0,0)" writing-mode="lr" x="737.13" xml:space="preserve" y="1419.5" zvalue="939">1号电容</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.5,799) scale(1,1) translate(0,0)" writing-mode="lr" x="913.5" xml:space="preserve" y="805.5" zvalue="974">1号主变急停</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,548,1083) scale(1,1) translate(0,0)" writing-mode="lr" x="548" xml:space="preserve" y="1089" zvalue="978">10kV电压并列</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,391.735) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="397.74" zvalue="3067">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,416.266) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="422.27" zvalue="3068">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,440.796) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="446.8" zvalue="3069">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,538.917) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="544.92" zvalue="3071">U0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,465.326) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="471.33" zvalue="3072">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,489.856) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="495.86" zvalue="3073">Ubc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.363,514.387) scale(1,1) translate(0,0)" writing-mode="lr" x="517.36" xml:space="preserve" y="520.39" zvalue="3074">Uca</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,863.622) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="869.62" zvalue="3084">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,888.152) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="894.15" zvalue="3085">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,912.682) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="918.6799999999999" zvalue="3086">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,1010.8) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="1016.8" zvalue="3088">U0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,937.212) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="943.21" zvalue="3089">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,961.743) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="967.74" zvalue="3090">Ubc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.954,986.273) scale(1,1) translate(0,0)" writing-mode="lr" x="516.95" xml:space="preserve" y="992.27" zvalue="3091">Uca</text>
 </g>
 <g id="ButtonClass">
  <g href="bs_35kV祷午站_网络状态.svg"><rect fill-opacity="0" height="40.27" width="135.08" x="252.4" y="831.38" zvalue="886"/></g>
  <g href="bs_35kV祷午站_公用信号.svg"><rect fill-opacity="0" height="40.27" width="135.08" x="106.4" y="831.33" zvalue="892"/></g>
  <g href="bs_35kV祷午站_间隔索引.svg"><rect fill-opacity="0" height="40.27" width="135.08" x="105.4" y="883.1" zvalue="925"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="28.58" width="90.92" x="66" y="1417.7" zvalue="927"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="28.58" width="95.04000000000001" x="121.64" y="1417.7" zvalue="928"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="28.58" width="95.04000000000001" x="322.96" y="1411.46" zvalue="929"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="28.58" width="90.92" x="190.41" y="1417.7" zvalue="932"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="28.58" width="95.04000000000001" x="246.05" y="1417.7" zvalue="933"/></g>
  <g href="bs_35kV祷午站_状态监视.svg"><rect fill-opacity="0" height="40.27" width="135.08" x="253.4" y="882.38" zvalue="980"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv35" d="M 570.5 578.25 L 1580.5 578.25" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415935491" ObjectName="35kV1号母线"/>
   <cge:TPSR_Ref TObjectID="9288674415935491"/></metadata>
  <path d="M 570.5 578.25 L 1580.5 578.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 594.25 1054.25 L 1764.25 1054.25" stroke-width="6" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674416001027" ObjectName="10kV1号母线"/>
   <cge:TPSR_Ref TObjectID="9288674416001027"/></metadata>
  <path d="M 594.25 1054.25 L 1764.25 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="61">
   <use class="kv35" height="20" transform="rotate(0,800,458) scale(1.7,1.45) translate(-325.912,-137.638)" width="10" x="791.5" xlink:href="#Breaker:开关_0" y="443.5" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149589507" ObjectName="35kV林祷线312"/>
   <cge:TPSR_Ref TObjectID="6473925149589507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,800,458) scale(1.7,1.45) translate(-325.912,-137.638)" width="10" x="791.5" y="443.5"/></g>
  <g id="127">
   <use class="kv35" height="20" transform="rotate(0,1063,696) scale(1.7,1.45) translate(-434.206,-211.5)" width="10" x="1054.5" xlink:href="#Breaker:开关_0" y="681.5" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149655043" ObjectName="35kV1号主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473925149655043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063,696) scale(1.7,1.45) translate(-434.206,-211.5)" width="10" x="1054.5" y="681.5"/></g>
  <g id="142">
   <use class="kv10" height="20" transform="rotate(0,1608.1,1136.45) scale(1.7,1.45) translate(-658.66,-348.193)" width="10" x="1599.603481723862" xlink:href="#Breaker:开关_0" y="1121.954559152776" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149720579" ObjectName="35kV1号主变10kV侧901"/>
   <cge:TPSR_Ref TObjectID="6473925149720579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1608.1,1136.45) scale(1.7,1.45) translate(-658.66,-348.193)" width="10" x="1599.603481723862" y="1121.954559152776"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,871.013,1136.45) scale(1.7,1.45) translate(-355.152,-348.193)" width="10" x="862.5125726329532" xlink:href="#Breaker:开关_0" y="1121.954559542916" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149786115" ObjectName="10kV备用Ⅱ线907"/>
   <cge:TPSR_Ref TObjectID="6473925149786115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,871.013,1136.45) scale(1.7,1.45) translate(-355.152,-348.193)" width="10" x="862.5125726329532" y="1121.954559542916"/></g>
  <g id="78">
   <use class="kv10" height="20" transform="rotate(0,980.013,1136.45) scale(1.7,1.45) translate(-400.035,-348.193)" width="10" x="971.5125726329536" xlink:href="#Breaker:开关_0" y="1121.954559542916" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149851651" ObjectName="10kV备用Ⅰ线906"/>
   <cge:TPSR_Ref TObjectID="6473925149851651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,980.013,1136.45) scale(1.7,1.45) translate(-400.035,-348.193)" width="10" x="971.5125726329536" y="1121.954559542916"/></g>
  <g id="97">
   <use class="kv10" height="20" transform="rotate(0,1086.01,1136.45) scale(1.7,1.45) translate(-443.682,-348.193)" width="10" x="1077.512572632954" xlink:href="#Breaker:开关_0" y="1121.954559542916" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149917187" ObjectName="10kV岩桃线905"/>
   <cge:TPSR_Ref TObjectID="6473925149917187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086.01,1136.45) scale(1.7,1.45) translate(-443.682,-348.193)" width="10" x="1077.512572632954" y="1121.954559542916"/></g>
  <g id="169">
   <use class="kv10" height="20" transform="rotate(0,1202.01,1136.45) scale(1.7,1.45) translate(-491.446,-348.193)" width="10" x="1193.512572632954" xlink:href="#Breaker:开关_0" y="1121.954559542916" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925149982723" ObjectName="10kV那桃线904"/>
   <cge:TPSR_Ref TObjectID="6473925149982723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1202.01,1136.45) scale(1.7,1.45) translate(-491.446,-348.193)" width="10" x="1193.512572632954" y="1121.954559542916"/></g>
  <g id="249">
   <use class="kv10" height="20" transform="rotate(0,735.649,1136.45) scale(1.7,1.45) translate(-299.414,-348.193)" width="10" x="727.1489362693171" xlink:href="#Breaker:开关_0" y="1121.954559326172" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925150048259" ObjectName="10kV1号电容908"/>
   <cge:TPSR_Ref TObjectID="6473925150048259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,735.649,1136.45) scale(1.7,1.45) translate(-299.414,-348.193)" width="10" x="727.1489362693171" y="1121.954559326172"/></g>
  <g id="530">
   <use class="kv35" height="20" transform="rotate(0,1277.36,459.111) scale(1.7,1.45) translate(-522.472,-137.983)" width="10" x="1268.86003660373" xlink:href="#Breaker:开关_0" y="444.6111111111111" zvalue="651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925150113795" ObjectName="35kV祷午线311"/>
   <cge:TPSR_Ref TObjectID="6473925150113795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1277.36,459.111) scale(1.7,1.45) translate(-522.472,-137.983)" width="10" x="1268.86003660373" y="444.6111111111111"/></g>
  <g id="47">
   <use class="kv10" height="20" transform="rotate(0,1327.01,1136.45) scale(1.7,1.45) translate(-542.917,-348.193)" width="10" x="1318.512572632954" xlink:href="#Breaker:开关_0" y="1121.954559542916" zvalue="845"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925150179331" ObjectName="10kV进化线903"/>
   <cge:TPSR_Ref TObjectID="6473925150179331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1327.01,1136.45) scale(1.7,1.45) translate(-542.917,-348.193)" width="10" x="1318.512572632954" y="1121.954559542916"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="60">
   <use class="kv35" height="30" transform="rotate(0,800.938,537.091) scale(1.13333,1.13333) translate(-93.228,-61.1872)" width="15" x="792.4381825591411" xlink:href="#Disconnector:刀闸_0" y="520.0909090909091" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106349571" ObjectName="35kV林祷线3121"/>
   <cge:TPSR_Ref TObjectID="6192454106349571"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,800.938,537.091) scale(1.13333,1.13333) translate(-93.228,-61.1872)" width="15" x="792.4381825591411" y="520.0909090909091"/></g>
  <g id="57">
   <use class="kv35" height="30" transform="rotate(0,800.14,382) scale(1.13333,1.13333) translate(-93.1341,-42.9412)" width="15" x="791.6399633962696" xlink:href="#Disconnector:刀闸_0" y="365" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106284035" ObjectName="35kV林祷线3123"/>
   <cge:TPSR_Ref TObjectID="6192454106284035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,800.14,382) scale(1.13333,1.13333) translate(-93.1341,-42.9412)" width="15" x="791.6399633962696" y="365"/></g>
  <g id="87">
   <use class="kv35" height="30" transform="rotate(180,1455.22,634.879) scale(1.13333,-1.13333) translate(-170.202,-1193.07)" width="15" x="1446.71962594304" xlink:href="#Disconnector:令克_0" y="617.8793581676275" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110609411" ObjectName="35kV3号站变3911"/>
   <cge:TPSR_Ref TObjectID="6192454110609411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1455.22,634.879) scale(1.13333,-1.13333) translate(-170.202,-1193.07)" width="15" x="1446.71962594304" y="617.8793581676275"/></g>
  <g id="131">
   <use class="kv35" height="30" transform="rotate(0,1063.14,623) scale(1.13333,1.13333) translate(-124.075,-71.2941)" width="15" x="1054.63996339627" xlink:href="#Disconnector:刀闸_0" y="606" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106415107" ObjectName="35kV1号主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192454106415107"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1063.14,623) scale(1.13333,1.13333) translate(-124.075,-71.2941)" width="15" x="1054.63996339627" y="606"/></g>
  <g id="199">
   <use class="kv10" height="30" transform="rotate(0,1756.25,1136.02) scale(2.5977,1.88333) translate(-1068.19,-519.575)" width="15" x="1736.76724137931" xlink:href="#Disconnector:联体手车刀闸(带熔断器)_0" y="1107.772727272727" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108119043" ObjectName="10kV1号母线PT0951"/>
   <cge:TPSR_Ref TObjectID="6192454108119043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1756.25,1136.02) scale(2.5977,1.88333) translate(-1068.19,-519.575)" width="15" x="1736.76724137931" y="1107.772727272727"/></g>
  <g id="215">
   <use class="kv10" height="30" transform="rotate(0,1463.91,1110.61) scale(1.648,1.71667) translate(-570.753,-452.904)" width="15" x="1451.545454545455" xlink:href="#Disconnector:联体手车刀闸(带熔断器)_0" y="1084.863636363636" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108315651" ObjectName="10kV4号站变9911"/>
   <cge:TPSR_Ref TObjectID="6192454108315651"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1463.91,1110.61) scale(1.648,1.71667) translate(-570.753,-452.904)" width="15" x="1451.545454545455" y="1084.863636363636"/></g>
  <g id="250">
   <use class="kv10" height="30" transform="rotate(0,734.818,1291.45) scale(1.13333,1.13333) translate(-85.4492,-149.936)" width="15" x="726.3181818181818" xlink:href="#Disconnector:刀闸_0" y="1274.454545454545" zvalue="336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108708867" ObjectName="10kV1号电容9086"/>
   <cge:TPSR_Ref TObjectID="6192454108708867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,734.818,1291.45) scale(1.13333,1.13333) translate(-85.4492,-149.936)" width="15" x="726.3181818181818" y="1274.454545454545"/></g>
  <g id="419">
   <use class="kv10" height="26" transform="rotate(0,634,1138) scale(1.42857,3.42308) translate(-187.2,-774.051)" width="14" x="624" xlink:href="#Disconnector:联体手车刀闸_0" y="1093.5" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108905475" ObjectName="10kV分段9001"/>
   <cge:TPSR_Ref TObjectID="6192454108905475"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,634,1138) scale(1.42857,3.42308) translate(-187.2,-774.051)" width="14" x="624" y="1093.5"/></g>
  <g id="529">
   <use class="kv35" height="30" transform="rotate(0,1278.36,535.111) scale(1.13333,1.13333) translate(-149.395,-60.9542)" width="15" x="1269.86003660373" xlink:href="#Disconnector:刀闸_0" y="518.1111111111111" zvalue="653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109560835" ObjectName="35kV祷午线3111"/>
   <cge:TPSR_Ref TObjectID="6192454109560835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1278.36,535.111) scale(1.13333,1.13333) translate(-149.395,-60.9542)" width="15" x="1269.86003660373" y="518.1111111111111"/></g>
  <g id="519">
   <use class="kv35" height="30" transform="rotate(0,1277.5,383.111) scale(1.13333,1.13333) translate(-149.294,-43.0719)" width="15" x="1269" xlink:href="#Disconnector:刀闸_0" y="366.1111111111111" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109495299" ObjectName="35kV祷午线3113"/>
   <cge:TPSR_Ref TObjectID="6192454109495299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1277.5,383.111) scale(1.13333,1.13333) translate(-149.294,-43.0719)" width="15" x="1269" y="366.1111111111111"/></g>
  <g id="578">
   <use class="kv35" height="30" transform="rotate(0,683.681,690.25) scale(1.13333,1.13333) translate(-79.433,-79.2059)" width="15" x="675.1806803193467" xlink:href="#Disconnector:刀闸_0" y="673.25" zvalue="745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110019587" ObjectName="35kV1号母线PT0351"/>
   <cge:TPSR_Ref TObjectID="6192454110019587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,683.681,690.25) scale(1.13333,1.13333) translate(-79.433,-79.2059)" width="15" x="675.1806803193467" y="673.25"/></g>
  <g id="122">
   <use class="kv35" height="30" transform="rotate(270,844.636,254.802) scale(1.0303,-1.0303) translate(-24.615,-501.656)" width="15" x="836.909090909091" xlink:href="#Disconnector:刀闸_0" y="239.3478747268186" zvalue="790"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110085123" ObjectName="35kV林祷线0312"/>
   <cge:TPSR_Ref TObjectID="6192454110085123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,844.636,254.802) scale(1.0303,-1.0303) translate(-24.615,-501.656)" width="15" x="836.909090909091" y="239.3478747268186"/></g>
  <g id="310">
   <use class="kv35" height="30" transform="rotate(270,1324.67,248.818) scale(1.0303,-1.0303) translate(-38.7336,-489.864)" width="15" x="1316.942374127907" xlink:href="#Disconnector:刀闸_0" y="233.3636368404736" zvalue="800"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110216195" ObjectName="35kV祷午线0311"/>
   <cge:TPSR_Ref TObjectID="6192454110216195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1324.67,248.818) scale(1.0303,-1.0303) translate(-38.7336,-489.864)" width="15" x="1316.942374127907" y="233.3636368404736"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="58">
   <path class="kv35" d="M 801.04 520.65 L 801.04 472.01" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.04 520.65 L 801.04 472.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 800.21 444.11 L 800.21 398.71" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="57@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.21 444.11 L 800.21 398.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 842.42 495.88 L 801.04 495.88" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 842.42 495.88 L 801.04 495.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 800.24 365.56 L 800.24 228.85" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.24 365.56 L 800.24 228.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 861.82 283.51 L 800.24 283.51" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.82 283.51 L 800.24 283.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 842.42 342.88 L 800.24 342.88" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 842.42 342.88 L 800.24 342.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 842.42 415.88 L 800.21 415.88" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 842.42 415.88 L 800.21 415.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 1063.13 710.01 L 1063.13 746.81" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.13 710.01 L 1063.13 746.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 1063.24 578.25 L 1063.24 606.56" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.24 578.25 L 1063.24 606.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv35" d="M 1063.21 639.71 L 1063.21 682.11" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.21 639.71 L 1063.21 682.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1109.42 654.88 L 1063.21 654.88" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.42 654.88 L 1063.21 654.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1608.31 1122.56 L 1608.31 1099.84" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="144@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1608.31 1122.56 L 1608.31 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1608.23 1174.07 L 1608.23 1150.47" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="142@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1608.23 1174.07 L 1608.23 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 871.22 1122.56 L 871.22 1099.84" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="156@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.22 1122.56 L 871.22 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 871.14 1174.07 L 871.14 1150.47" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.14 1174.07 L 871.14 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 871.19 1086.28 L 871.19 1054.25" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="139@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.19 1086.28 L 871.19 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 871.11 1187.63 L 871.11 1258.63" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.11 1187.63 L 871.11 1258.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 893.6 1228.33 L 871.11 1228.33" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.6 1228.33 L 871.11 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 980.22 1122.56 L 980.22 1099.84" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.22 1122.56 L 980.22 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 980.14 1174.07 L 980.14 1150.47" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@1" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.14 1174.07 L 980.14 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 980.19 1086.28 L 980.19 1054.25" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="139@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.19 1086.28 L 980.19 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 980.11 1187.63 L 980.11 1258.63" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.11 1187.63 L 980.11 1258.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1002.6 1228.33 L 980.11 1228.33" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.6 1228.33 L 980.11 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1086.22 1122.56 L 1086.22 1099.84" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.22 1122.56 L 1086.22 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1086.14 1174.07 L 1086.14 1150.47" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.14 1174.07 L 1086.14 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1086.19 1086.28 L 1086.19 1054.25" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="139@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.19 1086.28 L 1086.19 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1086.11 1187.63 L 1086.11 1259.63" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.11 1187.63 L 1086.11 1259.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 1108.6 1228.33 L 1086.11 1228.33" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1108.6 1228.33 L 1086.11 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 1202.22 1122.56 L 1202.22 1099.84" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.22 1122.56 L 1202.22 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1202.14 1174.07 L 1202.14 1150.47" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@1" LinkObjectIDznd="169@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.14 1174.07 L 1202.14 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1202.19 1086.28 L 1202.19 1054.25" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="139@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.19 1086.28 L 1202.19 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1202.11 1187.63 L 1202.11 1262.63" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.11 1187.63 L 1202.11 1262.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1224.6 1228.33 L 1202.11 1228.33" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 1224.6 1228.33 L 1202.11 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 1756.25 1209.99 L 1756.25 1156.74" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1756.25 1209.99 L 1756.25 1156.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 1463.91 1090.44 L 1463.91 1054.25" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="139@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.91 1090.44 L 1463.91 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1463.91 1129.5 L 1463.91 1219.38" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.91 1129.5 L 1463.91 1219.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 735.86 1122.56 L 735.86 1099.84" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="248@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.86 1122.56 L 735.86 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 735.78 1174.07 L 735.78 1150.47" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="249@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.78 1174.07 L 735.78 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv10" d="M 735.75 1187.63 L 735.75 1275.02" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.75 1187.63 L 735.75 1275.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 758.24 1228.33 L 735.75 1228.33" stroke-width="1" zvalue="333"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="243" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.24 1228.33 L 735.75 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 734.89 1308.16 L 734.89 1357.2" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.89 1308.16 L 734.89 1357.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 758.24 1329.33 L 734.89 1329.33" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.24 1329.33 L 734.89 1329.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="528">
   <path class="kv35" d="M 1278.43 578.25 L 1278.43 551.82" stroke-width="1" zvalue="655"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="529@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.43 578.25 L 1278.43 551.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv35" d="M 1278.46 518.67 L 1278.46 473.12" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="529@0" LinkObjectIDznd="530@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.46 518.67 L 1278.46 473.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="518">
   <path class="kv35" d="M 1277.57 445.22 L 1277.57 399.82" stroke-width="1" zvalue="659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="530@0" LinkObjectIDznd="519@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.57 445.22 L 1277.57 399.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="515">
   <path class="kv35" d="M 1319.78 496.99 L 1278.46 496.99" stroke-width="1" zvalue="664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@0" LinkObjectIDznd="520" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.78 496.99 L 1278.46 496.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="507">
   <path class="kv35" d="M 1277.6 366.67 L 1277.6 229.96" stroke-width="1" zvalue="671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="501@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.6 366.67 L 1277.6 229.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="505">
   <path class="kv35" d="M 1321.9 280.08 L 1277.6 280.08" stroke-width="1" zvalue="673"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@0" LinkObjectIDznd="507" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.9 280.08 L 1277.6 280.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="kv35" d="M 1319.78 343.99 L 1277.6 343.99" stroke-width="1" zvalue="674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="507" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.78 343.99 L 1277.6 343.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="503">
   <path class="kv35" d="M 1319.78 416.99 L 1277.57 416.99" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="517@0" LinkObjectIDznd="518" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.78 416.99 L 1277.57 416.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="577">
   <path class="kv35" d="M 683.78 578.25 L 683.78 673.81" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@4" LinkObjectIDznd="578@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.78 578.25 L 683.78 673.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="576">
   <path class="kv35" d="M 683.75 706.96 L 683.75 779.64" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="578@1" LinkObjectIDznd="571@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.75 706.96 L 683.75 779.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv35" d="M 730.67 635.13 L 683.78 635.13" stroke-width="1" zvalue="753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="577" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.67 635.13 L 683.78 635.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv35" d="M 730.67 750.13 L 683.75 750.13" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="575@0" LinkObjectIDznd="576" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.67 750.13 L 683.75 750.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="585">
   <path class="kv10" d="M 1794.82 1118.14 L 1794.82 1094.27 L 1756.25 1094.27" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1794.82 1118.14 L 1794.82 1094.27 L 1756.25 1094.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="587">
   <path class="kv10" d="M 634.03 1176.74 L 634.03 1206 L 544.96 1206 L 544.93 1180.17" stroke-width="1" zvalue="767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.03 1176.74 L 634.03 1206 L 544.96 1206 L 544.93 1180.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv35" d="M 880.62 254.89 L 859.58 254.89" stroke-width="1" zvalue="791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.62 254.89 L 859.58 254.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 829.45 254.74 L 800.24 254.74" stroke-width="1" zvalue="792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@1" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.45 254.74 L 800.24 254.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1364.8 248.91 L 1339.61 248.91" stroke-width="1" zvalue="801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1364.8 248.91 L 1339.61 248.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv35" d="M 1309.48 248.76 L 1277.6 248.76" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@1" LinkObjectIDznd="507" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.48 248.76 L 1277.6 248.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv35" d="M 801.01 553.8 L 801.01 578.25" stroke-width="1" zvalue="807"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.01 553.8 L 801.01 578.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="542">
   <path class="kv10" d="M 1485.97 1157.29 L 1463.91 1157.29" stroke-width="1" zvalue="817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="540@0" LinkObjectIDznd="221" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.97 1157.29 L 1463.91 1157.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 735.83 1086.28 L 735.83 1054.25" stroke-width="1" zvalue="823"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.83 1086.28 L 735.83 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1455.12 725.15 L 1455.12 648.76" stroke-width="1" zvalue="829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="87@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.12 725.15 L 1455.12 648.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1455.13 619.86 L 1455.13 578.25" stroke-width="1" zvalue="835"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.13 619.86 L 1455.13 578.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1327.22 1122.56 L 1327.22 1099.84" stroke-width="1" zvalue="849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.22 1122.56 L 1327.22 1099.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 1327.14 1174.07 L 1327.14 1150.47" stroke-width="1" zvalue="850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.14 1174.07 L 1327.14 1150.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1327.19 1086.28 L 1327.19 1054.25" stroke-width="1" zvalue="851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="139@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.19 1086.28 L 1327.19 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1327.11 1187.63 L 1327.11 1263.63" stroke-width="1" zvalue="852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.11 1187.63 L 1327.11 1263.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1349.6 1228.33 L 1327.11 1228.33" stroke-width="1" zvalue="857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.6 1228.33 L 1327.11 1228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1064 838.28 L 1064 984.5 L 1669 984.5 L 1669 1268.5 L 1608.2 1268.5 L 1608.2 1187.63" stroke-width="1" zvalue="859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@2" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064 838.28 L 1064 984.5 L 1669 984.5 L 1669 1268.5 L 1608.2 1268.5 L 1608.2 1187.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 1609 1086.28 L 1609 1054.25" stroke-width="1" zvalue="860"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="139@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1609 1086.28 L 1609 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 701.03 1361.13 L 701.03 1340.5 L 734.89 1340.5" stroke-width="1" zvalue="864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.03 1361.13 L 701.03 1340.5 L 734.89 1340.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1756.25 1113.89 L 1756.25 1054.25" stroke-width="1" zvalue="869"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="139@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1756.25 1113.89 L 1756.25 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1091.35 867.51 L 1064 867.51" stroke-width="1" zvalue="870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.35 867.51 L 1064 867.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 633.98 1099.32 L 633.98 1054.25" stroke-width="1" zvalue="872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@0" LinkObjectIDznd="139@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.98 1099.32 L 633.98 1054.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="37">
   <use class="kv35" height="20" transform="rotate(270,859,416) scale(1.7,1.7) translate(-350.206,-164.294)" width="10" x="850.5" xlink:href="#GroundDisconnector:地刀_0" y="399" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106218499" ObjectName="35kV林祷线31237"/>
   <cge:TPSR_Ref TObjectID="6192454106218499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,859,416) scale(1.7,1.7) translate(-350.206,-164.294)" width="10" x="850.5" y="399"/></g>
  <g id="36">
   <use class="kv35" height="20" transform="rotate(270,859,496) scale(1.7,1.7) translate(-350.206,-197.235)" width="10" x="850.5" xlink:href="#GroundDisconnector:地刀_0" y="479" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106087427" ObjectName="35kV林祷线31217"/>
   <cge:TPSR_Ref TObjectID="6192454106087427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,859,496) scale(1.7,1.7) translate(-350.206,-197.235)" width="10" x="850.5" y="479"/></g>
  <g id="34">
   <use class="kv35" height="20" transform="rotate(270,859,343) scale(1.7,1.7) translate(-350.206,-134.235)" width="10" x="850.5" xlink:href="#GroundDisconnector:地刀_0" y="326" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454105956355" ObjectName="35kV林祷线31238"/>
   <cge:TPSR_Ref TObjectID="6192454105956355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,859,343) scale(1.7,1.7) translate(-350.206,-134.235)" width="10" x="850.5" y="326"/></g>
  <g id="135">
   <use class="kv35" height="20" transform="rotate(270,1126,655) scale(1.7,1.7) translate(-460.147,-262.706)" width="10" x="1117.5" xlink:href="#GroundDisconnector:地刀_0" y="638" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106546179" ObjectName="35kV1号主变35kV侧30117"/>
   <cge:TPSR_Ref TObjectID="6192454106546179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1126,655) scale(1.7,1.7) translate(-460.147,-262.706)" width="10" x="1117.5" y="638"/></g>
  <g id="163">
   <use class="kv10" height="20" transform="rotate(270,910.182,1228.45) scale(1.7,1.7) translate(-371.281,-498.834)" width="10" x="901.6818181818182" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559304497" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107070467" ObjectName="10kV备用Ⅱ线90738"/>
   <cge:TPSR_Ref TObjectID="6192454107070467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,910.182,1228.45) scale(1.7,1.7) translate(-371.281,-498.834)" width="10" x="901.6818181818182" y="1211.454559304497"/></g>
  <g id="70">
   <use class="kv10" height="20" transform="rotate(270,1019.18,1228.45) scale(1.7,1.7) translate(-416.163,-498.834)" width="10" x="1010.681818181818" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559304497" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107201539" ObjectName="10kV备用Ⅰ线90638"/>
   <cge:TPSR_Ref TObjectID="6192454107201539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1019.18,1228.45) scale(1.7,1.7) translate(-416.163,-498.834)" width="10" x="1010.681818181818" y="1211.454559304497"/></g>
  <g id="89">
   <use class="kv10" height="20" transform="rotate(270,1125.18,1228.45) scale(1.7,1.7) translate(-459.81,-498.834)" width="10" x="1116.681818181818" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559304497" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107529219" ObjectName="10kV岩桃线90538"/>
   <cge:TPSR_Ref TObjectID="6192454107529219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1125.18,1228.45) scale(1.7,1.7) translate(-459.81,-498.834)" width="10" x="1116.681818181818" y="1211.454559304497"/></g>
  <g id="108">
   <use class="kv10" height="20" transform="rotate(270,1241.18,1228.45) scale(1.7,1.7) translate(-507.575,-498.834)" width="10" x="1232.681818181818" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559304497" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107856899" ObjectName="10kV那桃线90438"/>
   <cge:TPSR_Ref TObjectID="6192454107856899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1241.18,1228.45) scale(1.7,1.7) translate(-507.575,-498.834)" width="10" x="1232.681818181818" y="1211.454559304497"/></g>
  <g id="206">
   <use class="kv10" height="30" transform="rotate(0,1794.82,1150.36) scale(1,2.26087) translate(0,-622.634)" width="30" x="1779.818181818181" xlink:href="#GroundDisconnector:手车式地刀_0" y="1116.446640316206" zvalue="308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108250115" ObjectName="10kV1号母线PT09518"/>
   <cge:TPSR_Ref TObjectID="6192454108250115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1794.82,1150.36) scale(1,2.26087) translate(0,-622.634)" width="30" x="1779.818181818181" y="1116.446640316206"/></g>
  <g id="241">
   <use class="kv10" height="20" transform="rotate(270,774.818,1228.45) scale(1.7,1.7) translate(-315.543,-498.834)" width="10" x="766.3181818181818" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559326172" zvalue="331"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108512259" ObjectName="10kV1号电容90838"/>
   <cge:TPSR_Ref TObjectID="6192454108512259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,774.818,1228.45) scale(1.7,1.7) translate(-315.543,-498.834)" width="10" x="766.3181818181818" y="1211.454559326172"/></g>
  <g id="256">
   <use class="kv10" height="20" transform="rotate(270,774.818,1329.45) scale(1.7,1.7) translate(-315.543,-540.422)" width="10" x="766.3181818181818" xlink:href="#GroundDisconnector:地刀_0" y="1312.454545454545" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108839939" ObjectName="10kV1号电容90868"/>
   <cge:TPSR_Ref TObjectID="6192454108839939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,774.818,1329.45) scale(1.7,1.7) translate(-315.543,-540.422)" width="10" x="766.3181818181818" y="1312.454545454545"/></g>
  <g id="517">
   <use class="kv35" height="20" transform="rotate(270,1336.36,417.111) scale(1.7,1.7) translate(-546.766,-164.752)" width="10" x="1327.86003660373" xlink:href="#GroundDisconnector:地刀_0" y="400.1111111111111" zvalue="660"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109429763" ObjectName="35kV祷午线31137"/>
   <cge:TPSR_Ref TObjectID="6192454109429763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1336.36,417.111) scale(1.7,1.7) translate(-546.766,-164.752)" width="10" x="1327.86003660373" y="400.1111111111111"/></g>
  <g id="516">
   <use class="kv35" height="20" transform="rotate(270,1336.36,497.111) scale(1.7,1.7) translate(-546.766,-197.693)" width="10" x="1327.86003660373" xlink:href="#GroundDisconnector:地刀_0" y="480.1111111111111" zvalue="662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109298691" ObjectName="35kV祷午线31117"/>
   <cge:TPSR_Ref TObjectID="6192454109298691"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1336.36,497.111) scale(1.7,1.7) translate(-546.766,-197.693)" width="10" x="1327.86003660373" y="480.1111111111111"/></g>
  <g id="512">
   <use class="kv35" height="20" transform="rotate(270,1336.36,344.111) scale(1.7,1.7) translate(-546.766,-134.693)" width="10" x="1327.86003660373" xlink:href="#GroundDisconnector:地刀_0" y="327.1111111111111" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109167619" ObjectName="35kV祷午线31138"/>
   <cge:TPSR_Ref TObjectID="6192454109167619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1336.36,344.111) scale(1.7,1.7) translate(-546.766,-134.693)" width="10" x="1327.86003660373" y="327.1111111111111"/></g>
  <g id="575">
   <use class="kv35" height="20" transform="rotate(270,747.25,750.25) scale(1.7,1.7) translate(-304.191,-301.926)" width="10" x="738.75" xlink:href="#GroundDisconnector:地刀_0" y="733.25" zvalue="749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109954051" ObjectName="35kV1号母线PT03517"/>
   <cge:TPSR_Ref TObjectID="6192454109954051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,747.25,750.25) scale(1.7,1.7) translate(-304.191,-301.926)" width="10" x="738.75" y="733.25"/></g>
  <g id="574">
   <use class="kv35" height="20" transform="rotate(270,747.25,635.25) scale(1.7,1.7) translate(-304.191,-254.574)" width="10" x="738.75" xlink:href="#GroundDisconnector:地刀_0" y="618.25" zvalue="751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109822979" ObjectName="35kV1号母线PT3117"/>
   <cge:TPSR_Ref TObjectID="6192454109822979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,747.25,635.25) scale(1.7,1.7) translate(-304.191,-254.574)" width="10" x="738.75" y="618.25"/></g>
  <g id="540">
   <use class="kv10" height="20" transform="rotate(270,1502.55,1157.41) scale(1.7,1.7) translate(-615.195,-469.58)" width="10" x="1494.045454545454" xlink:href="#GroundDisconnector:地刀_0" y="1140.409090909091" zvalue="816"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110412803" ObjectName="10kV4号站变99138"/>
   <cge:TPSR_Ref TObjectID="6192454110412803"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1502.55,1157.41) scale(1.7,1.7) translate(-615.195,-469.58)" width="10" x="1494.045454545454" y="1140.409090909091"/></g>
  <g id="28">
   <use class="kv10" height="20" transform="rotate(270,1366.18,1228.45) scale(1.7,1.7) translate(-559.045,-498.834)" width="10" x="1357.681818181818" xlink:href="#GroundDisconnector:地刀_0" y="1211.454559304497" zvalue="855"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110740483" ObjectName="10kV进化线90338"/>
   <cge:TPSR_Ref TObjectID="6192454110740483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1366.18,1228.45) scale(1.7,1.7) translate(-559.045,-498.834)" width="10" x="1357.681818181818" y="1211.454559304497"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="33">
   <use class="kv35" height="26" transform="rotate(270,874.182,283.545) scale(1,1) translate(0,0)" width="12" x="868.1818181818182" xlink:href="#Accessory:避雷器_0" y="270.5454545454545" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454105825283" ObjectName="35kV林祷线312避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,874.182,283.545) scale(1,1) translate(0,0)" width="12" x="868.1818181818182" y="270.5454545454545"/></g>
  <g id="141">
   <use class="kv10" height="26" transform="rotate(270,1105.27,867.545) scale(1,1.12587) translate(0,-95.3563)" width="12" x="1099.272727272727" xlink:href="#Accessory:避雷器_0" y="852.9090909090909" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106611715" ObjectName="35kV1号主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1105.27,867.545) scale(1,1.12587) translate(0,-95.3563)" width="12" x="1099.272727272727" y="852.9090909090909"/></g>
  <g id="203">
   <use class="kv10" height="15" transform="rotate(0,1755.52,1232.25) scale(-4.03775,3.49938) translate(-2170.56,-861.368)" width="13" x="1729.278178058113" xlink:href="#Accessory:PT8_0" y="1206" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110478339" ObjectName="10kV1号母线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1755.52,1232.25) scale(-4.03775,3.49938) translate(-2170.56,-861.368)" width="13" x="1729.278178058113" y="1206"/></g>
  <g id="509">
   <use class="kv35" height="26" transform="rotate(270,1334.27,280.111) scale(1,1) translate(0,0)" width="12" x="1328.269127512821" xlink:href="#Accessory:避雷器_0" y="267.1111111111111" zvalue="667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109036547" ObjectName="35kV祷午线311避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1334.27,280.111) scale(1,1) translate(0,0)" width="12" x="1328.269127512821" y="267.1111111111111"/></g>
  <g id="571">
   <use class="kv35" height="15" transform="rotate(0,684.534,803.669) scale(4.3582,3.77711) translate(-505.638,-570.067)" width="13" x="656.2057846745067" xlink:href="#Accessory:PT8_0" y="775.3409090909091" zvalue="755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109691907" ObjectName="35kV1号母线PT"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,684.534,803.669) scale(4.3582,3.77711) translate(-505.638,-570.067)" width="13" x="656.2057846745067" y="775.3409090909091"/></g>
  <g id="253">
   <use class="kv35" height="30" transform="rotate(0,905.818,254.893) scale(1.8,1.8) translate(-390.586,-101.286)" width="30" x="878.8181818181818" xlink:href="#Accessory:bsPT_0" y="227.8928571428571" zvalue="796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110150659" ObjectName="35kV林祷线312PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,905.818,254.893) scale(1.8,1.8) translate(-390.586,-101.286)" width="30" x="878.8181818181818" y="227.8928571428571"/></g>
  <g id="328">
   <use class="kv35" height="30" transform="rotate(0,1390,248.909) scale(1.8,1.8) translate(-605.778,-98.6261)" width="30" x="1363" xlink:href="#Accessory:bsPT_0" y="221.9086192565121" zvalue="806"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110281731" ObjectName="35kV祷午线311PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1390,248.909) scale(1.8,1.8) translate(-605.778,-98.6261)" width="30" x="1363" y="221.9086192565121"/></g>
  <g id="51">
   <use class="kv10" height="26" transform="rotate(0,701,1373.5) scale(1,1) translate(0,0)" width="12" x="695" xlink:href="#Accessory:避雷器_0" y="1360.5" zvalue="863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454111002627" ObjectName="10kV1号电容避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,701,1373.5) scale(1,1) translate(0,0)" width="12" x="695" y="1360.5"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="24">
   <use class="kv35" height="30" transform="rotate(0,800,214) scale(1,1) translate(0,0)" width="7" x="796.5" xlink:href="#ACLineSegment:线路_0" y="199" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249369083908" ObjectName="35kV林祷线_祷午"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,800,214) scale(1,1) translate(0,0)" width="7" x="796.5" y="199"/></g>
  <g id="501">
   <use class="kv35" height="30" transform="rotate(0,1277.36,215.111) scale(1,1) translate(0,0)" width="7" x="1273.86003660373" xlink:href="#ACLineSegment:线路_0" y="200.1111111111111" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249360826372" ObjectName="35kV祷午线"/>
   <cge:TPSR_Ref TObjectID="8444249360826372_5066549684862977"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1277.36,215.111) scale(1,1) translate(0,0)" width="7" x="1273.86003660373" y="200.1111111111111"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="103">
   <use class="kv35" height="30" transform="rotate(0,1455.12,747.837) scale(-2.09723,1.53805) translate(-2139.07,-253.542)" width="18" x="1436.245070038271" xlink:href="#EnergyConsumer:站用变_0" y="724.7662183509666" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110543875" ObjectName="35kV3号站变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1455.12,747.837) scale(-2.09723,1.53805) translate(-2139.07,-253.542)" width="18" x="1436.245070038271" y="724.7662183509666"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(180,871.112,1275.73) scale(0.916667,1.26667) translate(78.692,-264.574)" width="12" x="865.6116200975769" xlink:href="#EnergyConsumer:负荷_0" y="1256.727272727273" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106939395" ObjectName="10kV备用Ⅱ线"/>
   <cge:TPSR_Ref TObjectID="6192454106939395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,871.112,1275.73) scale(0.916667,1.26667) translate(78.692,-264.574)" width="12" x="865.6116200975769" y="1256.727272727273"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(180,980.112,1275.73) scale(0.916667,1.26667) translate(88.6011,-264.574)" width="12" x="974.6116200975769" xlink:href="#EnergyConsumer:负荷_0" y="1256.727272727273" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107267075" ObjectName="10kV备用Ⅰ线"/>
   <cge:TPSR_Ref TObjectID="6192454107267075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,980.112,1275.73) scale(0.916667,1.26667) translate(88.6011,-264.574)" width="12" x="974.6116200975769" y="1256.727272727273"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(180,1086.11,1276.73) scale(0.916667,1.26667) translate(98.2374,-264.785)" width="12" x="1080.611620097577" xlink:href="#EnergyConsumer:负荷_0" y="1257.727272727273" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107594755" ObjectName="10kV岩桃线"/>
   <cge:TPSR_Ref TObjectID="6192454107594755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1086.11,1276.73) scale(0.916667,1.26667) translate(98.2374,-264.785)" width="12" x="1080.611620097577" y="1257.727272727273"/></g>
  <g id="114">
   <use class="kv10" height="30" transform="rotate(180,1202.11,1279.73) scale(0.916667,1.26667) translate(108.783,-265.416)" width="12" x="1196.611620097577" xlink:href="#EnergyConsumer:负荷_0" y="1260.727272727273" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107922435" ObjectName="10kV那桃线"/>
   <cge:TPSR_Ref TObjectID="6192454107922435"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1202.11,1279.73) scale(0.916667,1.26667) translate(108.783,-265.416)" width="12" x="1196.611620097577" y="1260.727272727273"/></g>
  <g id="219">
   <use class="kv10" height="25" transform="rotate(0,1463.75,1242.54) scale(2.35,2.01429) translate(-827.378,-612.999)" width="20" x="1440.251239828267" xlink:href="#EnergyConsumer:站用变DY_0" y="1217.363636363636" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108381187" ObjectName="10kV4号站变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1463.75,1242.54) scale(2.35,2.01429) translate(-827.378,-612.999)" width="20" x="1440.251239828267" y="1217.363636363636"/></g>
  <g id="40">
   <use class="kv10" height="30" transform="rotate(180,1327.11,1280.73) scale(0.916667,1.26667) translate(120.147,-265.627)" width="12" x="1321.611620097577" xlink:href="#EnergyConsumer:负荷_0" y="1261.727272727273" zvalue="853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110806019" ObjectName="10kV进化线"/>
   <cge:TPSR_Ref TObjectID="6192454110806019"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1327.11,1280.73) scale(0.916667,1.26667) translate(120.147,-265.627)" width="12" x="1321.611620097577" y="1261.727272727273"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="123">
   <g id="1230">
    <use class="kv35" height="30" transform="rotate(0,1064,792.545) scale(3.25,3.26667) translate(-709.615,-515.929)" width="24" x="1025" xlink:href="#PowerTransformer2:可调两卷变_0" y="743.55" zvalue="145"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575446018" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1231">
    <use class="kv10" height="30" transform="rotate(0,1064,792.545) scale(3.25,3.26667) translate(-709.615,-515.929)" width="24" x="1025" xlink:href="#PowerTransformer2:可调两卷变_1" y="743.55" zvalue="145"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874575511554" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533199362" ObjectName="35kV1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399533199362"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1064,792.545) scale(3.25,3.26667) translate(-709.615,-515.929)" width="24" x="1025" y="743.55"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="144">
   <use class="kv10" height="22" transform="rotate(0,1608.99,1099.45) scale(0.772727,1.31818) translate(470.732,-261.886)" width="22" x="1600.489487315011" xlink:href="#DollyBreaker:手车_0" y="1084.954559391195" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106677251" ObjectName="35kV1号主变10kV侧901手车"/>
   <cge:TPSR_Ref TObjectID="6192454106677251"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1608.99,1099.45) scale(0.772727,1.31818) translate(470.732,-261.886)" width="22" x="1600.489487315011" y="1084.954559391195"/></g>
  <g id="145">
   <use class="kv10" height="22" transform="rotate(180,1608.19,1174.45) scale(-0.772727,1.31818) translate(-3691.88,-279.989)" width="22" x="1599.692016503497" xlink:href="#DollyBreaker:手车_0" y="1159.954559152776" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106742787" ObjectName="35kV1号主变10kV侧901手车下"/>
   <cge:TPSR_Ref TObjectID="6192454106742787"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1608.19,1174.45) scale(-0.772727,1.31818) translate(-3691.88,-279.989)" width="22" x="1599.692016503497" y="1159.954559152776"/></g>
  <g id="156">
   <use class="kv10" height="22" transform="rotate(0,871.182,1099.45) scale(0.772727,1.31818) translate(253.73,-261.886)" width="22" x="862.6818181818182" xlink:href="#DollyBreaker:手车_0" y="1084.954559304497" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106873859" ObjectName="10kV备用Ⅱ线907手车"/>
   <cge:TPSR_Ref TObjectID="6192454106873859"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,871.182,1099.45) scale(0.772727,1.31818) translate(253.73,-261.886)" width="22" x="862.6818181818182" y="1084.954559304497"/></g>
  <g id="155">
   <use class="kv10" height="22" transform="rotate(180,871.101,1174.45) scale(-0.772727,1.31818) translate(-2000.91,-279.989)" width="22" x="862.6011074125875" xlink:href="#DollyBreaker:手车_0" y="1159.954559304497" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454106808323" ObjectName="10kV备用Ⅱ线907手车下"/>
   <cge:TPSR_Ref TObjectID="6192454106808323"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,871.101,1174.45) scale(-0.772727,1.31818) translate(-2000.91,-279.989)" width="22" x="862.6011074125875" y="1159.954559304497"/></g>
  <g id="77">
   <use class="kv10" height="22" transform="rotate(0,980.182,1099.45) scale(0.772727,1.31818) translate(285.789,-261.886)" width="22" x="971.6818181818182" xlink:href="#DollyBreaker:手车_0" y="1084.954559304497" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107398147" ObjectName="10kV备用Ⅰ线906手车"/>
   <cge:TPSR_Ref TObjectID="6192454107398147"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,980.182,1099.45) scale(0.772727,1.31818) translate(285.789,-261.886)" width="22" x="971.6818181818182" y="1084.954559304497"/></g>
  <g id="76">
   <use class="kv10" height="22" transform="rotate(180,980.101,1174.45) scale(-0.772727,1.31818) translate(-2250.97,-279.989)" width="22" x="971.6011074125875" xlink:href="#DollyBreaker:手车_0" y="1159.954559304497" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107332611" ObjectName="10kV备用Ⅰ线906手车下"/>
   <cge:TPSR_Ref TObjectID="6192454107332611"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,980.101,1174.45) scale(-0.772727,1.31818) translate(-2250.97,-279.989)" width="22" x="971.6011074125875" y="1159.954559304497"/></g>
  <g id="96">
   <use class="kv10" height="22" transform="rotate(0,1086.18,1099.45) scale(0.772727,1.31818) translate(316.965,-261.886)" width="22" x="1077.681818181818" xlink:href="#DollyBreaker:手车_0" y="1084.954559304497" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107725827" ObjectName="10kV岩桃线905手车"/>
   <cge:TPSR_Ref TObjectID="6192454107725827"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1086.18,1099.45) scale(0.772727,1.31818) translate(316.965,-261.886)" width="22" x="1077.681818181818" y="1084.954559304497"/></g>
  <g id="95">
   <use class="kv10" height="22" transform="rotate(180,1086.1,1174.45) scale(-0.772727,1.31818) translate(-2494.14,-279.989)" width="22" x="1077.601107412587" xlink:href="#DollyBreaker:手车_0" y="1159.954559304497" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107660291" ObjectName="10kV岩桃线905手车下"/>
   <cge:TPSR_Ref TObjectID="6192454107660291"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1086.1,1174.45) scale(-0.772727,1.31818) translate(-2494.14,-279.989)" width="22" x="1077.601107412587" y="1159.954559304497"/></g>
  <g id="168">
   <use class="kv10" height="22" transform="rotate(0,1202.18,1099.45) scale(0.772727,1.31818) translate(351.083,-261.886)" width="22" x="1193.681818181818" xlink:href="#DollyBreaker:手车_0" y="1084.954559304497" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108053507" ObjectName="10kV那桃线904手车"/>
   <cge:TPSR_Ref TObjectID="6192454108053507"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1202.18,1099.45) scale(0.772727,1.31818) translate(351.083,-261.886)" width="22" x="1193.681818181818" y="1084.954559304497"/></g>
  <g id="167">
   <use class="kv10" height="22" transform="rotate(180,1202.1,1174.45) scale(-0.772727,1.31818) translate(-2760.26,-279.989)" width="22" x="1193.601107412587" xlink:href="#DollyBreaker:手车_0" y="1159.954559542916" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454107987971" ObjectName="10kV那桃线904手车下"/>
   <cge:TPSR_Ref TObjectID="6192454107987971"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1202.1,1174.45) scale(-0.772727,1.31818) translate(-2760.26,-279.989)" width="22" x="1193.601107412587" y="1159.954559542916"/></g>
  <g id="248">
   <use class="kv10" height="22" transform="rotate(0,735.818,1099.45) scale(0.772727,1.31818) translate(213.917,-261.886)" width="22" x="727.3181818181818" xlink:href="#DollyBreaker:手车_0" y="1084.954559326172" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108643331" ObjectName="10kV1号电容908手车"/>
   <cge:TPSR_Ref TObjectID="6192454108643331"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,735.818,1099.45) scale(0.772727,1.31818) translate(213.917,-261.886)" width="22" x="727.3181818181818" y="1084.954559326172"/></g>
  <g id="247">
   <use class="kv10" height="22" transform="rotate(180,735.737,1174.45) scale(-0.772727,1.31818) translate(-1690.37,-279.989)" width="22" x="727.237471048951" xlink:href="#DollyBreaker:手车_0" y="1159.954559326172" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454108577795" ObjectName="10kV1号电容908手车下"/>
   <cge:TPSR_Ref TObjectID="6192454108577795"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,735.737,1174.45) scale(-0.772727,1.31818) translate(-1690.37,-279.989)" width="22" x="727.237471048951" y="1159.954559326172"/></g>
  <g id="46">
   <use class="kv10" height="22" transform="rotate(0,1327.18,1099.45) scale(0.772727,1.31818) translate(387.848,-261.886)" width="22" x="1318.681818181818" xlink:href="#DollyBreaker:手车_0" y="1084.954559304497" zvalue="846"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110937091" ObjectName="10kV进化线903手车"/>
   <cge:TPSR_Ref TObjectID="6192454110937091"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1327.18,1099.45) scale(0.772727,1.31818) translate(387.848,-261.886)" width="22" x="1318.681818181818" y="1084.954559304497"/></g>
  <g id="45">
   <use class="kv10" height="22" transform="rotate(180,1327.1,1174.45) scale(-0.772727,1.31818) translate(-3047.03,-279.989)" width="22" x="1318.601107412587" xlink:href="#DollyBreaker:手车_0" y="1159.954559542916" zvalue="848"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454110871555" ObjectName="10kV进化线903手车下"/>
   <cge:TPSR_Ref TObjectID="6192454110871555"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1327.1,1174.45) scale(-0.772727,1.31818) translate(-3047.03,-279.989)" width="22" x="1318.601107412587" y="1159.954559542916"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="160">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="160" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,874.112,1329.73) scale(1,1) translate(0,0)" writing-mode="lr" x="874.11" xml:space="preserve" y="1336.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135656271874" ObjectName="P"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="161" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,874.112,1351.73) scale(1,1) translate(0,0)" writing-mode="lr" x="874.11" xml:space="preserve" y="1358.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135656337410" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="162" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,874.112,1373.73) scale(1,1) translate(0,0)" writing-mode="lr" x="874.11" xml:space="preserve" y="1380.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135656402946" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="223" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,980.112,1326.73) scale(1,1) translate(0,0)" writing-mode="lr" x="980.11" xml:space="preserve" y="1333.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135657189378" ObjectName="P"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="224" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086.11,1327.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.11" xml:space="preserve" y="1334.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135658565634" ObjectName="P"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="225" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1202.11,1330.73) scale(1,1) translate(-2.55154e-13,0)" writing-mode="lr" x="1202.11" xml:space="preserve" y="1337.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135659941890" ObjectName="P"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="227" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,980.112,1348.73) scale(1,1) translate(0,0)" writing-mode="lr" x="980.11" xml:space="preserve" y="1355.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135657254914" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="228" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1086.11,1349.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.11" xml:space="preserve" y="1356.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135658631170" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="229" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1202.11,1352.73) scale(1,1) translate(-2.55154e-13,0)" writing-mode="lr" x="1202.11" xml:space="preserve" y="1359.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135660007426" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="231" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,980.112,1370.73) scale(1,1) translate(0,0)" writing-mode="lr" x="980.11" xml:space="preserve" y="1377.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135657320450" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="232" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1086.11,1371.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.11" xml:space="preserve" y="1378.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135658696706" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="233" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1202.11,1374.73) scale(1,1) translate(-2.55154e-13,0)" writing-mode="lr" x="1202.11" xml:space="preserve" y="1381.46" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135660072962" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="260" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,582.068,392.795) scale(1,1) translate(0,0)" writing-mode="lr" x="582.0700000000001" xml:space="preserve" y="399.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135651880962" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="261" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,583.886,416.947) scale(1,1) translate(0,0)" writing-mode="lr" x="583.89" xml:space="preserve" y="423.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135651946498" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="262" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,583.886,441.098) scale(1,1) translate(0,0)" writing-mode="lr" x="583.89" xml:space="preserve" y="447.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652012034" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="263" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,582.886,513.553) scale(1,1) translate(0,0)" writing-mode="lr" x="582.89" xml:space="preserve" y="520.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652077570" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="264" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,582.886,465.25) scale(1,1) translate(0,0)" writing-mode="lr" x="582.89" xml:space="preserve" y="471.98" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652143106" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="265" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,582.886,489.402) scale(1,1) translate(0,0)" writing-mode="lr" x="582.89" xml:space="preserve" y="496.14" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652208642" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="266" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1203.5,648.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.5" xml:space="preserve" y="655.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652405250" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="267" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1203.5,670.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.5" xml:space="preserve" y="677.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652470786" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="268">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="268" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1204.14,913.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.14" xml:space="preserve" y="920.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652536322" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="269" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1204.14,935.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.14" xml:space="preserve" y="942.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652601858" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="270" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1203.5,692.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.5" xml:space="preserve" y="699.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652667394" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="271" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1204.14,957.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.14" xml:space="preserve" y="964.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652995074" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="1515">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="1515" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,2139.67,1805.06) scale(1,1) translate(-6.5383e-12,0)" writing-mode="lr" x="2139.67" xml:space="preserve" y="1811.33" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="511">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="511" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,583.886,537.705) scale(1,1) translate(0,0)" writing-mode="lr" x="583.89" xml:space="preserve" y="544.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652339714" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="521">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="521" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,583.091,863.25) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="869.98" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135654830082" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="522">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="522" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,583.091,887.417) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="894.15" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135654895618" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="523">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="523" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,583.091,911.583) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="918.3200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135654961154" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="524">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="524" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,583.091,984.083) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="990.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135655026690" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="525">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="525" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,583.091,935.75) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="942.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135655092226" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="526">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="526" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,583.091,959.917) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="966.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135655157762" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="527">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="527" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,583.091,1008.25) scale(1,1) translate(0,0)" writing-mode="lr" x="583.09" xml:space="preserve" y="1014.98" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135655288834" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="86" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1278.45,125.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.45" xml:space="preserve" y="132.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135662563330" ObjectName="P"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="98" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1278.45,149.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.45" xml:space="preserve" y="156.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135662628866" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="104" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1278.45,173.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.45" xml:space="preserve" y="180.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135662694402" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="9" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,807.773,126.909) scale(1,1) translate(0,0)" writing-mode="lr" x="807.77" xml:space="preserve" y="133.62" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135650373634" ObjectName="P"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="12" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,807.773,146.909) scale(1,1) translate(0,0)" writing-mode="lr" x="807.77" xml:space="preserve" y="153.62" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135650439170" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,807.773,166.909) scale(1,1) translate(0,0)" writing-mode="lr" x="807.77" xml:space="preserve" y="173.62" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135650504706" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="53" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1327.11,1328.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.11" xml:space="preserve" y="1335.49" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135665250306" ObjectName="P"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="54" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1327.11,1352.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.11" xml:space="preserve" y="1359.49" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135665315842" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="55" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1327.11,1376.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.11" xml:space="preserve" y="1383.49" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135665381378" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,933.5,756.545) scale(1,1) translate(0,0)" writing-mode="lr" x="933.5" xml:space="preserve" y="763.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135653257218" ObjectName="绕温"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="14" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,934.5,726.545) scale(1,1) translate(0,0)" writing-mode="lr" x="934.5" xml:space="preserve" y="733.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135652864002" ObjectName="油温1"/>
   </metadata>
  </g>
  <g id="356">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="356" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,303.061,631.53) scale(1,1) translate(8.87176e-14,6.89294e-13)" writing-mode="lr" x="303.06" xml:space="preserve" y="638.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135668920322" ObjectName="HYW2"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="355" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,304.284,671.811) scale(1,1) translate(-1.78249e-13,2.93606e-13)" writing-mode="lr" x="304.28" xml:space="preserve" y="678.5599999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135668985858" ObjectName="HYW3"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="282" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,730.71,1451.67) scale(1,1) translate(2.28887e-13,0)" writing-mode="lr" x="730.71" xml:space="preserve" y="1458.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135664070658" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="283" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,730.71,1469.67) scale(1,1) translate(2.28887e-13,0)" writing-mode="lr" x="730.71" xml:space="preserve" y="1476.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135664136194" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="4" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,936.5,701.545) scale(1,1) translate(0,0)" writing-mode="lr" x="936.5" xml:space="preserve" y="708.3" zvalue="1">ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128797274115" ObjectName="Tap"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="275">
   <use height="30" transform="rotate(0,360.321,300.483) scale(1.25,1.25) translate(-68.3141,-56.3466)" width="30" x="341.57" xlink:href="#State:红绿圆_0" y="281.73" zvalue="877"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962077843457" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,360.321,300.483) scale(1.25,1.25) translate(-68.3141,-56.3466)" width="30" x="341.57" y="281.73"/></g>
  <g id="119">
   <use height="10" transform="rotate(0,240.494,364.458) scale(6.16141,4.58011) translate(-124.04,-266.983)" width="30" x="148.07" xlink:href="#State:全站正常运行_0" y="341.56" zvalue="936"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549684862977" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,240.494,364.458) scale(6.16141,4.58011) translate(-124.04,-266.983)" width="30" x="148.07" y="341.56"/></g>
  <g id="19">
   <use height="14" transform="rotate(0,238.246,432.164) scale(5.81762,3.12927) translate(-122.62,-279.155)" width="31" x="148.07" xlink:href="#State:正常供电_0" y="410.26" zvalue="937"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762603576524803" ObjectName=""/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,238.246,432.164) scale(5.81762,3.12927) translate(-122.62,-279.155)" width="31" x="148.07" y="410.26"/></g>
  <g id="648">
   <use height="30" transform="rotate(0,989,799) scale(1,1) translate(0,0)" width="30" x="974" xlink:href="#State:红绿圆_0" y="784" zvalue="973"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,989,799) scale(1,1) translate(0,0)" width="30" x="974" y="784"/></g>
  <g id="321">
   <use height="30" transform="rotate(0,549,1054.5) scale(1,1) translate(0,0)" width="30" x="534" xlink:href="#State:红绿圆_0" y="1039.5" zvalue="976"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762603439620100" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,549,1054.5) scale(1,1) translate(0,0)" width="30" x="534" y="1039.5"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="CompensatorClass">
  <g id="278">
   <use class="kv10" height="20" transform="rotate(0,734.96,1373.83) scale(1.75,1.75) translate(-311.233,-581.286)" width="10" x="726.2104181655017" xlink:href="#Compensator:并联电容器接地_0" y="1356.333333333333" zvalue="938"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454109626371" ObjectName="10kV1号电容"/>
   <cge:TPSR_Ref TObjectID="6192454109626371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,734.96,1373.83) scale(1.75,1.75) translate(-311.233,-581.286)" width="10" x="726.2104181655017" y="1356.333333333333"/></g>
 </g>
</svg>