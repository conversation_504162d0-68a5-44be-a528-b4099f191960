<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685125121" height="1500" id="thSvg" source="NR-PCS9000" viewBox="0 0 1600 1500" width="1600">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,0,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(255,170,199);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(227,227,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(85,170,0);fill:none}
.kv6{stroke:rgb(85,170,0);fill:none}
.v400{stroke:rgb(255,85,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="11.13292181069959"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="6.083333333333336" y2="11.13582329690123"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="11.13240401999107" y2="15.91666666666666"/>
   <line fill="none" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="11.13518747193215" y2="15.91666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 42.0733 L 19.9167 42.0733 L 15.0833 34.0833 z" fill-opacity="0" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10.04) scale(1,1) translate(0,0)" width="9.5" x="0.28" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.123107692307695" xlink:href="#terminal" y="0.4201734750979291"/>
   <use terminal-index="1" type="0" x="5.075630769230772" xlink:href="#terminal" y="19.66315985823541"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.08333333333333304" x2="9.75" y1="0.6666666666666679" y2="19.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="0.25" y1="0.8333333333333357" y2="19.66666666666666"/>
   <rect fill-opacity="0" height="19.33" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.03,10) scale(1,1) translate(0,0)" width="9.67" x="0.2" y="0.33"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.4661605870522134" x2="7.700506079614452" y1="7.128423176033875" y2="24.03824349063279"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333334" x2="8.85" y1="3.755662181544974" y2="3.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="3.583333333333332" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.666666666666666" y2="13.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.5" x2="1.5" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.5" x2="8.75" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(0,170,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:bsPT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="1" xlink:href="#terminal" y="15"/>
   <ellipse cx="17.78" cy="15.05" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.38" cy="15.05" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,8.08,15) scale(1,1) translate(0,0)" width="4.92" x="5.62" y="11.29"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.7916666666666625" x2="12.79166666666666" y1="14.99166666666667" y2="14.99166666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器接地PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.61666666666667" xlink:href="#terminal" y="0.833333333333325"/>
   <rect fill-opacity="0" height="3.75" stroke="rgb(131,131,131)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,5.13) scale(1,1) translate(0,0)" width="2" x="11.5" y="3.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="12.25" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="6.5" y1="12.25" y2="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="0.8837500000000009" y2="9.583333333333332"/>
   <ellipse cx="12.77" cy="12.43" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.79" cy="16.82" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="16.18" cy="14.77" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.33476930069099" x2="12.58512928985352" y1="12.87741362107401" y2="12.16693309997056"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.81552308018022" x2="12.58226096948206" y1="12.86093130501353" y2="12.16693309997056"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58331418086846" x2="12.58331418086846" y1="12.16867403047354" y2="11.22918201502638"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.5677745356137" x2="13.53146295417303" y1="17.00139048345364" y2="17.88180390923548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.56634037542796" x2="11.66916113548238" y1="17.00283749062492" y2="17.89828622529594"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.56764008309627" x2="12.56764008309627" y1="16.15116072288544" y2="17.00139048345364"/>
   <path d="M 17.667 14.5427 L 16.0675 13.574 L 16.102 15.5503 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.666666666666666" x2="7.666666666666665" y1="16.91666666666666" y2="16.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.166666666666666" x2="8.166666666666664" y1="15.91666666666666" y2="15.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.166666666666665" x2="7.166666666666665" y1="17.91666666666666" y2="17.91666666666666"/>
  </symbol>
  <symbol id=":bs电缆_0" viewBox="0,0,30,30">
   <path d="M 10.9166 4.25 L 19.25 4.25 L 15.0833 8.09722 L 10.9166 4.25 z" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08327650869417" x2="15.08327650869417" y1="8.097222222222227" y2="23.48611111111108"/>
   <path d="M 10.8333 27.3333 L 19.1668 27.3333 L 15.0001 23.4861 L 10.8333 27.3333" fill="none" stroke="rgb(255,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id=":母线前景_0" viewBox="0,0,40,50">
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="25.05000000190735" y2="25.05000000190735"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="31.03333356348674" y2="31.03333356348674"/>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="9.999999198913549">Ua</text>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="37.01666712506614" y2="37.01666712506614"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="43.10000069046023" y2="43.10000069046023"/>
   <rect fill-opacity="0" height="48" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,25) scale(1,1) translate(0,0)" width="38.4" x="0.8" y="1"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="0.9999990844726163" y2="49.10000091934208"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="19.06666644032795" y2="19.06666644032795"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927973" x2="39.3000007362366" y1="6.999999313354472" y2="6.999999313354472"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.8999992713927938" x2="39.3000007362366" y1="13.08333287874856" y2="13.08333287874856"/>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="40.0857146323068">Uca</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="46.04285771669664">U0</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="34.07857154600962">Ubc</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="28.17142846352714">Uab</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="22.11428537532261">Uc</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378905" xml:space="preserve" y="16.20714229284012">Ub</text>
   <text fill="rgb(255,255,255)" font-family="Helvetica" font-size="21" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="10.39999963378906" xml:space="preserve" y="51.85000079536441">F</text>
  </symbol>
  <symbol id="State:前置通道状态_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:前置通道状态_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(85,255,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_2" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_3" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(80,80,80)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.88,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.08" y="0.17"/>
  </symbol>
  <symbol id="State:前置通道状态_4" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,0,255)" fill-opacity="1" height="29.5" rx="0" ry="0" stroke="rgb(0,138,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15,14.92) scale(1,1) translate(0,0)" width="29.58" x="0.21" y="0.17"/>
   <ellipse Plane="0" cx="15" cy="14.92" fill="rgb(255,255,255)" fill-opacity="1" rx="7.09" ry="7.09" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:正常供电_0" viewBox="0,0,31,14">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="12.83" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.5,6.67) scale(1,1) translate(0,0)" width="30.5" x="0.25" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.5" xml:space="preserve" y="9.166666666666668">   正常供电   </text>
  </symbol>
  <symbol id="State:正常供电_1" viewBox="0,0,31,14">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="13.33" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,15.42,7) scale(1,1) translate(0,0)" width="30.5" x="0.17" y="0.33"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="15.41666666666667" xml:space="preserve" y="9.5"> 全站保供电 </text>
  </symbol>
  <symbol id="State:全站正常运行_0" viewBox="0,0,30,10">
   <rect fill="rgb(0,170,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(0,170,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警启用</text>
  </symbol>
  <symbol id="State:全站正常运行_1" viewBox="0,0,30,10">
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="9.42" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,14.96,4.96) scale(1,1) translate(0,0)" width="29.08" x="0.42" y="0.25"/>
   <text Plane="0" fill="rgb(255,0,0)" fill-opacity="1" font-family="Helvetica" font-size="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="14.95833333333333" xml:space="preserve" y="6.958333333333334">失压告警退出</text>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV福桥站" InitShowingPlane="0" fill="rgb(0,0,0)" height="1500" width="1600" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.137,51.9346) scale(1,1) translate(0,0)" writing-mode="lr" x="225.14" xml:space="preserve" y="56.43" zvalue="812"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,263.051,52.564) scale(1,1) translate(0,0)" writing-mode="lr" x="263.05" xml:space="preserve" y="61.56" zvalue="813">35kV福桥站</text>
  <rect fill="none" fill-opacity="0" height="1328" id="161" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,226.269,685.25) scale(1,1) translate(-1.42765e-14,0)" width="323.95" x="64.3" y="21.25" zvalue="815"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.71" id="160" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,301.551,734.813) scale(1,1) translate(1.03692e-13,0)" width="136.12" x="233.49" y="715.96" zvalue="816"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,301.551,734.813) scale(1,1) translate(0,0)" writing-mode="lr" x="301.55" xml:space="preserve" y="744.3099999999999" zvalue="816">网络状态</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.71" id="154" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,150.551,734.139) scale(1,1) translate(7.32683e-14,0)" width="136.12" x="82.48999999999999" y="715.29" zvalue="823"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,150.551,734.139) scale(1,1) translate(0,0)" writing-mode="lr" x="150.55" xml:space="preserve" y="743.64" zvalue="823">公用信号</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.71" id="56" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,149.551,785.061) scale(1,1) translate(7.23802e-14,1.70132e-13)" width="136.12" x="81.48999999999999" y="766.21" zvalue="858"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,149.551,785.061) scale(1,1) translate(0,0)" writing-mode="lr" x="149.55" xml:space="preserve" y="794.5599999999999" zvalue="858">间隔索引</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="387.7108231820695" y1="1217.8242819293" y2="1217.8242819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="387.7108231820695" y1="1254.284581929301" y2="1254.284581929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="62.37682318206953" y1="1217.8242819293" y2="1254.284581929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.7108231820695" x2="387.7108231820695" y1="1217.8242819293" y2="1254.284581929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="119.7275231820695" y1="1254.284481929301" y2="1254.284481929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="119.7275231820695" y1="1299.699681929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="62.37682318206953" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7275231820695" x2="119.7275231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="177.0795231820696" y1="1254.284481929301" y2="1254.284481929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="177.0795231820696" y1="1299.699681929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="119.7288231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0795231820696" x2="177.0795231820696" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="242.5631231820695" y1="1254.284481929301" y2="1254.284481929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="242.5631231820695" y1="1299.699681929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="177.0791231820696" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5631231820695" x2="242.5631231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="308.0472231820695" y1="1254.284481929301" y2="1254.284481929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="308.0472231820695" y1="1299.699681929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="242.5632231820696" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0472231820695" x2="308.0472231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="387.7126231820695" y1="1254.284481929301" y2="1254.284481929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="387.7126231820695" y1="1299.699681929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="308.0473231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.7126231820695" x2="387.7126231820695" y1="1254.284481929301" y2="1299.699681929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="119.7275231820695" y1="1299.699781929301" y2="1299.699781929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="119.7275231820695" y1="1348.8496819293" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="62.37682318206953" x2="62.37682318206953" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7275231820695" x2="119.7275231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="177.0795231820696" y1="1299.699781929301" y2="1299.699781929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="177.0795231820696" y1="1348.8496819293" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.7288231820695" x2="119.7288231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0795231820696" x2="177.0795231820696" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="242.5631231820695" y1="1299.699781929301" y2="1299.699781929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="242.5631231820695" y1="1348.8496819293" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="177.0791231820696" x2="177.0791231820696" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5631231820695" x2="242.5631231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="308.0472231820695" y1="1299.699781929301" y2="1299.699781929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="308.0472231820695" y1="1348.8496819293" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="242.5632231820696" x2="242.5632231820696" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0472231820695" x2="308.0472231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="387.7126231820695" y1="1299.699781929301" y2="1299.699781929301"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="387.7126231820695" y1="1348.8496819293" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="308.0473231820695" x2="308.0473231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="387.7126231820695" x2="387.7126231820695" y1="1299.699781929301" y2="1348.8496819293"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.0606,1275.56) scale(1,1) translate(0,0)" writing-mode="lr" x="93.06" xml:space="preserve" y="1281.56" zvalue="860">数１</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151.199,1275.56) scale(1,1) translate(0,0)" writing-mode="lr" x="151.2" xml:space="preserve" y="1281.56" zvalue="861">数２</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,349.367,1277.71) scale(1,1) translate(0,0)" writing-mode="lr" x="349.37" xml:space="preserve" y="1283.21" zvalue="862">值班节点</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.429,1275.56) scale(1,1) translate(0,0)" writing-mode="lr" x="218.43" xml:space="preserve" y="1281.56" zvalue="865">专１</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,276.567,1275.56) scale(1,1) translate(0,0)" writing-mode="lr" x="276.57" xml:space="preserve" y="1281.56" zvalue="866">专2</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="37.71" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,300.551,785.813) scale(1,1) translate(1.03248e-13,-1.70299e-13)" width="136.12" x="232.49" y="766.96" zvalue="884"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.551,785.813) scale(1,1) translate(0,0)" writing-mode="lr" x="300.55" xml:space="preserve" y="795.3099999999999" zvalue="884">状态监视</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.5,505.167) scale(1,1) translate(0,0)" writing-mode="lr" x="587.5" xml:space="preserve" y="511.67" zvalue="2">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="32" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.63,770) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.63" xml:space="preserve" y="781.5" zvalue="4">#4主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.36,636) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.36" xml:space="preserve" y="643.5" zvalue="7">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.61,584.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.61" xml:space="preserve" y="590.11" zvalue="13">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.11,797.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.11" xml:space="preserve" y="803.11" zvalue="310">S9-6300/35</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.61,817.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.61" xml:space="preserve" y="823.11" zvalue="311">38.5±5%/6.3kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.61,838.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.61" xml:space="preserve" y="844.11" zvalue="312">Yd11</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.231,100.202) scale(1,1) translate(0,0)" writing-mode="lr" x="230.23" xml:space="preserve" y="106.7" zvalue="596">图纸版本：福桥站2019-2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.11,693.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.11" xml:space="preserve" y="699.11" zvalue="716">3014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.43,383.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.43" xml:space="preserve" y="391.17" zvalue="720">303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.85,458.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1175.85" xml:space="preserve" y="464.67" zvalue="722">3031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.84,305.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.84" xml:space="preserve" y="311.78" zvalue="725">3033</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="41" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1118.56,145.444) scale(1,1) translate(0,0)" writing-mode="tb" x="1118.56" xml:space="preserve" y="145.44" zvalue="737">福靖线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.61,262.504) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.61" xml:space="preserve" y="269" zvalue="742">BC相</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.653,813.056) scale(1,1) translate(0,0)" writing-mode="lr" x="759.65" xml:space="preserve" y="819.5599999999999" zvalue="752">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.056,597.889) scale(1,1) translate(0,0)" writing-mode="lr" x="784.0599999999999" xml:space="preserve" y="603.89" zvalue="753">0351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.62,1040.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.62" xml:space="preserve" y="1047.61" zvalue="760">608</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.44,963.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.44" xml:space="preserve" y="969.89" zvalue="761">6084</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.56,1128.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.56" xml:space="preserve" y="1134.33" zvalue="762">6081</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.333,1068.22) scale(1,1) translate(0,0)" writing-mode="lr" x="921.33" xml:space="preserve" y="1073.72" zvalue="763">60817</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.11,1270.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.11" xml:space="preserve" y="1276.94" zvalue="769">110kV福禄河四级水电站6.3kVⅠ母</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="226.206380601041" y1="504.9069374241215" y2="504.9069374241215"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="226.206380601041" y1="540.3938374241216" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="84.17038060104096" y1="504.9069374241215" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="226.206380601041" y1="504.9069374241215" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="368.242380601041" y1="504.9069374241215" y2="504.9069374241215"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="368.242380601041" y1="540.3938374241216" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="226.206380601041" y1="504.9069374241215" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.242380601041" x2="368.242380601041" y1="504.9069374241215" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="226.206380601041" y1="540.3938374241216" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="226.206380601041" y1="575.8807374241214" y2="575.8807374241214"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="84.17038060104096" x2="84.17038060104096" y1="540.3938374241216" y2="575.8807374241214"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="226.206380601041" y1="540.3938374241216" y2="575.8807374241214"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="368.242380601041" y1="540.3938374241216" y2="540.3938374241216"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="368.242380601041" y1="575.8807374241214" y2="575.8807374241214"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="226.206380601041" x2="226.206380601041" y1="540.3938374241216" y2="575.8807374241214"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.242380601041" x2="368.242380601041" y1="540.3938374241216" y2="575.8807374241214"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.488,524.698) scale(1,1) translate(0,0)" writing-mode="lr" x="156.49" xml:space="preserve" y="531.2" zvalue="817">主变有功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.488,557.219) scale(1,1) translate(0,0)" writing-mode="lr" x="156.49" xml:space="preserve" y="563.72" zvalue="818">主变无功总加:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.262,216.008) scale(1,1) translate(0,0)" writing-mode="lr" x="194.26" xml:space="preserve" y="225.51" zvalue="819">全站事故总信号</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="155" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,218.913,133.231) scale(1,1) translate(0,0)" writing-mode="lr" x="218.91" xml:space="preserve" y="139.73" zvalue="822">无人值守站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.041,885.985) scale(1,1) translate(0,0)" writing-mode="lr" x="223.04" xml:space="preserve" y="895.49" zvalue="824">AVC状态</text>
  <line fill="none" id="151" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="65.07929070327964" x2="386.8227001582468" y1="701.3114339776347" y2="701.3114339776347" zvalue="826"/>
  <rect fill="none" fill-opacity="0" height="37.68" id="150" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,220.485,888.326) scale(1,1) translate(-2.99702e-14,-1.93065e-13)" width="171.02" x="134.97" y="869.49" zvalue="827"/>
  <line fill="none" id="149" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="76.31095857467039" x2="127.2401917296027" y1="1002.395239721489" y2="1002.395239721489" zvalue="829"/>
  <line fill="none" id="148" stroke="rgb(255,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="76.31095857467039" x2="127.2401917296027" y1="1023.687401241679" y2="1023.687401241679" zvalue="830"/>
  <line fill="none" id="147" stroke="rgb(232,155,232)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="76.31095857467039" x2="127.2401917296027" y1="1044.979562761867" y2="1044.979562761867" zvalue="831"/>
  <line fill="none" id="146" stroke="rgb(227,227,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="76.31095857467039" x2="127.2401917296027" y1="1066.271724282059" y2="1066.271724282059" zvalue="832"/>
  <line fill="none" id="145" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" x1="76.31095857467039" x2="127.2401917296027" y1="1087.563885802249" y2="1087.563885802249" zvalue="833"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.681,1001.61) scale(1,1) translate(0,0)" writing-mode="lr" x="166.68" xml:space="preserve" y="1007.61" zvalue="834">500kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.681,1022.9) scale(1,1) translate(0,0)" writing-mode="lr" x="166.68" xml:space="preserve" y="1028.9" zvalue="835">220kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.971,1044.19) scale(1,1) translate(0,0)" writing-mode="lr" x="166.97" xml:space="preserve" y="1050.19" zvalue="836">110kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,163.206,1065.48) scale(1,1) translate(0,0)" writing-mode="lr" x="163.21" xml:space="preserve" y="1071.48" zvalue="837">35kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,162.626,1086.78) scale(1,1) translate(0,0)" writing-mode="lr" x="162.63" xml:space="preserve" y="1092.78" zvalue="838">10kV</text>
  <rect fill="rgb(255,85,128)" fill-opacity="1" height="10.25" id="130" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,255.695,1002.79) scale(1,1) translate(-3.63127e-13,-1.10763e-12)" width="44.14" x="233.63" y="997.66" zvalue="839"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.942,1022.5) scale(1,1) translate(0,0)" writing-mode="lr" x="327.94" xml:space="preserve" y="1028.5" zvalue="840">人工变位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.073,1042.61) scale(1,1) translate(0,0)" writing-mode="lr" x="327.07" xml:space="preserve" y="1048.61" zvalue="841">停电状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.376,1062.72) scale(1,1) translate(0,0)" writing-mode="lr" x="328.38" xml:space="preserve" y="1068.72" zvalue="842">接地状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.507,1082.83) scale(1,1) translate(0,0)" writing-mode="lr" x="327.51" xml:space="preserve" y="1088.83" zvalue="843">检修状态</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,168.954,1135.93) scale(1,1) translate(0,0)" writing-mode="lr" x="168.95" xml:space="preserve" y="1141.93" zvalue="844">数据封锁</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.65,1155.91) scale(1,1) translate(0,0)" writing-mode="lr" x="167.65" xml:space="preserve" y="1161.91" zvalue="845">线路对端</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.208,1175.89) scale(1,1) translate(0,0)" writing-mode="lr" x="177.21" xml:space="preserve" y="1181.89" zvalue="846">数据不更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,168.085,1115.95) scale(1,1) translate(0,0)" writing-mode="lr" x="168.08" xml:space="preserve" y="1121.95" zvalue="847">被旁路带</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.376,1002.4) scale(1,1) translate(0,0)" writing-mode="lr" x="328.38" xml:space="preserve" y="1008.4" zvalue="848">事故变位</text>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.25" id="68" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,255.695,1024.87) scale(1,1) translate(-3.63127e-13,1.585e-12)" width="44.14" x="233.63" y="1019.74" zvalue="849"/>
  <rect fill="rgb(0,0,255)" fill-opacity="1" height="10.25" id="67" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,255.695,1043.01) scale(1,1) translate(-3.63127e-13,1.61319e-12)" width="44.14" x="233.63" y="1037.88" zvalue="850"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.25" id="65" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,255.695,1062.72) scale(1,1) translate(-3.63127e-13,1.64384e-12)" width="44.14" x="233.63" y="1057.6" zvalue="851"/>
  <rect fill="rgb(255,170,0)" fill-opacity="1" height="10.25" id="64" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,255.695,1082.44) scale(1,1) translate(-3.63127e-13,1.67448e-12)" width="44.14" x="233.63" y="1077.31" zvalue="852"/>
  <rect fill="rgb(84,250,247)" fill-opacity="1" height="10.25" id="63" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,97.2485,1133.7) scale(1,1) translate(-1.16852e-13,1.75415e-12)" width="44.14" x="75.18000000000001" y="1128.57" zvalue="853"/>
  <rect fill="rgb(170,170,127)" fill-opacity="1" height="10.25" id="62" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,97.2485,1174.7) scale(1,1) translate(-1.16852e-13,1.81789e-12)" width="44.14" x="75.18000000000001" y="1169.58" zvalue="854"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.25" id="61" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,97.2485,1154.2) scale(1,1) translate(-1.16852e-13,1.78602e-12)" width="44.14" x="75.18000000000001" y="1149.07" zvalue="855"/>
  <rect fill="rgb(0,255,0)" fill-opacity="1" height="10.25" id="58" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,97.2485,1116.35) scale(1,1) translate(-1.16852e-13,1.72719e-12)" width="44.14" x="75.18000000000001" y="1111.22" zvalue="856"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.968,1239.12) scale(1,1) translate(0,0)" writing-mode="lr" x="232.97" xml:space="preserve" y="1245.12" zvalue="863">远动通道工况</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.753,773) scale(1,1) translate(0,0)" writing-mode="lr" x="858.75" xml:space="preserve" y="779" zvalue="891">油温1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.194,802.222) scale(1,1) translate(0,0)" writing-mode="lr" x="857.1900000000001" xml:space="preserve" y="808.22" zvalue="892">绕温:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.556,825.222) scale(1,1) translate(0,0)" writing-mode="lr" x="863.5599999999999" xml:space="preserve" y="831.22" zvalue="893">档位：</text>
 </g>
 <g id="ButtonClass">
  <g href="bs_35kV福桥站_网络状态.svg"><rect fill-opacity="0" height="37.71" width="136.12" x="233.49" y="715.96" zvalue="816"/></g>
  <g href="bs_35kV福桥站_公用信号.svg"><rect fill-opacity="0" height="37.71" width="136.12" x="82.48999999999999" y="715.29" zvalue="823"/></g>
  <g href="bs_35kV福桥站_间隔索引.svg"><rect fill-opacity="0" height="37.71" width="136.12" x="81.48999999999999" y="766.21" zvalue="858"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="26.76" width="91.62" x="47.25" y="1262.18" zvalue="860"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="26.76" width="95.77" x="103.32" y="1262.18" zvalue="861"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="26.76" width="95.77" x="301.48" y="1264.33" zvalue="862"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="26.76" width="91.62" x="172.62" y="1262.18" zvalue="865"/></g>
  <g href="bs_来宾110kV象州站.svg"><rect fill-opacity="0" height="26.76" width="95.77" x="228.68" y="1262.18" zvalue="866"/></g>
  <g href="bs_35kV福桥站_状态监视.svg"><rect fill-opacity="0" height="37.71" width="136.12" x="232.49" y="766.96" zvalue="884"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv35" d="M 541.44 525.56 L 1306.44 525.56" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674416656387" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674416656387"/></metadata>
  <path d="M 541.44 525.56 L 1306.44 525.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv6" d="M 508.89 1238.89 L 1396.67 1238.89" stroke-width="6" zvalue="768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674416721923" ObjectName="110kV福禄河四级水电站6.3kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674416721923"/></metadata>
  <path d="M 508.89 1238.89 L 1396.67 1238.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="3">
   <g id="30">
    <use class="kv35" height="50" transform="rotate(0,1005.71,805) scale(1.8,1.8) translate(-434.982,-337.778)" width="30" x="978.71" xlink:href="#PowerTransformer2:Y-D_0" y="760" zvalue="3"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874576363522" ObjectName="35"/>
    </metadata>
   </g>
   <g id="31">
    <use class="kv6" height="50" transform="rotate(0,1005.71,805) scale(1.8,1.8) translate(-434.982,-337.778)" width="30" x="978.71" xlink:href="#PowerTransformer2:Y-D_1" y="760" zvalue="3"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874576429058" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533658114" ObjectName="35kV#4主变"/>
   <cge:TPSR_Ref TObjectID="6755399533658114"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1005.71,805) scale(1.8,1.8) translate(-434.982,-337.778)" width="30" x="978.71" y="760"/></g>
 </g>
 <g id="BreakerClass">
  <g id="6">
   <use class="kv35" height="20" transform="rotate(0,1005.36,636) scale(1.7,1.45) translate(-410.472,-192.879)" width="10" x="996.8600366037304" xlink:href="#Breaker:开关_0" y="621.5" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925153390595" ObjectName="35kV#4主变35kV侧301"/>
   <cge:TPSR_Ref TObjectID="6473925153390595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.36,636) scale(1.7,1.45) translate(-410.472,-192.879)" width="10" x="996.8600366037304" y="621.5"/></g>
  <g id="80">
   <use class="kv35" height="20" transform="rotate(0,1147.21,383.556) scale(1.7,1.45) translate(-468.879,-114.534)" width="10" x="1138.705000390532" xlink:href="#Breaker:开关_0" y="369.0555555555554" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925153456131" ObjectName="35kV福靖线303"/>
   <cge:TPSR_Ref TObjectID="6473925153456131"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1147.21,383.556) scale(1.7,1.45) translate(-468.879,-114.534)" width="10" x="1138.705000390532" y="369.0555555555554"/></g>
  <g id="102">
   <use class="kv6" height="20" transform="rotate(0,1004.29,1042.44) scale(1.88889,1.61111) translate(-468.162,-389.299)" width="10" x="994.8444835257985" xlink:href="#Breaker:开关_0" y="1026.333333333333" zvalue="759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925153521667" ObjectName="35kV#4主变6.3kV侧608"/>
   <cge:TPSR_Ref TObjectID="6473925153521667"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1004.29,1042.44) scale(1.88889,1.61111) translate(-468.162,-389.299)" width="10" x="994.8444835257985" y="1026.333333333333"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="12">
   <use class="kv35" height="30" transform="rotate(0,1005.5,585) scale(1.13333,1.13333) translate(-117.294,-66.8235)" width="15" x="997.0000000000001" xlink:href="#Disconnector:刀闸_0" y="568" zvalue="12"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128893955" ObjectName="35kV#4主变35kV侧3011"/>
   <cge:TPSR_Ref TObjectID="6192454128893955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.5,585) scale(1.13333,1.13333) translate(-117.294,-66.8235)" width="15" x="997.0000000000001" y="568"/></g>
  <g id="18">
   <use class="kv35" height="30" transform="rotate(0,1005.5,695) scale(1.13333,1.13333) translate(-117.294,-79.7647)" width="15" x="997" xlink:href="#Disconnector:刀闸_0" y="678" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454128959491" ObjectName="35kV#4主变35kV侧3014"/>
   <cge:TPSR_Ref TObjectID="6192454128959491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.5,695) scale(1.13333,1.13333) translate(-117.294,-79.7647)" width="15" x="997" y="678"/></g>
  <g id="79">
   <use class="kv35" height="30" transform="rotate(0,1147.23,458.556) scale(1.13333,1.13333) translate(-133.969,-51.9477)" width="15" x="1138.734092040582" xlink:href="#Disconnector:刀闸_0" y="441.5555555555554" zvalue="721"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129287171" ObjectName="35kV福靖线3031"/>
   <cge:TPSR_Ref TObjectID="6192454129287171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1147.23,458.556) scale(1.13333,1.13333) translate(-133.969,-51.9477)" width="15" x="1138.734092040582" y="441.5555555555554"/></g>
  <g id="77">
   <use class="kv35" height="30" transform="rotate(0,1147.34,304.556) scale(1.13333,1.13333) translate(-133.982,-33.8301)" width="15" x="1138.844963786802" xlink:href="#Disconnector:刀闸_0" y="287.5555555555554" zvalue="724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129221635" ObjectName="35kV福靖线3033"/>
   <cge:TPSR_Ref TObjectID="6192454129221635"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1147.34,304.556) scale(1.13333,1.13333) translate(-133.982,-33.8301)" width="15" x="1138.844963786802" y="287.5555555555554"/></g>
  <g id="86">
   <use class="kv35" height="30" transform="rotate(0,758.611,598.889) scale(1.25926,1.25926) translate(-154.24,-119.412)" width="15" x="749.1666662825478" xlink:href="#Disconnector:刀闸_0" y="580" zvalue="752"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129418243" ObjectName="35kV母线PT0351"/>
   <cge:TPSR_Ref TObjectID="6192454129418243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,758.611,598.889) scale(1.25926,1.25926) translate(-154.24,-119.412)" width="15" x="749.1666662825478" y="580"/></g>
  <g id="106">
   <use class="kv6" height="30" transform="rotate(0,1004.44,963.778) scale(1.25926,1.25926) translate(-204.853,-194.536)" width="15" x="994.9999984105428" xlink:href="#Disconnector:刀闸_0" y="944.8888888888889" zvalue="760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129549315" ObjectName="35kV#4主变6.3kV侧6084"/>
   <cge:TPSR_Ref TObjectID="6192454129549315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1004.44,963.778) scale(1.25926,1.25926) translate(-204.853,-194.536)" width="15" x="994.9999984105428" y="944.8888888888889"/></g>
  <g id="123">
   <use class="kv6" height="30" transform="rotate(0,1004.44,1129.33) scale(1.25926,1.25926) translate(-204.853,-228.621)" width="15" x="994.9999983178245" xlink:href="#Disconnector:刀闸_0" y="1110.444444444445" zvalue="761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129614851" ObjectName="35kV#4主变6.3kV侧6081"/>
   <cge:TPSR_Ref TObjectID="6192454129614851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1004.44,1129.33) scale(1.25926,1.25926) translate(-204.853,-228.621)" width="15" x="994.9999983178245" y="1110.444444444445"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="19">
   <path class="kv35" d="M 1005.6 568.56 L 1005.6 525.56" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.6 568.56 L 1005.6 525.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 1005.57 601.71 L 1005.57 622.11" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.57 601.71 L 1005.57 622.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1005.49 650.01 L 1005.49 678.56" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="18@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.49 650.01 L 1005.49 678.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1005.57 711.71 L 1005.57 760.81" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.57 711.71 L 1005.57 760.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1147.33 442.12 L 1147.33 397.57" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.33 442.12 L 1147.33 397.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 1147.41 369.66 L 1147.41 321.26" stroke-width="1" zvalue="726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.41 369.66 L 1147.41 321.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 1147.44 288.12 L 1147.44 149.41" stroke-width="1" zvalue="734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.44 288.12 L 1147.44 149.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 1147.3 475.26 L 1147.3 525.56" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.3 475.26 L 1147.3 525.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1098.72 212.56 L 1147.44 212.56" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1098.72 212.56 L 1147.44 212.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 1106.41 261.56 L 1147.44 261.56" stroke-width="1" zvalue="750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.41 261.56 L 1147.44 261.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 758.72 580.62 L 758.72 553.09 L 758.72 553.09 L 758.72 525.56" stroke-width="1" zvalue="753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.72 580.62 L 758.72 553.09 L 758.72 553.09 L 758.72 525.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 758.69 617.45 L 758.69 706.63" stroke-width="1" zvalue="754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.69 617.45 L 758.69 706.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 687.82 707.12 L 687.82 639.96 L 758.69 639.96" stroke-width="1" zvalue="757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.82 707.12 L 687.82 639.96 L 758.69 639.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv6" d="M 1004.55 945.51 L 1004.55 849.37" stroke-width="1" zvalue="763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.55 945.51 L 1004.55 849.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv6" d="M 1004.43 1058.01 L 1004.43 1111.07" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.43 1058.01 L 1004.43 1111.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv6" d="M 1004.52 982.34 L 1004.52 1027.01" stroke-width="1" zvalue="766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.52 982.34 L 1004.52 1027.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv6" d="M 942.86 1086.53 L 1004.43 1086.53" stroke-width="1" zvalue="767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 942.86 1086.53 L 1004.43 1086.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv6" d="M 1004.52 1147.9 L 1004.52 1238.89" stroke-width="1" zvalue="769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.52 1147.9 L 1004.52 1238.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv6" d="M 646 1327.88 L 646 1238.89" stroke-width="1" zvalue="888"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="138@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 646 1327.88 L 646 1238.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="72">
   <use class="kv35" height="26" transform="rotate(90,1086.76,212.519) scale(1.28291,0.967445) translate(-237.957,6.72817)" width="12" x="1079.059086026356" xlink:href="#Accessory:避雷器_0" y="199.9419699096725" zvalue="733"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129156099" ObjectName="35kV福靖线303避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1086.76,212.519) scale(1.28291,0.967445) translate(-237.957,6.72817)" width="12" x="1079.059086026356" y="199.9419699096725"/></g>
  <g id="66">
   <use class="kv35" height="30" transform="rotate(0,1082.61,261.56) scale(-1.7,1.7) translate(-1708.94,-97.201)" width="30" x="1057.111111111111" xlink:href="#Accessory:bsPT_0" y="236.0595238095241" zvalue="741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129025027" ObjectName="35kV福靖线303PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1082.61,261.56) scale(-1.7,1.7) translate(-1708.94,-97.201)" width="30" x="1057.111111111111" y="236.0595238095241"/></g>
  <g id="84">
   <use class="kv35" height="20" transform="rotate(0,759.376,750.833) scale(4.82222,4.82222) translate(-554.124,-556.908)" width="25" x="699.0977622240144" xlink:href="#Accessory:带熔断器接地PT_0" y="702.6111111111111" zvalue="751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129352707" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,759.376,750.833) scale(4.82222,4.82222) translate(-554.124,-556.908)" width="25" x="699.0977622240144" y="702.6111111111111"/></g>
  <g id="98">
   <use class="kv35" height="26" transform="rotate(0,687.778,723.148) scale(1.2963,1.2963) translate(-155.429,-161.439)" width="12" x="680" xlink:href="#Accessory:避雷器_0" y="706.2962962962962" zvalue="756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129483779" ObjectName="35kV母线PT避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,687.778,723.148) scale(1.2963,1.2963) translate(-155.429,-161.439)" width="12" x="680" y="706.2962962962962"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="69">
   <use class="kv35" height="30" transform="rotate(0,1147.44,134.556) scale(1,1) translate(0,0)" width="7" x="1143.944444444444" xlink:href="#ACLineSegment:线路_0" y="119.5555555555553" zvalue="736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249376292868" ObjectName="35kV福靖线"/>
   <cge:TPSR_Ref TObjectID="8444249376292868_5066549685125121"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1147.44,134.556) scale(1,1) translate(0,0)" width="7" x="1143.944444444444" y="119.5555555555553"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="125">
   <use class="kv6" height="20" transform="rotate(270,924.444,1086.67) scale(1.88889,-1.88889) translate(-430.588,-1653.07)" width="10" x="915" xlink:href="#GroundDisconnector:地刀_0" y="1067.777777777778" zvalue="762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454129745923" ObjectName="35kV#4主变6.3kV侧60817"/>
   <cge:TPSR_Ref TObjectID="6192454129745923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,924.444,1086.67) scale(1.88889,-1.88889) translate(-430.588,-1653.07)" width="10" x="915" y="1067.777777777778"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="7" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1143.78,58.7778) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.78" xml:space="preserve" y="65.54000000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135759753218" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="8" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1143.78,82.7778) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.78" xml:space="preserve" y="89.54000000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135759818754" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="9" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1143.78,106.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.78" xml:space="preserve" y="113.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135759884290" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="10" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.389,1032.56) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1039.31" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135761719298" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="11" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,635.389,1057.67) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1064.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135761784834" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,635.389,1085) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1091.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135761850370" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="14" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,635.389,1161.67) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1168.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135761915906" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="15" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.389,1109) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1115.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135761981442" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="16" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,635.389,1135.22) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1141.98" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135762046978" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="17" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.389,1212.56) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1219.31" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135762112514" ObjectName="F"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.389,1186.56) scale(1,1) translate(0,0)" writing-mode="lr" x="635.39" xml:space="preserve" y="1193.31" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135762178050" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="23" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,955.098,624.778) scale(1,1) translate(0,0)" writing-mode="lr" x="955.1" xml:space="preserve" y="631.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757328386" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="25" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,955.098,643.778) scale(1,1) translate(0,0)" writing-mode="lr" x="955.1" xml:space="preserve" y="650.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757393922" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="27" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,944.209,1001.89) scale(1,1) translate(0,0)" writing-mode="lr" x="944.21" xml:space="preserve" y="1008.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757459458" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="28" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,944.209,1025.89) scale(1,1) translate(0,0)" writing-mode="lr" x="944.21" xml:space="preserve" y="1032.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757524994" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="29" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,955.098,662.778) scale(1,1) translate(0,0)" writing-mode="lr" x="955.1" xml:space="preserve" y="669.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757590530" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="32" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,944.209,1049.89) scale(1,1) translate(0,0)" writing-mode="lr" x="944.21" xml:space="preserve" y="1056.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757918210" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="31" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,680.23,284.27) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="291.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135756804098" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="19" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,680.23,312.27) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="319.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135756869634" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="37" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,680.23,338.841) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="345.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135756935170" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" id="38" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,680.23,413.413) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="420.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757000706" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="90" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,680.23,362.841) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="369.6" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757066242" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="19" id="91" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,680.23,389.413) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="396.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757131778" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="92" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,680.23,463.984) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="470.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757197314" ObjectName="F"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="93" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,680.23,439.984) scale(1,1) translate(0,0)" writing-mode="lr" x="680.23" xml:space="preserve" y="446.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757262850" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1607">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="1607" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,294.792,519.667) scale(1,1) translate(1.75415e-13,0)" writing-mode="lr" x="294.79" xml:space="preserve" y="526.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135762243586" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="1606">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="1606" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,294.792,561.231) scale(1,1) translate(-1.16943e-13,0)" writing-mode="lr" x="294.79" xml:space="preserve" y="567.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135762309122" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="100" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,922.959,773) scale(1,1) translate(0,0)" writing-mode="lr" x="922.96" xml:space="preserve" y="779.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135757787138" ObjectName="油温1"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="42" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,927.542,799.306) scale(1,1) translate(0,0)" writing-mode="lr" x="927.54" xml:space="preserve" y="806.12" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135758180354" ObjectName="绕温"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="19" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,929.542,825.306) scale(1,1) translate(0,0)" writing-mode="lr" x="929.54" xml:space="preserve" y="832.12" zvalue="1">ddddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481129691971588" ObjectName="HTap"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="550">
   <use height="30" transform="rotate(0,330.938,218.915) scale(1.42697,0.985748) translate(-92.6171,2.95127)" width="30" x="309.53" xlink:href="#State:红绿圆_0" y="204.13" zvalue="820"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.938,218.915) scale(1.42697,0.985748) translate(-92.6171,2.95127)" width="30" x="309.53" y="204.13"/></g>
  <g id="533">
   <use height="10" transform="rotate(0,221.316,287.927) scale(6.97919,3.62755) translate(-99.9177,-195.417)" width="30" x="116.63" xlink:href="#State:全站正常运行_0" y="269.79" zvalue="828"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549685125121" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(0,221.316,287.927) scale(6.97919,3.62755) translate(-99.9177,-195.417)" width="30" x="116.63" y="269.79"/></g>
  <g id="540">
   <use height="14" transform="rotate(0,218.77,341.552) scale(6.58977,2.47845) translate(-98.9301,-193.394)" width="31" x="116.63" xlink:href="#State:正常供电_0" y="324.2" zvalue="857"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762603571085315" ObjectName=""/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,218.77,341.552) scale(6.58977,2.47845) translate(-98.9301,-193.394)" width="31" x="116.63" y="324.2"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="26">
   <use class="kv6" height="30" transform="rotate(0,646,1350) scale(1.5,1.5) translate(-207.833,-442.5)" width="30" x="623.5" xlink:href="#Generator:发电机_0" y="1327.5" zvalue="887"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192456772550657" ObjectName="35kV福桥站发电机"/>
   <cge:TPSR_Ref TObjectID="6192456772550657"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,646,1350) scale(1.5,1.5) translate(-207.833,-442.5)" width="30" x="623.5" y="1327.5"/></g>
 </g>
</svg>