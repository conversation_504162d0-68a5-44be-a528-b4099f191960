<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="210000034" height="1100" id="thSvg" viewBox="0 0 1500 1200" width="2500">
<defs>
<style type="text/css"><![CDATA[
.kv0{stroke:rgb(0,85,0);fill:none}
.kv10kV{stroke:rgb(0,255,0);fill:none}
.kv110kV{stroke:rgb(170,85,127);fill:none}
.kv220kV{stroke:rgb(255,255,255);fill:none}
.kv35kV{stroke:rgb(255,255,0);fill:none}
.kv500kV{stroke:rgb(255,0,0);fill:none}
.kv6kV{stroke:rgb(0,0,0);fill:none}
.kv-1{stroke:rgb(93,92,88);fill:none}
]]></style>
<symbol id="terminal" preserveAspectRatio="xMidYMid meet">
 <circle cx="0" cy="0" fill="yellow" r="1" stroke="yellow" stroke-width="1"/>
</symbol>
<symbol id="Breaker:lj_断路器_横_0" viewBox="0,0,34,14">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="30" xlink:href="#terminal" y="7"/>
 <rect AFMask="32783" Plane="0" fill="none" height="12" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,16,7)" width="27" x="3" y="1"/>
</symbol>
<symbol id="Breaker:lj_断路器_横_1" viewBox="0,0,34,14">
 <use Plane="0" x="3" xlink:href="#terminal" y="7"/>
 <use Plane="0" x="30" xlink:href="#terminal" y="7"/>
 <rect AFMask="32783" Plane="0" fill="rgb(0,0,255)" height="12" stroke="rgb(0,0,255)" stroke-width="1" transform="rotate(0,16,7)" width="27" x="3" y="1"/>
</symbol>
<symbol id="GZP:gz3_0" viewBox="0,0,36,36">
 <circle AFMask="32783" Plane="0" cx="16.5" cy="17.5" fill="none" r="11.5" stroke="rgb(0,170,0)" stroke-width="1"/>
</symbol>
<symbol id="GZP:gz3_1" viewBox="0,0,36,36">
 <circle AFMask="32783" Plane="0" cx="17" cy="18" fill="rgb(0,255,0)" r="12" stroke="rgb(0,255,0)" stroke-width="1"/>
</symbol>
</defs>
<g id="Head_Layer">
 <rect FacName="110kV荔枝站" InitShowingPlane="0," VerNo="10" fill="rgb(0,0,0)" height="1100" width="2500" x="0" y="0"/>
</g>
<g id="Other_Layer">
 <rect AFMask="32783" Plane="0" fill="none" height="834" stroke="rgb(255,255,255)" stroke-width="2" transform="rotate(0,625,609)" width="1163" x="44" y="192"/>
 <g ChangePicPlane="0," Plane="0" href="BH_.svg"><rect fill="none" height="64" rect-style="1" stroke="rgb(85,170,255)" stroke-width="2" width="580" x="951" y="48"/></g>
 <rect AFMask="32783" Plane="0" fill="rgb(255,170,0)" height="71" stroke="rgb(170,170,255)" stroke-width="2" transform="rotate(0,1239,77)" width="613" x="933" y="42"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="234" y2="234"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="293" x2="293" y1="189" y2="1026"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="483" x2="483" y1="190" y2="1028"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="836" x2="836" y1="189" y2="1028"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="992" x2="992" y1="193" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="669" x2="669" y1="190" y2="1028"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1899" x2="1899" y1="190" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="2222" x2="2222" y1="193" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="2066" x2="2066" y1="189" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1713" x2="1713" y1="190" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1523" x2="1523" y1="189" y2="1027"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="234" y2="234"/>
 <rect AFMask="32783" Plane="0" fill="none" height="836" stroke="rgb(255,255,255)" stroke-width="2" transform="rotate(0,1855,610)" width="1163" x="1274" y="192"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="278" y2="278"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="322" y2="322"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="366" y2="366"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="410" y2="410"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="454" y2="454"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="498" y2="498"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="542" y2="542"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="586" y2="586"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="630" y2="630"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="674" y2="674"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="718" y2="718"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="762" y2="762"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="806" y2="806"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="850" y2="850"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="894" y2="894"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="938" y2="938"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="45" x2="1207" y1="982" y2="982"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="278" y2="278"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="322" y2="322"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="366" y2="366"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="410" y2="410"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="454" y2="454"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="498" y2="498"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="542" y2="542"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="586" y2="586"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="630" y2="630"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="674" y2="674"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="718" y2="718"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="762" y2="762"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="806" y2="806"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="850" y2="850"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="894" y2="894"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="938" y2="938"/>
 <line AFMask="32783" Plane="0" fill="none" stroke="rgb(128,128,128)" stroke-width="2" x1="1275" x2="2437" y1="982" y2="982"/>
</g>
<g id="Breaker_Layer">
 <g id="100000850">
  <use class="kv35kV" height="14" transform="rotate(0,386,256) scale(1,1.1) translate(-17,-30.2727)" width="34" x="386" xlink:href="#Breaker:lj_断路器_横_0" y="256"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="220007694" ObjectName="110kV荔枝站\35.00千伏\CB_35kV荔英Ⅱ线3089测控保护装置停用重合闸软压板" Plane="0"/>
   <cge:Meas_Ref ObjectID="02200076940020"/>
  <cge:TPSR_Ref TObjectID="220007694"/></metadata>
 <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,386,256) scale(1,1.1) translate(-17,-30.2727)" width="34" x="386" y="256"/></g>
 <g id="100000854">
  <use class="kv35kV" height="14" transform="rotate(0,386,300) scale(1,1.1) translate(-17,-34.2727)" width="34" x="386" xlink:href="#Breaker:lj_断路器_横_0" y="300"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="220007695" ObjectName="110kV荔枝站\35.00千伏\CB_35kV荔沙Ⅱ线3099测控保护装置停用重合闸软压板" Plane="0"/>
   <cge:Meas_Ref ObjectID="02200076950020"/>
  <cge:TPSR_Ref TObjectID="220007695"/></metadata>
 <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,386,300) scale(1,1.1) translate(-17,-34.2727)" width="34" x="386" y="300"/></g>
</g>
<g id="GZP_Layer">
 <g id="135000856">
  <use class="kv35kV" height="36" transform="rotate(0,1093,257) scale(1,1) translate(-17,-18)" width="36" x="1093" xlink:href="#GZP:gz3_0" y="257"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306073118" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔英Ⅱ线308重合闸软压板实际状态(5级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1093,257) scale(1,1) translate(-17,-18)" width="36" x="1093" y="257"/></g>
 <g id="135000857">
  <use class="kv35kV" height="36" transform="rotate(0,912,257) scale(1,1) translate(-17,-18)" width="36" x="912" xlink:href="#GZP:gz3_0" y="257"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306073119" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔英Ⅱ线308重合闸正常方式要求状态(5级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,912,257) scale(1,1) translate(-17,-18)" width="36" x="912" y="257"/></g>
 <g id="135000861">
  <use class="kv35kV" height="36" transform="rotate(0,912,301) scale(1,1) translate(-17,-18)" width="36" x="912" xlink:href="#GZP:gz3_0" y="301"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306073120" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔沙Ⅱ线309重合闸正常方式要求状态(5级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,912,301) scale(1,1) translate(-17,-18)" width="36" x="912" y="301"/></g>
 <g id="135000860">
  <use class="kv35kV" height="36" transform="rotate(0,1093,301) scale(1,1) translate(-17,-18)" width="36" x="1093" xlink:href="#GZP:gz3_0" y="301"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306073117" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔沙Ⅱ线309重合闸软压板实际状态(5级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1093,301) scale(1,1) translate(-17,-18)" width="36" x="1093" y="301"/></g>
 <g id="135001242">
  <use class="kv35kV" height="36" transform="rotate(0,751,301) scale(1,1) translate(-17,-18)" width="36" x="751" xlink:href="#GZP:gz3_0" y="301"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306070171" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔沙Ⅱ线309重合闸充电满(4级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,751,301) scale(1,1) translate(-17,-18)" width="36" x="751" y="301"/></g>
 <g id="135001241">
  <use class="kv35kV" height="36" transform="rotate(0,751,257) scale(1,1) translate(-17,-18)" width="36" x="751" xlink:href="#GZP:gz3_0" y="257"/>
  <metadata>
   <cge:PSR_Ref AFMask="32783" ObjectID="306070207" ObjectName="110kV荔枝站\35.00千伏\RLY_35kV荔英Ⅱ线308重合闸充电满(4级)" Plane="0"/>
  </metadata>
 <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,751,257) scale(1,1) translate(-17,-18)" width="36" x="751" y="257"/></g>
</g>
<g id="Text_Layer">
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="45" font-size="45" font-width="45" stroke="rgb(255,255,255)" writing-mode="lr" x="1022" y="100">荔枝站重合闸投退表</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="122" y="222">线路名称</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="2266" y="157">实心:投入</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="2266" y="177">空心:退出</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="349" y="222">重合闸软压板</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="493" y="222">闭锁重合闸硬压板</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="855" y="211">重合闸正常</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="855" y="231">方式要求状态</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="18" font-width="18" stroke="rgb(255,255,255)" writing-mode="lr" x="1003" y="221">重合闸软压板实际状态</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="680" y="221">重合闸充电信号</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="306" y="223">停用</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="84" y="267">35kV荔英Ⅱ线308</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="84" y="311">35kV荔沙Ⅱ线309</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="1549" y="223">备自投软压板</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="2083" y="212">备自投软压板</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="2083" y="232">要求状态</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="18" font-width="18" stroke="rgb(255,255,255)" writing-mode="lr" x="2235" y="223">备自投软压板实际状态</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,255,255)" writing-mode="lr" x="1912" y="223">备自投充电信号</text>
 <text AFMask="32783" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-height="20" font-size="20" font-width="20" stroke="rgb(255,0,0)" writing-mode="lr" x="45" y="124">注:35kV荔英Ⅱ线308停用重合闸软压板、35kV荔沙Ⅱ线309停用重合闸软压板现场无遥控功能.</text>
</g>
</svg>