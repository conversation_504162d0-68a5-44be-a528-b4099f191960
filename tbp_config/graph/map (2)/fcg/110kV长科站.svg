<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549609365505" height="1180" id="thSvg" source="NR-PCS9000" viewBox="0 0 2200 1180" width="2200">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(255,255,255);fill:none}
.kv500{stroke:rgb(232,190,4);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(170,0,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(170,85,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(0,255,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.kv6{stroke:rgb(255,170,255);fill:none}
.v400{stroke:rgb(85,170,255);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.004066390041494" xlink:href="#terminal" y="0.6780487804878046"/>
   <use terminal-index="1" type="0" x="5.004066390041494" xlink:href="#terminal" y="18.84390243902438"/>
   <rect fill-opacity="0" height="18.17" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.09,9.84) scale(1,1) translate(0,0)" width="9.27" x="0.45" y="0.76"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.004066390041494" xlink:href="#terminal" y="0.6780487804878046"/>
   <use terminal-index="1" type="0" x="5.004066390041494" xlink:href="#terminal" y="18.84390243902438"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.12" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,4.98,10.23) scale(1,1) translate(0,0)" width="9.76" x="0.1" y="0.67"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.004066390041494" xlink:href="#terminal" y="0.6780487804878046"/>
   <use terminal-index="1" type="0" x="5.004066390041494" xlink:href="#terminal" y="18.84390243902438"/>
   <path d="M 0.140664 0.638211 L 9.9 0.638211 L 4.93668 10.1992 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1"/>
   <path d="M 0.140664 19.8 L 9.9 19.8 L 5.04822 10.239 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1"/>
   <rect fill-opacity="0" height="19.12" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.02,10.24) scale(1,1) translate(0,0)" width="9.76" x="0.14" y="0.68"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.2459386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.49350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.166666666666666" x2="7.583333333333334" y1="5.5" y2="23.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.750000000000002" y2="0.3554618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.541666666666666" x2="10.45833333333333" y1="5.755662181544974" y2="5.755662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.2459386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.49350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.541666666666666" x2="10.45833333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.2459386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.49350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.541666666666666" x2="10.45833333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="0.5" y1="13.5" y2="3.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.916666666666666" x2="8.25" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.1" x2="5.1" y1="3.666666666666666" y2="15.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.916666666666666" x2="8.25" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.071442753019992" xlink:href="#terminal" y="0.2488473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.416666666666668" x2="0.25" y1="3.916666666666666" y2="13.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.5" x2="9.666666666666668" y1="3.833333333333333" y2="13.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.916666666666666" x2="8.25" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19.44691358024691"/>
   <rect fill-opacity="0" height="19" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5,10) scale(1,1) translate(0,0)" width="9.5" x="0.25" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19.44691358024691"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(0,5,10) scale(1,1) translate(0,0)" width="9.5" x="0.25" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19.44691358024691"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.5" y1="0.6666666666666679" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.5" y1="0.5833333333333321" y2="19.5"/>
   <rect fill-opacity="0" height="19" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5,10) scale(1,1) translate(0,0)" width="9.5" x="0.25" y="0.5"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:放电间隙_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.07425742574257" xlink:href="#terminal" y="1.541357053910886"/>
   <path d="M 15.0179 10.6516 L 9.44643 6.55655 L 20.5893 6.55655 L 15.0179 10.6516 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="15.02" cy="20.38" fill-opacity="0" rx="5.57" ry="2.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.01785714285714" x2="15.01785714285714" y1="1.437768648720199" y2="10.65157830846088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.07142857142857" x2="19.42857142857143" y1="29.07919762794226" y2="29.07919762794226"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="25" y1="24.98417111250195" y2="24.98417111250195"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.446428571428577" x2="21.05357142857142" y1="27.0316843702221" y2="27.0316843702221"/>
   <path d="M 15.0179 12.3578 L 9.44643 16.4529 L 20.5893 16.4529 L 15.0179 12.3578 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.01785714285714" x2="15.01785714285714" y1="25.15479721731197" y2="12.35783935656102"/>
  </symbol>
  <symbol id="Accessory:避雷器－阀式_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="18.83333333333334" y2="18.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="8.333333333333336" y2="8.333333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.083333333333333" x2="11.08333333333333" y1="11.83333333333334" y2="11.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="2.166666666666666" y2="5.683333333333334"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.11,13.68) scale(1,1) translate(0,0)" width="10.05" x="5.08" y="5.68"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.702777777777772" x2="12.21666666666667" y1="28.1" y2="28.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.13333333333333" x2="10.13333333333333" y1="21.68333333333334" y2="25.28333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="8.333333333333332" y1="15.33333333333334" y2="15.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.744444444444442" x2="12.85555555555556" y1="26.71842105263158" y2="26.71842105263158"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.466666666666666" x2="14.13333333333333" y1="25.33684210526318" y2="25.33684210526318"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(0,170,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT-线路三相_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.5"/>
   <ellipse cx="5.3" cy="8.789999999999999" fill-opacity="0" rx="4.29" ry="4.04" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.60498484614148" x2="9.08839544026352" y1="2.202243457850716" y2="3.552409857852524"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.08839544026352" x2="9.08839544026352" y1="3.552409857852524" y2="5.458527128443312"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.571806034385565" x2="9.088395440263522" y1="2.122821904909434" y2="3.631831410793807"/>
   <path d="M 8.58287 13.083 L 7.57181 14.9891 L 10.605 14.9891 L 9.59393 13.083" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="12.63" cy="8.789999999999999" fill-opacity="0" rx="4.29" ry="4.04" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.58016268927414" x2="11.67404541868336" y1="8.794232351977191" y2="8.794232351977191"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.10883563028194" x2="13.59982612439757" y1="7.321143672566668" y2="8.837733078444623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.02941407734066" x2="13.67924767733885" y1="10.26732103138771" y2="8.75073162550976"/>
   <ellipse cx="9.34" cy="13.73" fill-opacity="0" rx="4.29" ry="4.1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.573485052204417" x2="5.082494558088792" y1="7.321143672566668" y2="8.837733078444623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.6529066051457" x2="5.003073005147508" y1="10.26732103138771" y2="8.75073162550976"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.102157993212215" x2="7.008275263803004" y1="8.794232351977191" y2="8.794232351977191"/>
   <ellipse cx="9.34" cy="4.46" fill-opacity="0" rx="4.29" ry="4.04" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.686947459912016" y2="21.52315435646375"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.000000000000001" x2="1.078947368421052" y1="1.607730263157897" y2="6.541118421052633"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.92105263157895" x2="7.037006578947368" y1="6.467105263157893" y2="1.607730263157894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.078947368421053" x2="6.962993421052632" y1="19.44588815789474" y2="24.37023026315789"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.037006578947368" x2="12.95805921052632" y1="24.37023026315789" y2="19.48289473684211"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7" x2="7" y1="1.666666666666664" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <ellipse cx="6.99" cy="3.03" fill-opacity="0" rx="2.9" ry="2.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.01" cy="23.12" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.99506578947368" x2="1.078947368421053" y1="6.042763157894737" y2="22.0296052631579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9679276315789469" x2="12.95805921052632" y1="6.00575657894737" y2="21.95559210526316"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV长科站" InitShowingPlane="" fill="rgb(0,0,0)" height="1180" width="2200" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="110kV长科站_全站保护信号.svg"><rect fill-opacity="0" height="53.86" width="180" x="48.57" y="838.4299999999999" zvalue="1306"/></g>
  <g href="110kV长科站_全站模拟量.svg"><rect fill-opacity="0" height="53.86" width="180" x="48.57" y="908.4299999999999" zvalue="1321"/></g>
 </g>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,148.388,105.167) scale(1,1) translate(0,8.84478e-14)" writing-mode="lr" x="148.39" xml:space="preserve" y="114.17" zvalue="594">      110kV长科站</text>
  <rect fill="none" fill-opacity="0" height="474.38" id="346" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,164.347,878.524) scale(1,1) translate(0,0)" width="293.33" x="17.68" y="641.33" zvalue="622"/>
  
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.571,865.366) scale(1,1) translate(0,0)" writing-mode="lr" x="138.57" xml:space="preserve" y="874.37" zvalue="1306">全站信号</text>
  
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.571,935.366) scale(1,1) translate(0,0)" writing-mode="lr" x="138.57" xml:space="preserve" y="944.37" zvalue="1321">全站模拟量</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,580.476,462.095) scale(1,1) translate(0,0)" writing-mode="lr" x="580.48" xml:space="preserve" y="468.6" zvalue="19">110kV#2M</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1809.5,438.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1809.5" xml:space="preserve" y="445.17" zvalue="21">110kV#1M</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833,354.056) scale(1,1) translate(0,0)" writing-mode="lr" x="833" xml:space="preserve" y="360.06" zvalue="23">106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833,424.167) scale(1,1) translate(0,0)" writing-mode="lr" x="833" xml:space="preserve" y="428.67" zvalue="25">1062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.32,100.417) scale(1,1) translate(0,0)" writing-mode="lr" x="798.3200000000001" xml:space="preserve" y="106.42" zvalue="33">潭长Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887,221.167) scale(1,1) translate(0,0)" writing-mode="lr" x="887" xml:space="preserve" y="225.67" zvalue="35">10638</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,888,310.479) scale(1,1) translate(0,0)" writing-mode="lr" x="888" xml:space="preserve" y="314.98" zvalue="45">10637</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828,279.167) scale(1,1) translate(0,0)" writing-mode="lr" x="828" xml:space="preserve" y="283.67" zvalue="51">1063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889,385.821) scale(1,1) translate(0,0)" writing-mode="lr" x="889" xml:space="preserve" y="390.32" zvalue="66">10627</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.11,438.464) scale(1,1) translate(-2.47494e-13,0)" writing-mode="lr" x="1127.11" xml:space="preserve" y="444.46" zvalue="68">100</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.19,481.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.19" xml:space="preserve" y="486.47" zvalue="75">1002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.31,485.897) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.31" xml:space="preserve" y="490.4" zvalue="80">1001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086,539.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1086" xml:space="preserve" y="543.67" zvalue="84">10027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444,347.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1444" xml:space="preserve" y="353.06" zvalue="88">105</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439.6,421.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1439.6" xml:space="preserve" y="425.67" zvalue="90">1051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1424.75,91.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.75" xml:space="preserve" y="97.42" zvalue="92">潭长Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.78,212.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.78" xml:space="preserve" y="216.67" zvalue="94">10538</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497,307.479) scale(1,1) translate(0,0)" writing-mode="lr" x="1497" xml:space="preserve" y="311.98" zvalue="104">10537</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437,279.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1437" xml:space="preserve" y="283.67" zvalue="107">1053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497,383.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1497" xml:space="preserve" y="388.32" zvalue="114">10517</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1542.82,859.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1542.82" xml:space="preserve" y="864" zvalue="190">901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1582.06,737.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.06" xml:space="preserve" y="746.14" zvalue="200">1B</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1356.5,762.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1356.5" xml:space="preserve" y="766.67" zvalue="435">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157,539.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1157" xml:space="preserve" y="543.67" zvalue="438">10017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1604.06,770.251) scale(1,1) translate(8.62952e-13,0)" writing-mode="lr" x="1604.06" xml:space="preserve" y="776.25" zvalue="568">容量:40MVA </text>
  <line fill="none" id="389" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-5" x2="318.5896103453661" y1="182.0832449256729" y2="182.0832449256729" zvalue="603"/>
  <line fill="none" id="381" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-5" x2="318.5896103453661" y1="239.3803677294025" y2="239.3803677294025" zvalue="604"/>
  <line fill="none" id="380" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="-5" x2="318.5896103453661" y1="587.3333333333333" y2="587.3333333333333" zvalue="605"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.9417,286.704) scale(1,1) translate(6.37251e-14,-4.8006e-13)" writing-mode="lr" x="73.94" xml:space="preserve" y="292.7" zvalue="618">总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="361" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.9417,327.459) scale(1,1) translate(6.37251e-14,-2.76227e-13)" writing-mode="lr" x="73.94" xml:space="preserve" y="333.46" zvalue="619">总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.9417,368.213) scale(1,1) translate(-5.25245e-14,0)" writing-mode="lr" x="73.94" xml:space="preserve" y="374.21" zvalue="620">事故总</text>
  <line fill="none" id="109" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="318.4638465851053" x2="318.4638465851053" y1="54.33333333333326" y2="1105.666666666667" zvalue="634"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1657,901) scale(1,1) translate(0,0)" writing-mode="lr" x="1657" xml:space="preserve" y="907.5" zvalue="725">10kV#1M</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="513" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,763.439,34.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="763.4400000000001" xml:space="preserve" y="39.11" zvalue="1194">P:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="512" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,763.439,54.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="763.4400000000001" xml:space="preserve" y="59.11" zvalue="1195">Q:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="508" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,763.439,74.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="763.4400000000001" xml:space="preserve" y="79.11" zvalue="1196">Ia:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="528" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1383.44,24.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.44" xml:space="preserve" y="29.11" zvalue="1198">P:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="527" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1383.44,44.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.44" xml:space="preserve" y="49.11" zvalue="1199">Q:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="524" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1383.44,64.6143) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.44" xml:space="preserve" y="69.11" zvalue="1200">Ia:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="532" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1102.01,354.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.01" xml:space="preserve" y="359.11" zvalue="1202">P:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="530" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1102.01,374.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.01" xml:space="preserve" y="379.11" zvalue="1203">Q:</text>
  <text fill="rgb(85,170,127)" font-family="SimSun" font-size="13" id="529" stroke="rgb(85,170,127)" text-anchor="middle" transform="rotate(0,1102.01,394.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.01" xml:space="preserve" y="399.11" zvalue="1204">Ia:</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="575" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1165.97,619.402) scale(1,1) translate(0,0)" writing-mode="lr" x="1165.97" xml:space="preserve" y="625.4" zvalue="1246">档位：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="574" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.47,646.402) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.47" xml:space="preserve" y="652.4" zvalue="1247">油温：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="573" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.52,696.402) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.52" xml:space="preserve" y="702.4" zvalue="1248">绕温：</text>
  <rect fill="none" fill-opacity="0" height="126.15" id="572" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1209.23,668.231) scale(1,1) translate(0,0)" width="160" x="1129.23" y="605.15" zvalue="1249"/>
  <line fill="none" id="571" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1129.5" x2="1290.5" y1="630" y2="630" zvalue="1250"/>
  <line fill="none" id="570" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1190.5" x2="1190.5" y1="606" y2="731.2222222222222" zvalue="1251"/>
  <line fill="none" id="569" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1129.5" x2="1290.5" y1="660" y2="660" zvalue="1252"/>
  <rect fill="none" fill-opacity="0" height="97.86" id="582" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1788.74,915.929) scale(1,1) translate(0,0)" width="160" x="1708.74" y="867" zvalue="1256"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="580" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1737.09,882) scale(1,1) translate(0,0)" writing-mode="lr" x="1737.09" xml:space="preserve" y="888" zvalue="1257">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="579" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1738.09,908) scale(1,1) translate(0,0)" writing-mode="lr" x="1738.09" xml:space="preserve" y="914" zvalue="1258">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="578" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1736.09,934) scale(1,1) translate(0,0)" writing-mode="lr" x="1736.09" xml:space="preserve" y="940" zvalue="1259">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="577" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1736.09,958) scale(1,1) translate(0,0)" writing-mode="lr" x="1736.09" xml:space="preserve" y="964" zvalue="1260">Uab</text>
  <line fill="none" id="576" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1708.738095238095" x2="1868.738095238095" y1="893.9999999999999" y2="893.9999999999999" zvalue="1261"/>
  <line fill="none" id="554" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1761.738095238095" x2="1761.738095238095" y1="868.0000000000001" y2="964.857142857143" zvalue="1262"/>
  <line fill="none" id="553" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1708.738095238095" x2="1868.738095238095" y1="918.9999999999999" y2="918.9999999999999" zvalue="1263"/>
  <line fill="none" id="552" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1708.738095238095" x2="1868.738095238095" y1="943.9999999999999" y2="943.9999999999999" zvalue="1264"/>
  <rect fill="none" fill-opacity="0" height="97.86" id="594" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,607.738,386.429) scale(1,1) translate(0,0)" width="160" x="527.74" y="337.5" zvalue="1269"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556.09,352.5) scale(1,1) translate(0,0)" writing-mode="lr" x="556.09" xml:space="preserve" y="358.5" zvalue="1270">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.09,378.5) scale(1,1) translate(0,0)" writing-mode="lr" x="557.09" xml:space="preserve" y="384.5" zvalue="1271">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="591" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.09,404.5) scale(1,1) translate(0,0)" writing-mode="lr" x="555.09" xml:space="preserve" y="410.5" zvalue="1272">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="590" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.09,428.5) scale(1,1) translate(0,0)" writing-mode="lr" x="555.09" xml:space="preserve" y="434.5" zvalue="1273">Uab</text>
  <line fill="none" id="589" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="527.7380952380954" x2="687.7380952380954" y1="364.4999999999999" y2="364.4999999999999" zvalue="1274"/>
  <line fill="none" id="588" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="580.7380952380954" x2="580.7380952380954" y1="338.5000000000001" y2="435.357142857143" zvalue="1275"/>
  <line fill="none" id="584" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="527.7380952380954" x2="687.7380952380954" y1="389.4999999999999" y2="389.4999999999999" zvalue="1276"/>
  <line fill="none" id="583" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="527.7380952380954" x2="687.7380952380954" y1="414.4999999999999" y2="414.4999999999999" zvalue="1277"/>
  <rect fill="none" fill-opacity="0" height="97.86" id="603" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1668.74,386.429) scale(1,1) translate(0,0)" width="160" x="1588.74" y="337.5" zvalue="1278"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1617.09,352.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1617.09" xml:space="preserve" y="358.5" zvalue="1279">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="601" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1618.09,378.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.09" xml:space="preserve" y="384.5" zvalue="1280">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="600" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.09,404.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.09" xml:space="preserve" y="410.5" zvalue="1281">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="599" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.09,428.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.09" xml:space="preserve" y="434.5" zvalue="1282">Uab</text>
  <line fill="none" id="598" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1588.738095238095" x2="1748.738095238095" y1="364.4999999999999" y2="364.4999999999999" zvalue="1283"/>
  <rect fill="none" fill-opacity="0" height="79" id="153" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,457.5,644.5) scale(1,1) translate(0,0)" width="160" x="377.5" y="605" zvalue="1283"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,621) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="627" zvalue="1284">P</text>
  <line fill="none" id="597" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1641.738095238095" x2="1641.738095238095" y1="338.5000000000001" y2="435.357142857143" zvalue="1284"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,646) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="652" zvalue="1285">Q</text>
  <line fill="none" id="596" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1588.738095238095" x2="1748.738095238095" y1="389.4999999999999" y2="389.4999999999999" zvalue="1285"/>
  <line fill="none" id="595" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1588.738095238095" x2="1748.738095238095" y1="414.4999999999999" y2="414.4999999999999" zvalue="1286"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,672) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="678" zvalue="1286">Ia</text>
  <rect fill="none" fill-opacity="0" height="79" id="606" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1220.43,786.321) scale(1,1) translate(0,0)" width="160" x="1140.43" y="746.8200000000001" zvalue="1287"/>
  <rect fill="none" fill-opacity="0" height="79" id="146" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,457.5,723.5) scale(1,1) translate(0,0)" width="160" x="377.5" y="684" zvalue="1287"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,700) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="706" zvalue="1288">P</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,762.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="768.8200000000001" zvalue="1288">P</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,725) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="731" zvalue="1289">Q</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="604" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,787.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="793.8200000000001" zvalue="1289">Q</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="551" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,813.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="819.8200000000001" zvalue="1290">Ia</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,404.852,751) scale(1,1) translate(0,0)" writing-mode="lr" x="404.85" xml:space="preserve" y="757" zvalue="1290">Ia</text>
  <rect fill="none" fill-opacity="0" height="79" id="550" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1220.43,865.321) scale(1,1) translate(0,0)" width="160" x="1140.43" y="825.8200000000001" zvalue="1291"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="142" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,355.5,641.5) scale(1,1) translate(0,0)" writing-mode="tb" x="355.5" xml:space="preserve" y="641.5" zvalue="1291">高压侧</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="141" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,354.5,723.5) scale(1,1) translate(0,0)" writing-mode="tb" x="354.5" xml:space="preserve" y="723.5" zvalue="1292">低压侧</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="549" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,841.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="847.8200000000001" zvalue="1292">P</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="548" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,866.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="872.8200000000001" zvalue="1293">Q</text>
  <line fill="none" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.4999999999998" x2="536.4999999999998" y1="631" y2="631" zvalue="1293"/>
  <line fill="none" id="139" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="432.4999999999998" x2="432.4999999999998" y1="606" y2="764" zvalue="1294"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="547" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.78,892.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.78" xml:space="preserve" y="898.8200000000001" zvalue="1294">Ia</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="546" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1118.43,783.321) scale(1,1) translate(0,0)" writing-mode="tb" x="1118.43" xml:space="preserve" y="783.3200000000001" zvalue="1295">高压侧</text>
  <line fill="none" id="134" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.4999999999998" x2="536.4999999999998" y1="656" y2="656" zvalue="1295"/>
  <line fill="none" id="103" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.4999999999998" x2="536.4999999999998" y1="711" y2="711" zvalue="1296"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="545" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117.43,865.321) scale(1,1) translate(0,0)" writing-mode="tb" x="1117.43" xml:space="preserve" y="865.3200000000001" zvalue="1296">低压侧</text>
  <line fill="none" id="544" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1140.428571428571" x2="1299.428571428571" y1="772.8214285714287" y2="772.8214285714287" zvalue="1297"/>
  <line fill="none" id="12" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.4999999999998" x2="536.4999999999998" y1="736" y2="736" zvalue="1297"/>
  <line fill="none" id="543" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1195.428571428571" x2="1195.428571428571" y1="747.8214285714287" y2="905.8214285714287" zvalue="1298"/>
  <line fill="none" id="542" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1140.428571428571" x2="1299.428571428571" y1="797.8214285714287" y2="797.8214285714287" zvalue="1299"/>
  <line fill="none" id="541" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1140.428571428571" x2="1299.428571428571" y1="852.8214285714287" y2="852.8214285714287" zvalue="1300"/>
  <line fill="none" id="540" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1140.428571428571" x2="1299.428571428571" y1="877.8214285714287" y2="877.8214285714287" zvalue="1301"/>
  <line fill="none" id="489" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1129.5" x2="1290.5" y1="685" y2="685" zvalue="1310"/>
  <line fill="none" id="490" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1129.5" x2="1290.5" y1="710" y2="710" zvalue="1312"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="503" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1158.47,671.402) scale(1,1) translate(0,0)" writing-mode="lr" x="1158.47" xml:space="preserve" y="677.4" zvalue="1314">油温2:</text>
  
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="16" id="616" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,107.182,1053.08) scale(1,1) translate(2.56036e-14,0)" writing-mode="lr" x="107.18" xml:space="preserve" y="1059.08" zvalue="1347">备自投试验注意：</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="16" id="615" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,99.492,1078.42) scale(1,1) translate(0,0)" writing-mode="lr" x="99.49200733752605" xml:space="preserve" y="1084.416666666667" zvalue="1348">1、全站失压封锁；</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="16" id="614" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,157.585,1101.42) scale(1,1) translate(8.54517e-15,0)" writing-mode="lr" x="157.5852893830484" xml:space="preserve" y="1107.416666666667" zvalue="1349">2、进线事故总信号右键停止更新。</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="677" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.824,858.833) scale(1,1) translate(0,0)" writing-mode="lr" x="773.8200000000001" xml:space="preserve" y="863.33" zvalue="1393">902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="638" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.056,719.473) scale(1,1) translate(0,0)" writing-mode="lr" x="821.0599999999999" xml:space="preserve" y="728.47" zvalue="1397">2B</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="636" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.31,625.556) scale(1,1) translate(0,0)" writing-mode="lr" x="772.3099999999999" xml:space="preserve" y="630.0599999999999" zvalue="1400">1024</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="635" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.194,527.154) scale(1,1) translate(0,-2.29663e-13)" writing-mode="lr" x="833.1900000000001" xml:space="preserve" y="531.65" zvalue="1403">10227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.889,650.599) scale(1,1) translate(0,0)" writing-mode="lr" x="831.89" xml:space="preserve" y="655.1" zvalue="1407">10248</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.5,746.5) scale(1,1) translate(0,0)" writing-mode="lr" x="588.5" xml:space="preserve" y="751" zvalue="1411">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="645" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.056,754.584) scale(1,1) translate(0,0)" writing-mode="lr" x="836.0599999999999" xml:space="preserve" y="760.58" zvalue="1415">容量:40MVA </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626,905) scale(1,1) translate(0,0)" writing-mode="lr" x="626" xml:space="preserve" y="911.5" zvalue="1418">10kV#2M</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="667" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.5,494) scale(1,1) translate(0,0)" writing-mode="lr" x="776.5" xml:space="preserve" y="498.5" zvalue="1425">1022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="671" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.097,568) scale(1,1) translate(0,0)" writing-mode="lr" x="771.1" xml:space="preserve" y="572.5" zvalue="1429">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="673" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.5,596.263) scale(1,1) translate(0,0)" writing-mode="lr" x="828.5" xml:space="preserve" y="600.76" zvalue="1433">10247</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.71,637.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.71" xml:space="preserve" y="642.39" zvalue="1438">1014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1593.29,539.488) scale(1,1) translate(0,0)" writing-mode="lr" x="1593.29" xml:space="preserve" y="543.99" zvalue="1440">10117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598.29,662.932) scale(1,1) translate(0,0)" writing-mode="lr" x="1598.29" xml:space="preserve" y="667.4299999999999" zvalue="1443">10148</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1536.9,506.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1536.9" xml:space="preserve" y="510.83" zvalue="1446">1011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.5,580.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.5" xml:space="preserve" y="584.83" zvalue="1448">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1582.9,608.596) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.9" xml:space="preserve" y="613.1" zvalue="1453">10147</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1325.26,539.053) scale(1,1) translate(0,0)" writing-mode="lr" x="1325.26" xml:space="preserve" y="543.55" zvalue="1460">0151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1272.5,571.575) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.5" xml:space="preserve" y="576.0700000000001" zvalue="1462">01517</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.04,655.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.04" xml:space="preserve" y="662" zvalue="1464">#1母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1263.25,503.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1263.25" xml:space="preserve" y="508" zvalue="1470">01518</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.026,546.386) scale(1,1) translate(0,0)" writing-mode="lr" x="942.03" xml:space="preserve" y="550.89" zvalue="1474">0152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.5,572.908) scale(1,1) translate(0,0)" writing-mode="lr" x="897.5" xml:space="preserve" y="577.41" zvalue="1476">01527</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,969.043,657.833) scale(1,1) translate(0,0)" writing-mode="lr" x="969.04" xml:space="preserve" y="664.33" zvalue="1478">#2母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.75,501.833) scale(1,1) translate(0,0)" writing-mode="lr" x="886.75" xml:space="preserve" y="506.33" zvalue="1482">01528</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.74,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1175.74" xml:space="preserve" y="1002.5" zvalue="1486">900</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069,987.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1069" xml:space="preserve" y="992" zvalue="1492">9002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.54,1085) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.54" xml:space="preserve" y="1091" zvalue="1493">10kV母线分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.471,189.494) scale(1,1) translate(0,0)" writing-mode="lr" x="891.47" xml:space="preserve" y="193.99" zvalue="1573">01067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.921,149) scale(1,1) translate(0,0)" writing-mode="lr" x="831.92" xml:space="preserve" y="153.5" zvalue="1575">0106</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909,140.476) scale(1,1) translate(0,0)" writing-mode="lr" x="909" xml:space="preserve" y="144.98" zvalue="1577">潭长Ⅱ线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1512.4,189.018) scale(1,1) translate(0,0)" writing-mode="lr" x="1512.4" xml:space="preserve" y="193.52" zvalue="1585">01057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1452.85,148.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1452.85" xml:space="preserve" y="153.02" zvalue="1587">0105</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.93,140) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.93" xml:space="preserve" y="144.5" zvalue="1589">潭长Ⅰ线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1706.71,639.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1706.71" xml:space="preserve" y="643.72" zvalue="1595">1034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1761.29,540.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1761.29" xml:space="preserve" y="545.3200000000001" zvalue="1597">10317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1766.29,664.265) scale(1,1) translate(0,0)" writing-mode="lr" x="1766.29" xml:space="preserve" y="668.77" zvalue="1600">10348</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1704.9,507.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1704.9" xml:space="preserve" y="512.17" zvalue="1602">1031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1705.5,581.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1705.5" xml:space="preserve" y="586.17" zvalue="1604">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1750.9,609.929) scale(1,1) translate(0,0)" writing-mode="lr" x="1750.9" xml:space="preserve" y="614.4299999999999" zvalue="1609">10347</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.713,625.556) scale(1,1) translate(0,0)" writing-mode="lr" x="674.71" xml:space="preserve" y="630.0599999999999" zvalue="1615">1044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,691.598,514.154) scale(1,1) translate(0,0)" writing-mode="lr" x="691.6" xml:space="preserve" y="518.65" zvalue="1617">10427</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.292,664.599) scale(1,1) translate(0,0)" writing-mode="lr" x="680.29" xml:space="preserve" y="669.1" zvalue="1620">10448</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.903,494) scale(1,1) translate(0,0)" writing-mode="lr" x="678.9" xml:space="preserve" y="498.5" zvalue="1622">1042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.5,568) scale(1,1) translate(0,0)" writing-mode="lr" x="673.5" xml:space="preserve" y="572.5" zvalue="1624">104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.903,584.263) scale(1,1) translate(0,0)" writing-mode="lr" x="686.9" xml:space="preserve" y="588.76" zvalue="1628">10447</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,409.04,480.581) scale(1,1) translate(0,0)" writing-mode="lr" x="409.04" xml:space="preserve" y="486.58" zvalue="2745">档位：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,406.54,507.581) scale(1,1) translate(0,0)" writing-mode="lr" x="406.54" xml:space="preserve" y="513.58" zvalue="2746">油温：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,405.587,557.581) scale(1,1) translate(0,0)" writing-mode="lr" x="405.59" xml:space="preserve" y="563.58" zvalue="2747">绕温：</text>
  <rect fill="none" fill-opacity="0" height="126.15" id="177" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,452.302,529.409) scale(1,1) translate(0,0)" width="160" x="372.3" y="466.33" zvalue="2748"/>
  <line fill="none" id="176" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="372.5714285714284" x2="533.5714285714284" y1="491.1785714285714" y2="491.1785714285714" zvalue="2749"/>
  <line fill="none" id="175" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="433.5714285714284" x2="433.5714285714284" y1="467.1785714285714" y2="592.4007936507936" zvalue="2750"/>
  <line fill="none" id="173" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="372.5714285714284" x2="533.5714285714284" y1="521.1785714285714" y2="521.1785714285714" zvalue="2751"/>
  <rect fill="none" fill-opacity="0" height="97.86" id="165" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,482.81,899.107) scale(1,1) translate(0,0)" width="160" x="402.81" y="850.1799999999999" zvalue="2754"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,431.162,865.179) scale(1,1) translate(0,0)" writing-mode="lr" x="431.16" xml:space="preserve" y="871.1799999999999" zvalue="2755">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,432.162,891.179) scale(1,1) translate(0,0)" writing-mode="lr" x="432.16" xml:space="preserve" y="897.1799999999999" zvalue="2756">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,430.162,917.179) scale(1,1) translate(0,0)" writing-mode="lr" x="430.16" xml:space="preserve" y="923.1799999999999" zvalue="2757">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,430.162,941.179) scale(1,1) translate(0,0)" writing-mode="lr" x="430.16" xml:space="preserve" y="947.1799999999999" zvalue="2758">Uab</text>
  <line fill="none" id="159" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="402.8095238095239" x2="562.8095238095239" y1="877.1785714285713" y2="877.1785714285713" zvalue="2759"/>
  <line fill="none" id="158" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="455.8095238095239" x2="455.8095238095239" y1="851.1785714285716" y2="948.0357142857144" zvalue="2760"/>
  <line fill="none" id="157" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="402.8095238095239" x2="562.8095238095239" y1="902.1785714285713" y2="902.1785714285713" zvalue="2761"/>
  <line fill="none" id="154" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="402.8095238095239" x2="562.8095238095239" y1="927.1785714285713" y2="927.1785714285713" zvalue="2762"/>
  <line fill="none" id="171" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="372.5714285714284" x2="533.5714285714284" y1="546.1785714285714" y2="546.1785714285714" zvalue="2809"/>
  <line fill="none" id="170" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="372.5714285714284" x2="533.5714285714284" y1="571.1785714285714" y2="571.1785714285714" zvalue="2811"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,401.54,532.581) scale(1,1) translate(0,-1.15592e-13)" writing-mode="lr" x="401.54" xml:space="preserve" y="538.58" zvalue="2813">油温2:</text>
  
 </g>
 <g id="BusbarSectionClass">
  <g id="49">
   <path class="kv110" d="M 624 463.67 L 1020 463.67" stroke-width="6" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674328707074" ObjectName="110kV#2M"/>
   <cge:TPSR_Ref TObjectID="9288674328707074"/></metadata>
  <path d="M 624 463.67 L 1020 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 1267 463.67 L 1783 463.67" stroke-width="6" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674328641538" ObjectName="110kV#1M"/>
   <cge:TPSR_Ref TObjectID="9288674328641538"/></metadata>
  <path d="M 1267 463.67 L 1783 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1204 923 L 1655 923" stroke-width="6" zvalue="724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674328772610" ObjectName="10kV#1M"/>
   <cge:TPSR_Ref TObjectID="9288674328772610"/></metadata>
  <path d="M 1204 923 L 1655 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="643">
   <path class="kv10" d="M 610 923 L 1132 923" stroke-width="6" zvalue="1417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674328838146" ObjectName="10kV#2M"/>
   <cge:TPSR_Ref TObjectID="9288674328838146"/></metadata>
  <path d="M 610 923 L 1132 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="47">
   <use class="kv110" height="20" transform="rotate(0,798,351.917) scale(1.4,1.05) translate(-226,-16.2579)" width="10" x="790.9999999998022" xlink:href="#Breaker:开关_0" y="341.4166666666666" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920344578" ObjectName="110kV潭长Ⅱ线106"/>
   <cge:TPSR_Ref TObjectID="6473924920344578"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,798,351.917) scale(1.4,1.05) translate(-226,-16.2579)" width="10" x="790.9999999998022" y="341.4166666666666"/></g>
  <g id="64">
   <use class="kv110" height="20" transform="rotate(90,1123.33,464.333) scale(1.5,1.5) translate(-371.944,-149.778)" width="10" x="1115.833333333333" xlink:href="#Breaker:母联开关_0" y="449.3333333333333" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920541186" ObjectName="110kV母线分段100"/>
   <cge:TPSR_Ref TObjectID="6473924920541186"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1123.33,464.333) scale(1.5,1.5) translate(-371.944,-149.778)" width="10" x="1115.833333333333" y="449.3333333333333"/></g>
  <g id="81">
   <use class="kv110" height="20" transform="rotate(0,1418,351.917) scale(1.4,1.05) translate(-403.143,-16.2579)" width="10" x="1410.999999999802" xlink:href="#Breaker:开关_0" y="341.4166666666666" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920410114" ObjectName="110kV潭长Ⅰ线105"/>
   <cge:TPSR_Ref TObjectID="6473924920410114"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1418,351.917) scale(1.4,1.05) translate(-403.143,-16.2579)" width="10" x="1410.999999999802" y="341.4166666666666"/></g>
  <g id="105">
   <use class="kv10" height="20" transform="rotate(0,1512.82,855) scale(1.4,1.05) translate(-430.235,-40.2143)" width="10" x="1505.823682978036" xlink:href="#Breaker:开关_0" y="844.4999999999999" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920475650" ObjectName="110kV1号主变10kV侧901"/>
   <cge:TPSR_Ref TObjectID="6473924920475650"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1512.82,855) scale(1.4,1.05) translate(-430.235,-40.2143)" width="10" x="1505.823682978036" y="844.4999999999999"/></g>
  <g id="663">
   <use class="kv10" height="20" transform="rotate(0,743.824,854.333) scale(1.4,1.05) translate(-210.521,-40.1825)" width="10" x="736.823682978036" xlink:href="#Breaker:开关_0" y="843.8333333333333" zvalue="1392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920606722" ObjectName="110kV2号主变10kV侧902"/>
   <cge:TPSR_Ref TObjectID="6473924920606722"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,743.824,854.333) scale(1.4,1.05) translate(-210.521,-40.1825)" width="10" x="736.823682978036" y="843.8333333333333"/></g>
  <g id="668">
   <use class="kv110" height="20" transform="rotate(0,743.597,565.5) scale(1.4,1.05) translate(-210.456,-26.4286)" width="10" x="736.5968713578588" xlink:href="#Breaker:开关_0" y="555" zvalue="1428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920672258" ObjectName="110kV2号主变110kV侧102"/>
   <cge:TPSR_Ref TObjectID="6473924920672258"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,743.597,565.5) scale(1.4,1.05) translate(-210.456,-26.4286)" width="10" x="736.5968713578588" y="555"/></g>
  <g id="94">
   <use class="kv110" height="20" transform="rotate(0,1510,577.833) scale(1.4,1.05) translate(-429.429,-27.0159)" width="10" x="1503" xlink:href="#Breaker:开关_0" y="567.3333333333334" zvalue="1447"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920737794" ObjectName="110kV1号主变110kV侧101"/>
   <cge:TPSR_Ref TObjectID="6473924920737794"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1510,577.833) scale(1.4,1.05) translate(-429.429,-27.0159)" width="10" x="1503" y="567.3333333333334"/></g>
  <g id="626">
   <use class="kv10" height="20" transform="rotate(0,1209.99,994) scale(-2.1,2.35) translate(-1780.67,-557.521)" width="10" x="1199.485103324544" xlink:href="#Breaker:母联开关_0" y="970.5" zvalue="1485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924920803330" ObjectName="10kV母线分段900"/>
   <cge:TPSR_Ref TObjectID="6473924920803330"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1209.99,994) scale(-2.1,2.35) translate(-1780.67,-557.521)" width="10" x="1199.485103324544" y="970.5"/></g>
  <g id="252">
   <use class="kv110" height="20" transform="rotate(0,1678,579.167) scale(1.4,1.05) translate(-477.429,-27.0794)" width="10" x="1671" xlink:href="#Breaker:开关_0" y="568.6666695276896" zvalue="1603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924922376194" ObjectName="110kV3号主变110kV侧103"/>
   <cge:TPSR_Ref TObjectID="6473924922376194"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1678,579.167) scale(1.4,1.05) translate(-477.429,-27.0794)" width="10" x="1671" y="568.6666695276896"/></g>
  <g id="270">
   <use class="kv110" height="20" transform="rotate(0,646,565.5) scale(1.4,1.05) translate(-182.571,-26.4286)" width="10" x="639" xlink:href="#Breaker:开关_0" y="555" zvalue="1623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924922441730" ObjectName="110kV4号主变110kV侧104"/>
   <cge:TPSR_Ref TObjectID="6473924922441730"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,646,565.5) scale(1.4,1.05) translate(-182.571,-26.4286)" width="10" x="639" y="555"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="46">
   <use class="kv110" height="30" transform="rotate(0,797,422.667) scale(0.8,1) translate(197.75,0)" width="15" x="791" xlink:href="#Disconnector:刀闸_0" y="407.6666666666666" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673339394" ObjectName="110kV潭长Ⅱ线1062"/>
   <cge:TPSR_Ref TObjectID="6192452673339394"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,797,422.667) scale(0.8,1) translate(197.75,0)" width="15" x="791" y="407.6666666666666"/></g>
  <g id="50">
   <use class="kv110" height="30" transform="rotate(0,798,272.667) scale(0.8,1) translate(198,0)" width="15" x="792.0000000003663" xlink:href="#Disconnector:刀闸_0" y="257.6666666666666" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452672946178" ObjectName="110kV潭长Ⅱ线1063"/>
   <cge:TPSR_Ref TObjectID="6192452672946178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,798,272.667) scale(0.8,1) translate(198,0)" width="15" x="792.0000000003663" y="257.6666666666666"/></g>
  <g id="1">
   <use class="kv110" height="30" transform="rotate(270,1056,463.744) scale(0.8,1) translate(262.5,0)" width="15" x="1050" xlink:href="#Disconnector:刀闸_0" y="448.7435897435898" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673536002" ObjectName="110kV母线分段1002"/>
   <cge:TPSR_Ref TObjectID="6192452673536002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1056,463.744) scale(0.8,1) translate(262.5,0)" width="15" x="1050" y="448.7435897435898"/></g>
  <g id="8">
   <use class="kv110" height="30" transform="rotate(270,1185.31,464.319) scale(0.8,1) translate(294.827,0)" width="15" x="1179.307692307692" xlink:href="#Disconnector:刀闸_0" y="449.31854628163" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673601538" ObjectName="110kV母线分段1001"/>
   <cge:TPSR_Ref TObjectID="6192452673601538"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1185.31,464.319) scale(0.8,1) translate(294.827,0)" width="15" x="1179.307692307692" y="449.31854628163"/></g>
  <g id="80">
   <use class="kv110" height="30" transform="rotate(0,1417.94,422.667) scale(0.8,1) translate(352.984,0)" width="15" x="1411.935471305172" xlink:href="#Disconnector:刀闸_0" y="407.6666666666666" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674453506" ObjectName="110kV潭长Ⅰ线1051"/>
   <cge:TPSR_Ref TObjectID="6192452674453506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1417.94,422.667) scale(0.8,1) translate(352.984,0)" width="15" x="1411.935471305172" y="407.6666666666666"/></g>
  <g id="65">
   <use class="kv110" height="30" transform="rotate(0,1418,272.667) scale(0.8,1) translate(353,0)" width="15" x="1412.000000000366" xlink:href="#Disconnector:刀闸_0" y="257.6666666666666" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674060290" ObjectName="110kV潭长Ⅰ线1053"/>
   <cge:TPSR_Ref TObjectID="6192452674060290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1418,272.667) scale(0.8,1) translate(353,0)" width="15" x="1412.000000000366" y="257.6666666666666"/></g>
  <g id="658">
   <use class="kv110" height="30" transform="rotate(0,743.532,626.889) scale(0.8,1) translate(184.383,0)" width="15" x="737.5323426632283" xlink:href="#Disconnector:刀闸_0" y="611.8888888888889" zvalue="1398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675567618" ObjectName="110kV2号主变110kV侧1024"/>
   <cge:TPSR_Ref TObjectID="6192452675567618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,743.532,626.889) scale(0.8,1) translate(184.383,0)" width="15" x="737.5323426632283" y="611.8888888888889"/></g>
  <g id="664">
   <use class="kv110" height="30" transform="rotate(0,745,495) scale(0.8,1) translate(184.75,0)" width="15" x="739" xlink:href="#Disconnector:刀闸_0" y="480" zvalue="1424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675764226" ObjectName="110kV2号主变110kV侧1022"/>
   <cge:TPSR_Ref TObjectID="6192452675764226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,745,495) scale(0.8,1) translate(184.75,0)" width="15" x="739" y="480"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(0,1509.94,639.222) scale(0.8,1) translate(375.984,0)" width="15" x="1503.935471305369" xlink:href="#Disconnector:刀闸_0" y="624.2222222222223" zvalue="1437"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676354050" ObjectName="110kV1号主变110kV侧1014"/>
   <cge:TPSR_Ref TObjectID="6192452676354050"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1509.94,639.222) scale(0.8,1) translate(375.984,0)" width="15" x="1503.935471305369" y="624.2222222222223"/></g>
  <g id="97">
   <use class="kv110" height="30" transform="rotate(0,1511.4,507.333) scale(0.8,1) translate(376.351,0)" width="15" x="1505.403128642141" xlink:href="#Disconnector:刀闸_0" y="492.3333333333334" zvalue="1444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676681730" ObjectName="110kV1号主变110kV侧1011"/>
   <cge:TPSR_Ref TObjectID="6192452676681730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1511.4,507.333) scale(0.8,1) translate(376.351,0)" width="15" x="1505.403128642141" y="492.3333333333334"/></g>
  <g id="37">
   <use class="kv110" height="30" transform="rotate(0,1354.97,540) scale(1.13333,1.13333) translate(-158.409,-61.5294)" width="15" x="1346.473684254909" xlink:href="#Disconnector:刀闸_0" y="523.0000000000002" zvalue="1458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676616194" ObjectName="110kV#1母线PT0151"/>
   <cge:TPSR_Ref TObjectID="6192452676616194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1354.97,540) scale(1.13333,1.13333) translate(-158.409,-61.5294)" width="15" x="1346.473684254909" y="523.0000000000002"/></g>
  <g id="130">
   <use class="kv110" height="30" transform="rotate(0,972.974,542.333) scale(1.13333,1.13333) translate(-113.467,-61.8039)" width="15" x="964.4736842549089" xlink:href="#Disconnector:刀闸_0" y="525.3333361943564" zvalue="1472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677206018" ObjectName="110kV#2母线PT0152"/>
   <cge:TPSR_Ref TObjectID="6192452677206018"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,972.974,542.333) scale(1.13333,1.13333) translate(-113.467,-61.8039)" width="15" x="964.4736842549089" y="525.3333361943564"/></g>
  <g id="77">
   <use class="kv10" height="26" transform="rotate(0,1109,985.5) scale(-2,2.80769) translate(-1656.5,-611)" width="14" x="1095" xlink:href="#Disconnector:联体手车刀闸_0" y="949" zvalue="1491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677271554" ObjectName="10kV母线分段9002手车"/>
   <cge:TPSR_Ref TObjectID="6192452677271554"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1109,985.5) scale(-2,2.80769) translate(-1656.5,-611)" width="14" x="1095" y="949"/></g>
  <g id="234">
   <use class="kv110" height="30" transform="rotate(270,832.389,164.733) scale(1.25926,1.25926) translate(-169.43,-30.0266)" width="15" x="822.944682768212" xlink:href="#Disconnector:刀闸_0" y="145.843711843712" zvalue="1574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687495170" ObjectName="110kV潭长Ⅱ线线路PT0106"/>
   <cge:TPSR_Ref TObjectID="6192452687495170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,832.389,164.733) scale(1.25926,1.25926) translate(-169.43,-30.0266)" width="15" x="822.944682768212" y="145.843711843712"/></g>
  <g id="246">
   <use class="kv110" height="30" transform="rotate(270,1453.32,164.257) scale(1.25926,1.25926) translate(-297.268,-29.9287)" width="15" x="1443.874461127157" xlink:href="#Disconnector:刀闸_0" y="145.3681844075522" zvalue="1586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687757314" ObjectName="110kV潭长Ⅰ线线路PT0105"/>
   <cge:TPSR_Ref TObjectID="6192452687757314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1453.32,164.257) scale(1.25926,1.25926) translate(-297.268,-29.9287)" width="15" x="1443.874461127157" y="145.3681844075522"/></g>
  <g id="257">
   <use class="kv110" height="30" transform="rotate(0,1677.94,640.556) scale(0.8,1) translate(417.984,0)" width="15" x="1671.935471305369" xlink:href="#Disconnector:刀闸_0" y="625.5555584165786" zvalue="1594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688412674" ObjectName="110kV3号主变110kV侧1034"/>
   <cge:TPSR_Ref TObjectID="6192452688412674"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1677.94,640.556) scale(0.8,1) translate(417.984,0)" width="15" x="1671.935471305369" y="625.5555584165786"/></g>
  <g id="253">
   <use class="kv110" height="30" transform="rotate(0,1679.4,508.667) scale(0.8,1) translate(418.351,0)" width="15" x="1673.403128642141" xlink:href="#Disconnector:刀闸_0" y="493.6666695276896" zvalue="1601"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688084994" ObjectName="110kV3号主变110kV侧1031"/>
   <cge:TPSR_Ref TObjectID="6192452688084994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1679.4,508.667) scale(0.8,1) translate(418.351,0)" width="15" x="1673.403128642141" y="493.6666695276896"/></g>
  <g id="277">
   <use class="kv110" height="30" transform="rotate(0,645.935,626.889) scale(0.8,1) translate(159.984,0)" width="15" x="639.9354713053694" xlink:href="#Disconnector:刀闸_0" y="611.8888888888889" zvalue="1614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688936962" ObjectName="110kV4号主变110kV侧1044"/>
   <cge:TPSR_Ref TObjectID="6192452688936962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,645.935,626.889) scale(0.8,1) translate(159.984,0)" width="15" x="639.9354713053694" y="611.8888888888889"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,647.403,495) scale(0.8,1) translate(160.351,0)" width="15" x="641.4031286421412" xlink:href="#Disconnector:刀闸_0" y="480" zvalue="1621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688609282" ObjectName="110kV4号主变110kV侧1042"/>
   <cge:TPSR_Ref TObjectID="6192452688609282"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,647.403,495) scale(0.8,1) translate(160.351,0)" width="15" x="641.4031286421412" y="480"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="39">
   <use class="kv110" height="30" transform="rotate(0,798.07,141.667) scale(1,1) translate(0,0)" width="7" x="794.5702216410549" xlink:href="#ACLineSegment:线路_0" y="126.6666564941402" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249339985922" ObjectName="110kV潭长Ⅱ线"/>
   <cge:TPSR_Ref TObjectID="8444249339985922_5066549609365505"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,798.07,141.667) scale(1,1) translate(0,0)" width="7" x="794.5702216410549" y="126.6666564941402"/></g>
  <g id="79">
   <use class="kv110" height="30" transform="rotate(0,1417.09,132.667) scale(1,1) translate(0,0)" width="7" x="1413.587777051227" xlink:href="#ACLineSegment:线路_0" y="117.666656176249" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249340051458" ObjectName="110kV潭长Ⅰ线"/>
   <cge:TPSR_Ref TObjectID="8444249340051458_5066549609365505"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1417.09,132.667) scale(1,1) translate(0,0)" width="7" x="1413.587777051227" y="117.666656176249"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="38">
   <use class="kv110" height="20" transform="rotate(90,839,223.667) scale(-1,-1.6) translate(-1678,-357.458)" width="10" x="834" xlink:href="#GroundDisconnector:地刀_0" y="207.6666564941402" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673208322" ObjectName="110kV潭长Ⅱ线10638"/>
   <cge:TPSR_Ref TObjectID="6192452673208322"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,839,223.667) scale(-1,-1.6) translate(-1678,-357.458)" width="10" x="834" y="207.6666564941402"/></g>
  <g id="34">
   <use class="kv110" height="20" transform="rotate(270,838.5,311.272) scale(0.8525,1.55) translate(144.34,-104.951)" width="10" x="834.2375" xlink:href="#GroundDisconnector:地刀_0" y="295.7722307484719" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673077250" ObjectName="110kV潭长Ⅱ线10637"/>
   <cge:TPSR_Ref TObjectID="6192452673077250"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,838.5,311.272) scale(0.8525,1.55) translate(144.34,-104.951)" width="10" x="834.2375" y="295.7722307484719"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(270,839.5,387.154) scale(0.8525,1.55) translate(144.513,-131.877)" width="10" x="835.2375" xlink:href="#GroundDisconnector:地刀_0" y="371.6541727701824" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673470466" ObjectName="110kV潭长Ⅱ线10627"/>
   <cge:TPSR_Ref TObjectID="6192452673470466"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,839.5,387.154) scale(0.8525,1.55) translate(144.513,-131.877)" width="10" x="835.2375" y="371.6541727701824"/></g>
  <g id="13">
   <use class="kv110" height="20" transform="rotate(0,1094.75,508.167) scale(1.35,1.35) translate(-282.074,-128.247)" width="10" x="1088" xlink:href="#GroundDisconnector:地刀_0" y="494.6666679779688" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673732610" ObjectName="110kV母线分段10027"/>
   <cge:TPSR_Ref TObjectID="6192452673732610"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1094.75,508.167) scale(1.35,1.35) translate(-282.074,-128.247)" width="10" x="1088" y="494.6666679779688"/></g>
  <g id="26">
   <use class="kv110" height="20" transform="rotate(0,1155.75,508.167) scale(1.35,1.35) translate(-297.889,-128.247)" width="10" x="1149" xlink:href="#GroundDisconnector:地刀_0" y="494.6666679382324" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673863682" ObjectName="110kV母线分段10017"/>
   <cge:TPSR_Ref TObjectID="6192452673863682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1155.75,508.167) scale(1.35,1.35) translate(-297.889,-128.247)" width="10" x="1149" y="494.6666679382324"/></g>
  <g id="78">
   <use class="kv110" height="20" transform="rotate(90,1456.78,216.667) scale(-1,-1.6) translate(-2913.56,-346.083)" width="10" x="1451.777777777778" xlink:href="#GroundDisconnector:地刀_0" y="200.6666565633939" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674322434" ObjectName="110kV潭长Ⅰ线10538"/>
   <cge:TPSR_Ref TObjectID="6192452674322434"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1456.78,216.667) scale(-1,-1.6) translate(-2913.56,-346.083)" width="10" x="1451.777777777778" y="200.6666565633939"/></g>
  <g id="67">
   <use class="kv110" height="20" transform="rotate(270,1459.5,309.154) scale(0.8525,1.55) translate(251.786,-104.2)" width="10" x="1455.2375" xlink:href="#GroundDisconnector:地刀_0" y="293.6541727701823" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674191362" ObjectName="110kV潭长Ⅰ线10537"/>
   <cge:TPSR_Ref TObjectID="6192452674191362"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1459.5,309.154) scale(0.8525,1.55) translate(251.786,-104.2)" width="10" x="1455.2375" y="293.6541727701823"/></g>
  <g id="54">
   <use class="kv110" height="20" transform="rotate(270,1459.5,388.154) scale(0.8525,1.55) translate(251.786,-132.232)" width="10" x="1455.2375" xlink:href="#GroundDisconnector:地刀_0" y="372.6541727463404" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452673994754" ObjectName="110kV潭长Ⅰ线10517"/>
   <cge:TPSR_Ref TObjectID="6192452673994754"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1459.5,388.154) scale(0.8525,1.55) translate(251.786,-132.232)" width="10" x="1455.2375" y="372.6541727463404"/></g>
  <g id="184">
   <use class="kv110" height="20" transform="rotate(0,1390.75,762.167) scale(1.35,1.35) translate(-358.815,-194.099)" width="10" x="1384" xlink:href="#GroundDisconnector:地刀_0" y="748.6666666666666" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674846722" ObjectName="110kV1号主变110kV侧中性点1010"/>
   <cge:TPSR_Ref TObjectID="6192452674846722"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1390.75,762.167) scale(1.35,1.35) translate(-358.815,-194.099)" width="10" x="1384" y="748.6666666666666"/></g>
  <g id="655">
   <use class="kv110" height="20" transform="rotate(270,786.5,530.488) scale(0.8525,1.55) translate(135.343,-182.738)" width="10" x="782.2375" xlink:href="#GroundDisconnector:地刀_0" y="514.9875000000001" zvalue="1402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675502082" ObjectName="110kV2号主变110kV侧10227"/>
   <cge:TPSR_Ref TObjectID="6192452675502082"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,786.5,530.488) scale(0.8525,1.55) translate(135.343,-182.738)" width="10" x="782.2375" y="514.9875000000001"/></g>
  <g id="653">
   <use class="kv110" height="20" transform="rotate(270,786.5,650.488) scale(0.8525,1.55) translate(135.343,-225.318)" width="10" x="782.2375" xlink:href="#GroundDisconnector:地刀_0" y="634.9875000000001" zvalue="1405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675371010" ObjectName="110kV2号主变110kV侧10248"/>
   <cge:TPSR_Ref TObjectID="6192452675371010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,786.5,650.488) scale(0.8525,1.55) translate(135.343,-225.318)" width="10" x="782.2375" y="634.9875000000001"/></g>
  <g id="649">
   <use class="kv110" height="20" transform="rotate(0,622.75,746.5) scale(1.35,1.35) translate(-159.704,-190.037)" width="10" x="616" xlink:href="#GroundDisconnector:地刀_0" y="733" zvalue="1410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675108866" ObjectName="110kV2号主变110kV侧中性点1020"/>
   <cge:TPSR_Ref TObjectID="6192452675108866"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,622.75,746.5) scale(1.35,1.35) translate(-159.704,-190.037)" width="10" x="616" y="733"/></g>
  <g id="672">
   <use class="kv110" height="20" transform="rotate(270,774.5,599.263) scale(0.8525,1.55) translate(133.267,-207.142)" width="10" x="770.2375" xlink:href="#GroundDisconnector:地刀_0" y="583.7625" zvalue="1432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675895298" ObjectName="110kV2号主变110kV侧10247"/>
   <cge:TPSR_Ref TObjectID="6192452675895298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,774.5,599.263) scale(0.8525,1.55) translate(133.267,-207.142)" width="10" x="770.2375" y="583.7625"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(270,1552.9,542.821) scale(0.8525,1.55) translate(267.947,-187.114)" width="10" x="1548.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="527.3208333333334" zvalue="1439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676288514" ObjectName="110kV1号主变110kV侧10117"/>
   <cge:TPSR_Ref TObjectID="6192452676288514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1552.9,542.821) scale(0.8525,1.55) translate(267.947,-187.114)" width="10" x="1548.640628642141" y="527.3208333333334"/></g>
  <g id="99">
   <use class="kv110" height="20" transform="rotate(270,1552.9,662.821) scale(0.8525,1.55) translate(267.947,-229.694)" width="10" x="1548.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="647.3208333333334" zvalue="1442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676157442" ObjectName="110kV1号主变110kV侧10148"/>
   <cge:TPSR_Ref TObjectID="6192452676157442"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1552.9,662.821) scale(0.8525,1.55) translate(267.947,-229.694)" width="10" x="1548.640628642141" y="647.3208333333334"/></g>
  <g id="87">
   <use class="kv110" height="20" transform="rotate(270,1540.9,611.596) scale(0.8525,1.55) translate(265.87,-211.518)" width="10" x="1536.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="596.0958333333333" zvalue="1451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676026370" ObjectName="110kV1号主变110kV侧10147"/>
   <cge:TPSR_Ref TObjectID="6192452676026370"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1540.9,611.596) scale(0.8525,1.55) translate(265.87,-211.518)" width="10" x="1536.640628642141" y="596.0958333333333"/></g>
  <g id="275">
   <use class="kv110" height="20" transform="rotate(90,1318.5,570.575) scale(-1.7,1.7) translate(-2090.59,-227.942)" width="10" x="1310" xlink:href="#GroundDisconnector:地刀_0" y="553.5746382737941" zvalue="1459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676550658" ObjectName="110kV#1母线PT01517"/>
   <cge:TPSR_Ref TObjectID="6192452676550658"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1318.5,570.575) scale(-1.7,1.7) translate(-2090.59,-227.942)" width="10" x="1310" y="553.5746382737941"/></g>
  <g id="112">
   <use class="kv110" height="20" transform="rotate(90,1317,500.5) scale(-1.7,1.7) translate(-2088.21,-199.088)" width="10" x="1308.5" xlink:href="#GroundDisconnector:地刀_0" y="483.5" zvalue="1469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676812802" ObjectName="110kV#1母线PT01518"/>
   <cge:TPSR_Ref TObjectID="6192452676812802"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1317,500.5) scale(-1.7,1.7) translate(-2088.21,-199.088)" width="10" x="1308.5" y="483.5"/></g>
  <g id="129">
   <use class="kv110" height="20" transform="rotate(90,936.5,572.908) scale(-1.7,1.7) translate(-1483.88,-228.903)" width="10" x="928" xlink:href="#GroundDisconnector:地刀_0" y="555.9079744681503" zvalue="1473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677140482" ObjectName="110kV#2母线PT01527"/>
   <cge:TPSR_Ref TObjectID="6192452677140482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,936.5,572.908) scale(-1.7,1.7) translate(-1483.88,-228.903)" width="10" x="928" y="555.9079744681503"/></g>
  <g id="124">
   <use class="kv110" height="20" transform="rotate(90,935,502.833) scale(-1.7,1.7) translate(-1481.5,-200.049)" width="10" x="926.5" xlink:href="#GroundDisconnector:地刀_0" y="485.8333361943563" zvalue="1481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676943874" ObjectName="110kV#2母线PT01528"/>
   <cge:TPSR_Ref TObjectID="6192452676943874"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,935,502.833) scale(-1.7,1.7) translate(-1481.5,-200.049)" width="10" x="926.5" y="485.8333361943563"/></g>
  <g id="235">
   <use class="kv110" height="20" transform="rotate(0,860.246,182.27) scale(0.978941,1.55) translate(18.4002,-59.1766)" width="10" x="855.3513494348779" xlink:href="#GroundDisconnector:地刀_0" y="166.7703882760031" zvalue="1572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687626242" ObjectName="110kV潭长Ⅱ线线路PT01067"/>
   <cge:TPSR_Ref TObjectID="6192452687626242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,860.246,182.27) scale(0.978941,1.55) translate(18.4002,-59.1766)" width="10" x="855.3513494348779" y="166.7703882760031"/></g>
  <g id="248">
   <use class="kv110" height="20" transform="rotate(0,1481.18,181.795) scale(0.978941,1.55) translate(31.7575,-59.0079)" width="10" x="1476.281127793823" xlink:href="#GroundDisconnector:地刀_0" y="166.2948608398435" zvalue="1584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687888386" ObjectName="110kV潭长Ⅰ线线路PT01057"/>
   <cge:TPSR_Ref TObjectID="6192452687888386"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1481.18,181.795) scale(0.978941,1.55) translate(31.7575,-59.0079)" width="10" x="1476.281127793823" y="166.2948608398435"/></g>
  <g id="256">
   <use class="kv110" height="20" transform="rotate(270,1720.9,544.154) scale(0.8525,1.55) translate(297.014,-187.587)" width="10" x="1716.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="528.6541695276896" zvalue="1596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688347138" ObjectName="110kV3号主变110kV侧10317"/>
   <cge:TPSR_Ref TObjectID="6192452688347138"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1720.9,544.154) scale(0.8525,1.55) translate(297.014,-187.587)" width="10" x="1716.640628642141" y="528.6541695276896"/></g>
  <g id="254">
   <use class="kv110" height="20" transform="rotate(270,1720.9,664.154) scale(0.8525,1.55) translate(297.014,-230.168)" width="10" x="1716.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="648.6541695276898" zvalue="1599"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688216066" ObjectName="110kV3号主变110kV侧10348"/>
   <cge:TPSR_Ref TObjectID="6192452688216066"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1720.9,664.154) scale(0.8525,1.55) translate(297.014,-230.168)" width="10" x="1716.640628642141" y="648.6541695276898"/></g>
  <g id="249">
   <use class="kv110" height="20" transform="rotate(270,1708.9,612.929) scale(0.8525,1.55) translate(294.938,-211.991)" width="10" x="1704.640628642141" xlink:href="#GroundDisconnector:地刀_0" y="597.4291695276896" zvalue="1607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688019458" ObjectName="110kV3号主变110kV侧10347"/>
   <cge:TPSR_Ref TObjectID="6192452688019458"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1708.9,612.929) scale(0.8525,1.55) translate(294.938,-211.991)" width="10" x="1704.640628642141" y="597.4291695276896"/></g>
  <g id="276">
   <use class="kv110" height="20" transform="rotate(270,688.903,530.488) scale(0.8525,1.55) translate(118.457,-182.738)" width="10" x="684.6406286421412" xlink:href="#GroundDisconnector:地刀_0" y="514.9875000000001" zvalue="1616"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688871426" ObjectName="110kV4号主变110kV侧10427"/>
   <cge:TPSR_Ref TObjectID="6192452688871426"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,688.903,530.488) scale(0.8525,1.55) translate(118.457,-182.738)" width="10" x="684.6406286421412" y="514.9875000000001"/></g>
  <g id="272">
   <use class="kv110" height="20" transform="rotate(270,688.903,650.488) scale(0.8525,1.55) translate(118.457,-225.318)" width="10" x="684.6406286421412" xlink:href="#GroundDisconnector:地刀_0" y="634.9875000000001" zvalue="1619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688740354" ObjectName="110kV4号主变110kV侧10448"/>
   <cge:TPSR_Ref TObjectID="6192452688740354"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,688.903,650.488) scale(0.8525,1.55) translate(118.457,-225.318)" width="10" x="684.6406286421412" y="634.9875000000001"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(270,676.903,599.263) scale(0.8525,1.55) translate(116.381,-207.142)" width="10" x="672.6406286421412" xlink:href="#GroundDisconnector:地刀_0" y="583.7625" zvalue="1627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452688543746" ObjectName="110kV4号主变110kV侧10447"/>
   <cge:TPSR_Ref TObjectID="6192452688543746"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,676.903,599.263) scale(0.8525,1.55) translate(116.381,-207.142)" width="10" x="672.6406286421412" y="583.7625"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="30">
   <path class="kv110" d="M 823.39 311.21 L 798.03 311.21" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.39 311.21 L 798.03 311.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv110" d="M 798.05 287.16 L 798.01 342.13" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.05 287.16 L 798.01 342.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 798.07 257.91 L 798.07 156.52" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.07 257.91 L 798.07 156.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 823.4 223.6 L 798.07 223.6" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.4 223.6 L 798.07 223.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 797.07 407.91 L 797.07 361.2" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.07 407.91 L 797.07 361.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv110" d="M 797.05 437.16 L 797.05 463.67" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.05 437.16 L 797.05 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 824.39 387.09 L 797.07 387.09" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.39 387.09 L 797.07 387.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv110" d="M 1070.49 463.69 L 1109.16 463.69" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="64@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.49 463.69 L 1109.16 463.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 1137.58 464.33 L 1170.55 464.25" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1137.58 464.33 L 1170.55 464.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv110" d="M 1199.8 464.27 L 1279 464.27" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1199.8 464.27 L 1279 464.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv110" d="M 1094.85 495 L 1094.85 463.69" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.85 495 L 1094.85 463.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 1155.85 495 L 1155.85 464.29" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155.85 495 L 1155.85 464.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 1444.39 309.09 L 1418.03 309.09" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 1444.39 309.09 L 1418.03 309.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv110" d="M 1418.05 287.16 L 1418.01 342.13" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.05 287.16 L 1418.01 342.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv110" d="M 1418.07 257.91 L 1418.07 147.52" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="79@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.07 257.91 L 1418.07 147.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 1441.18 216.6 L 1418.07 216.6" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.18 216.6 L 1418.07 216.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv110" d="M 1418.01 407.91 L 1418.01 361.2" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.01 407.91 L 1418.01 361.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv110" d="M 1417.98 437.16 L 1417.98 463.67" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.98 437.16 L 1417.98 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv110" d="M 1444.39 388.09 L 1418.01 388.09" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1444.39 388.09 L 1418.01 388.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1512.83 845.21 L 1512.83 824.86" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1512.83 845.21 L 1512.83 824.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 1511.57 714.56 L 1390.84 714.56 L 1390.85 749" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@2" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1511.57 714.56 L 1390.84 714.56 L 1390.85 749" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 1416.12 740.32 L 1416.12 714.56" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.12 740.32 L 1416.12 714.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 1443 748.92 L 1443 714.56" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1443 748.92 L 1443 714.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv10" d="M 1512.83 864.29 L 1512.83 887.25" stroke-width="1" zvalue="738"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1512.83 864.29 L 1512.83 887.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 1041.25 463.67 L 1015.62 463.67" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.25 463.67 L 1015.62 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv10" d="M 1513.37 896.13 L 1513.37 923" stroke-width="1" zvalue="1387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="315@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.37 896.13 L 1513.37 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="660">
   <path class="kv10" d="M 743.83 844.55 L 743.83 824.2" stroke-width="1" zvalue="1395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="663@0" LinkObjectIDznd="661@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.83 844.55 L 743.83 824.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="654">
   <path class="kv110" d="M 771.39 530.43 L 745.05 530.43" stroke-width="1" zvalue="1404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="655@0" LinkObjectIDznd="669" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.39 530.43 L 745.05 530.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="648">
   <path class="kv110" d="M 743.57 698.89 L 622.84 698.89 L 622.85 733.34" stroke-width="1" zvalue="1412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="659@2" LinkObjectIDznd="649@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.57 698.89 L 622.84 698.89 L 622.85 733.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="647">
   <path class="kv110" d="M 648.12 724.66 L 648.12 698.89" stroke-width="1" zvalue="1413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="651@0" LinkObjectIDznd="648" MaxPinNum="2"/>
   </metadata>
  <path d="M 648.12 724.66 L 648.12 698.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="646">
   <path class="kv110" d="M 675 733.25 L 675 698.89" stroke-width="1" zvalue="1414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="650@0" LinkObjectIDznd="648" MaxPinNum="2"/>
   </metadata>
  <path d="M 675 733.25 L 675 698.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="642">
   <path class="kv10" d="M 743.83 863.62 L 743.83 886.58" stroke-width="1" zvalue="1419"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="663@1" LinkObjectIDznd="662@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.83 863.62 L 743.83 886.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="641">
   <path class="kv10" d="M 745.37 895.46 L 745.37 923" stroke-width="1" zvalue="1420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="662@0" LinkObjectIDznd="643@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.37 895.46 L 745.37 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="640">
   <path class="kv10" d="M 743.56 772.17 L 743.56 815.31" stroke-width="1" zvalue="1421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="659@1" LinkObjectIDznd="661@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.56 772.17 L 743.56 815.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="639">
   <path class="kv10" d="M 773.13 804 L 743.56 804" stroke-width="1" zvalue="1422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="644@0" LinkObjectIDznd="640" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.13 804 L 743.56 804" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="665">
   <path class="kv110" d="M 745.07 463.67 L 745.07 480.25" stroke-width="1" zvalue="1425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@2" LinkObjectIDznd="664@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.07 463.67 L 745.07 480.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="669">
   <path class="kv110" d="M 745.05 509.49 L 745.05 555.71" stroke-width="1" zvalue="1429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@1" LinkObjectIDznd="668@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.05 509.49 L 745.05 555.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="670">
   <path class="kv110" d="M 743.6 574.79 L 743.6 612.13" stroke-width="1" zvalue="1430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="668@1" LinkObjectIDznd="658@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.6 574.79 L 743.6 612.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="674">
   <path class="kv110" d="M 759.39 599.2 L 743.6 599.2" stroke-width="1" zvalue="1433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="672@0" LinkObjectIDznd="670" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.39 599.2 L 743.6 599.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="675">
   <path class="kv110" d="M 743.58 641.38 L 743.59 675.2" stroke-width="1" zvalue="1434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="658@1" LinkObjectIDznd="659@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.58 641.38 L 743.59 675.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="676">
   <path class="kv110" d="M 771.39 650.43 L 743.58 650.43" stroke-width="1" zvalue="1435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="653@0" LinkObjectIDznd="675" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.39 650.43 L 743.58 650.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 1537.79 542.76 L 1511.45 542.76" stroke-width="1" zvalue="1441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.79 542.76 L 1511.45 542.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 1511.45 521.83 L 1511.45 568.05" stroke-width="1" zvalue="1449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1511.45 521.83 L 1511.45 568.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 1510.01 587.12 L 1510.01 624.47" stroke-width="1" zvalue="1450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1510.01 587.12 L 1510.01 624.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 1525.79 611.53 L 1510.01 611.53" stroke-width="1" zvalue="1452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1525.79 611.53 L 1510.01 611.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 1509.98 653.72 L 1509.98 690.87" stroke-width="1" zvalue="1454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.98 653.72 L 1509.98 690.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv110" d="M 1537.79 662.76 L 1509.98 662.76" stroke-width="1" zvalue="1455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.79 662.76 L 1509.98 662.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 1511.47 492.58 L 1511.47 463.67" stroke-width="1" zvalue="1456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1511.47 492.58 L 1511.47 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv110" d="M 1355.04 556.43 L 1355.04 595.11" stroke-width="1" zvalue="1463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.04 556.43 L 1355.04 595.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 1335.08 570.45 L 1355.04 570.45" stroke-width="1" zvalue="1465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="36" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.08 570.45 L 1355.04 570.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 1355.07 523.28 L 1355.07 463.67" stroke-width="1" zvalue="1466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.07 523.28 L 1355.07 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 1333.58 500.38 L 1355.07 500.38" stroke-width="1" zvalue="1470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 1333.58 500.38 L 1355.07 500.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 973.04 558.76 L 973.04 597.44" stroke-width="1" zvalue="1477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.04 558.76 L 973.04 597.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv110" d="M 953.08 572.79 L 973.04 572.79" stroke-width="1" zvalue="1479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 953.08 572.79 L 973.04 572.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 973.07 525.61 L 973.07 463.67" stroke-width="1" zvalue="1480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="49@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.07 525.61 L 973.07 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv110" d="M 951.58 502.71 L 973.07 502.71" stroke-width="1" zvalue="1483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.58 502.71 L 973.07 502.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1210.99 953.29 L 1210.99 971.68" stroke-width="1" zvalue="1487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="626@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.99 953.29 L 1210.99 971.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1209.99 1016.2 L 1209.99 1035.71" stroke-width="1" zvalue="1490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="626@1" LinkObjectIDznd="131@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.99 1016.2 L 1209.99 1035.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1211.02 943.01 L 1211.02 923" stroke-width="1" zvalue="1494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="315@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.02 943.01 L 1211.02 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1109.02 953.77 L 1109.02 923" stroke-width="1" zvalue="1495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="643@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.02 953.77 L 1109.02 923" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1210.02 1045.99 L 1210.02 1064 L 1108.96 1064 L 1108.96 1017.28" stroke-width="1" zvalue="1496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.02 1045.99 L 1210.02 1064 L 1108.96 1064 L 1108.96 1017.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1511.56 787.83 L 1511.56 815.98" stroke-width="1" zvalue="1498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1511.56 787.83 L 1511.56 815.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1548.13 804.67 L 1511.56 804.67" stroke-width="1" zvalue="1499"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.13 804.67 L 1511.56 804.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv110" d="M 813.81 164.62 L 798.07 164.62" stroke-width="1" zvalue="1580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.81 164.62 L 798.07 164.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv110" d="M 850.64 164.66 L 878.71 164.66" stroke-width="1" zvalue="1581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.64 164.66 L 878.71 164.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv110" d="M 860.32 167.16 L 860.32 164.66" stroke-width="1" zvalue="1582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.32 167.16 L 860.32 164.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv110" d="M 1434.74 164.15 L 1418.07 164.15" stroke-width="1" zvalue="1590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.74 164.15 L 1418.07 164.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv110" d="M 1471.57 164.18 L 1499.64 164.18" stroke-width="1" zvalue="1591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.57 164.18 L 1499.64 164.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv110" d="M 1481.25 166.68 L 1481.25 164.18" stroke-width="1" zvalue="1592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="243" MaxPinNum="2"/>
   </metadata>
  <path d="M 1481.25 166.68 L 1481.25 164.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv110" d="M 1705.79 544.09 L 1679.45 544.09" stroke-width="1" zvalue="1598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.79 544.09 L 1679.45 544.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv110" d="M 1679.45 523.16 L 1679.45 569.38" stroke-width="1" zvalue="1605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@1" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1679.45 523.16 L 1679.45 569.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv110" d="M 1678.01 588.45 L 1678.01 625.8" stroke-width="1" zvalue="1606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.01 588.45 L 1678.01 625.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv110" d="M 1693.79 612.87 L 1678.01 612.87" stroke-width="1" zvalue="1608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 1693.79 612.87 L 1678.01 612.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv110" d="M 1677.98 655.05 L 1677.98 692.2" stroke-width="1" zvalue="1610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1677.98 655.05 L 1677.98 692.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv110" d="M 1705.79 664.09 L 1677.98 664.09" stroke-width="1" zvalue="1611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="231" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.79 664.09 L 1677.98 664.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv110" d="M 673.79 530.43 L 647.45 530.43" stroke-width="1" zvalue="1618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.79 530.43 L 647.45 530.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv110" d="M 647.45 509.49 L 647.45 555.71" stroke-width="1" zvalue="1625"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="270@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.45 509.49 L 647.45 555.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv110" d="M 646.01 574.79 L 646.01 612.13" stroke-width="1" zvalue="1626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 646.01 574.79 L 646.01 612.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 661.79 599.2 L 646.01 599.2" stroke-width="1" zvalue="1629"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="268" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.79 599.2 L 646.01 599.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv110" d="M 645.98 641.38 L 646 675.2" stroke-width="1" zvalue="1630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.98 641.38 L 646 675.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv110" d="M 673.79 650.43 L 645.99 650.43" stroke-width="1" zvalue="1631"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="265" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.79 650.43 L 645.99 650.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv110" d="M 647.47 480.25 L 647.47 463.67" stroke-width="1" zvalue="1634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="49@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.47 480.25 L 647.47 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv110" d="M 1679.47 493.91 L 1679.47 463.67" stroke-width="1" zvalue="1635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1679.47 493.91 L 1679.47 463.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="107">
   <use class="kv10" height="22" transform="rotate(180,1513.38,887.5) scale(0.863636,0.863636) translate(237.455,138.632)" width="22" x="1503.882099116161" xlink:href="#DollyBreaker:手车_0" y="878.0000101725263" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674584578" ObjectName="110kV1号主变10kV侧901手车2"/>
   <cge:TPSR_Ref TObjectID="6192452674584578"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1513.38,887.5) scale(0.863636,0.863636) translate(237.455,138.632)" width="22" x="1503.882099116161" y="878.0000101725263"/></g>
  <g id="104">
   <use class="kv10" height="22" transform="rotate(0,1511.54,824.611) scale(0.863636,0.863636) translate(237.165,128.702)" width="22" x="1502.043806084097" xlink:href="#DollyBreaker:手车_0" y="815.1111094951627" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674519042" ObjectName="110kV1号主变10kV侧901手车"/>
   <cge:TPSR_Ref TObjectID="6192452674519042"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1511.54,824.611) scale(0.863636,0.863636) translate(237.165,128.702)" width="22" x="1502.043806084097" y="815.1111094951627"/></g>
  <g id="662">
   <use class="kv10" height="22" transform="rotate(180,745.382,886.833) scale(0.863636,0.863636) translate(116.192,138.526)" width="22" x="735.8820991161613" xlink:href="#DollyBreaker:手车_0" y="877.3333435058596" zvalue="1393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675698690" ObjectName="110kV2号主变10kV侧902手车2"/>
   <cge:TPSR_Ref TObjectID="6192452675698690"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,745.382,886.833) scale(0.863636,0.863636) translate(116.192,138.526)" width="22" x="735.8820991161613" y="877.3333435058596"/></g>
  <g id="661">
   <use class="kv10" height="22" transform="rotate(0,743.544,823.944) scale(0.863636,0.863636) translate(115.902,128.596)" width="22" x="734.0438060840966" xlink:href="#DollyBreaker:手车_0" y="814.4444428284962" zvalue="1394"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675633154" ObjectName="110kV2号主变10kV侧902手车"/>
   <cge:TPSR_Ref TObjectID="6192452675633154"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,743.544,823.944) scale(0.863636,0.863636) translate(115.902,128.596)" width="22" x="734.0438060840966" y="814.4444428284962"/></g>
  <g id="132">
   <use class="kv10" height="22" transform="rotate(0,1211.04,953) scale(-1,1) translate(-2422.07,0)" width="22" x="1200.036916596149" xlink:href="#DollyBreaker:手车_0" y="942" zvalue="1488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677402626" ObjectName="10kV母线分段900手车"/>
   <cge:TPSR_Ref TObjectID="6192452677402626"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1211.04,953) scale(-1,1) translate(-2422.07,0)" width="22" x="1200.036916596149" y="942"/></g>
  <g id="131">
   <use class="kv10" height="22" transform="rotate(180,1210.04,1036) scale(1,1) translate(0,0)" width="22" x="1199.036916596149" xlink:href="#DollyBreaker:手车_0" y="1025" zvalue="1489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677337090" ObjectName="10kV母线分段900手车2"/>
   <cge:TPSR_Ref TObjectID="6192452677337090"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(180,1210.04,1036) scale(1,1) translate(0,0)" width="22" x="1199.036916596149" y="1025"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="115">
   <g id="1150">
    <use class="kv110" height="30" transform="rotate(0,1511.56,739.139) scale(3.45833,3.46667) translate(-1044.98,-488.926)" width="24" x="1470.06" xlink:href="#PowerTransformer2:可调两卷变_0" y="687.14" zvalue="199"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874509582338" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1151">
    <use class="kv10" height="30" transform="rotate(0,1511.56,739.139) scale(3.45833,3.46667) translate(-1044.98,-488.926)" width="24" x="1470.06" xlink:href="#PowerTransformer2:可调两卷变_1" y="687.14" zvalue="199"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874509647874" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399481163778" ObjectName="110VkV1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399481163778"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1511.56,739.139) scale(3.45833,3.46667) translate(-1044.98,-488.926)" width="24" x="1470.06" y="687.14"/></g>
  <g id="659">
   <g id="6590">
    <use class="kv110" height="30" transform="rotate(0,743.556,723.473) scale(3.45833,3.46667) translate(-499.052,-477.779)" width="24" x="702.0599999999999" xlink:href="#PowerTransformer2:可调两卷变_0" y="671.47" zvalue="1396"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874509713410" ObjectName="110"/>
    </metadata>
   </g>
   <g id="6591">
    <use class="kv10" height="30" transform="rotate(0,743.556,723.473) scale(3.45833,3.46667) translate(-499.052,-477.779)" width="24" x="702.0599999999999" xlink:href="#PowerTransformer2:可调两卷变_1" y="671.47" zvalue="1396"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874509778946" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399481229314" ObjectName="110VkV2号主变"/>
   <cge:TPSR_Ref TObjectID="6755399481229314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,743.556,723.473) scale(3.45833,3.46667) translate(-499.052,-477.779)" width="24" x="702.0599999999999" y="671.47"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="179">
   <use class="kv110" height="30" transform="rotate(0,1416,759.402) scale(1.66667,1.41762) translate(-556.4,-217.451)" width="30" x="1391" xlink:href="#Accessory:放电间隙_0" y="738.1379310344828" zvalue="429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674650114" ObjectName="110kV1号主变110kV侧中性点放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1416,759.402) scale(1.66667,1.41762) translate(-556.4,-217.451)" width="30" x="1391" y="738.1379310344828"/></g>
  <g id="182">
   <use class="kv110" height="30" transform="rotate(0,1443,761.667) scale(1,1) translate(0,0)" width="20" x="1433" xlink:href="#Accessory:避雷器－阀式_0" y="746.6666666666666" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674715650" ObjectName="110kV1号主变110kV侧中性点避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1443,761.667) scale(1,1) translate(0,0)" width="20" x="1433" y="746.6666666666666"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(270,1563,804.667) scale(0.95,1.16667) translate(81.7632,-112.452)" width="20" x="1553.5" xlink:href="#Accessory:避雷器－阀式_0" y="787.1666666666666" zvalue="589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674912258" ObjectName="110kV1号主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1563,804.667) scale(0.95,1.16667) translate(81.7632,-112.452)" width="20" x="1553.5" y="787.1666666666666"/></g>
  <g id="651">
   <use class="kv110" height="30" transform="rotate(0,648,743.736) scale(1.66667,1.41762) translate(-249.2,-212.836)" width="30" x="623" xlink:href="#Accessory:放电间隙_0" y="722.4712643678162" zvalue="1408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675239938" ObjectName="110kV2号主变110kV侧中性点放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,648,743.736) scale(1.66667,1.41762) translate(-249.2,-212.836)" width="30" x="623" y="722.4712643678162"/></g>
  <g id="650">
   <use class="kv110" height="30" transform="rotate(0,675,746) scale(1,1) translate(0,0)" width="20" x="665" xlink:href="#Accessory:避雷器－阀式_0" y="731" zvalue="1409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452675174402" ObjectName="110kV2号主变110kV侧中性点避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675,746) scale(1,1) translate(0,0)" width="20" x="665" y="731"/></g>
  <g id="644">
   <use class="kv10" height="30" transform="rotate(270,788,804) scale(0.95,1.16667) translate(40.9737,-112.357)" width="20" x="778.5" xlink:href="#Accessory:避雷器－阀式_0" y="786.5" zvalue="1416"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452674977794" ObjectName="110kV2号主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,788,804) scale(0.95,1.16667) translate(40.9737,-112.357)" width="20" x="778.5" y="786.5"/></g>
  <g id="274">
   <use class="kv110" height="18" transform="rotate(0,1355.04,614) scale(2.22222,2.22222) translate(-734.274,-326.7)" width="18" x="1335.043003935563" xlink:href="#Accessory:PT-线路三相_0" y="594.0000000000002" zvalue="1461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452676419586" ObjectName="110kV#1母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1355.04,614) scale(2.22222,2.22222) translate(-734.274,-326.7)" width="18" x="1335.043003935563" y="594.0000000000002"/></g>
  <g id="128">
   <use class="kv110" height="18" transform="rotate(0,973.043,616.333) scale(2.22222,2.22222) translate(-524.174,-327.983)" width="18" x="953.0430039355631" xlink:href="#Accessory:PT-线路三相_0" y="596.3333361943564" zvalue="1475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452677009410" ObjectName="110kV#2母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,973.043,616.333) scale(2.22222,2.22222) translate(-524.174,-327.983)" width="18" x="953.0430039355631" y="596.3333361943564"/></g>
  <g id="233">
   <use class="kv110" height="18" transform="rotate(270,896.396,164.535) scale(1.66066,2.08059) translate(-350.667,-75.7289)" width="18" x="881.4495847289965" xlink:href="#Accessory:PT-线路三相_0" y="145.8096723537899" zvalue="1576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687429634" ObjectName="110kV潭长Ⅱ线线路PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,896.396,164.535) scale(1.66066,2.08059) translate(-350.667,-75.7289)" width="18" x="881.4495847289965" y="145.8096723537899"/></g>
  <g id="245">
   <use class="kv110" height="18" transform="rotate(270,1517.33,164.059) scale(1.66066,2.08059) translate(-597.691,-75.4819)" width="18" x="1502.379363087942" xlink:href="#Accessory:PT-线路三相_0" y="145.3341449176303" zvalue="1588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192452687691778" ObjectName="110kV潭长Ⅰ线线路PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1517.33,164.059) scale(1.66066,2.08059) translate(-597.691,-75.4819)" width="18" x="1502.379363087942" y="145.3341449176303"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="148">
   <use height="30" transform="rotate(0,165.275,364.86) scale(1.41665,1.14265) translate(-42.3595,-43.4087)" width="30" x="144.03" xlink:href="#State:红绿圆_0" y="347.72" zvalue="643"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951143882755" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,165.275,364.86) scale(1.41665,1.14265) translate(-42.3595,-43.4087)" width="30" x="144.03" y="347.72"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="212" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1148.33,353.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.63" xml:space="preserve" y="359.63" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716364803" ObjectName="P"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="213" stroke="rgb(85,255,0)" text-anchor="start" transform="rotate(0,1148.33,375.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.63" xml:space="preserve" y="381.63" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716430340" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="218" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1148.33,397.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.63" xml:space="preserve" y="403.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716495875" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="487">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="487" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1239.06,673.461) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.41" xml:space="preserve" y="679.26" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  </g>
  <g id="629">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="629" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,171.694,285.17) scale(1,1) translate(0,0)" writing-mode="lr" x="131.03" xml:space="preserve" y="290.84" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130720165891" ObjectName=""/>
   </metadata>
  </g>
  <g id="628">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="628" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,170.194,326.177) scale(1,1) translate(0,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="331.81" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130720231427" ObjectName=""/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="138" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,483.341,535.639) scale(1,1) translate(1.48109e-13,0)" writing-mode="lr" x="445.07" xml:space="preserve" y="541.4400000000001" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="158" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,485.056,622.473) scale(1,1) translate(0,0)" writing-mode="lr" x="486.03" xml:space="preserve" y="629.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717806595" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="159" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,486.056,644.473) scale(1,1) translate(0,0)" writing-mode="lr" x="487.03" xml:space="preserve" y="651.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717872131" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="160" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,484.056,698.473) scale(1,1) translate(0,0)" writing-mode="lr" x="485.03" xml:space="preserve" y="705.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717937667" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="161" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,484.028,722.473) scale(1,1) translate(0,0)" writing-mode="lr" x="485.01" xml:space="preserve" y="729.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130718003203" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="162" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,487.056,669.473) scale(1,1) translate(0,0)" writing-mode="lr" x="488.03" xml:space="preserve" y="676.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130718068739" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="164" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,484.556,749.473) scale(1,1) translate(0,0)" writing-mode="lr" x="485.53" xml:space="preserve" y="756.08" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130718265348" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,483.341,507.473) scale(1,1) translate(1.48109e-13,0)" writing-mode="lr" x="484.42" xml:space="preserve" y="514.08" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719379459" ObjectName="HYW"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="166" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,483.341,558.473) scale(1,1) translate(1.48109e-13,-1.21341e-13)" writing-mode="lr" x="484.42" xml:space="preserve" y="565.08" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719444996" ObjectName="HRW"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="167" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,483.341,478.473) scale(1,1) translate(1.48109e-13,0)" writing-mode="lr" x="484.42" xml:space="preserve" y="485.08" zvalue="1">sdd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719576067" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f6.2" Plane="0" fill="rgb(255,85,255)" font-family="SimSun" font-size="16" id="170" stroke="rgb(255,85,255)" text-anchor="middle" transform="rotate(0,508,863) scale(1,1) translate(0,0)" writing-mode="lr" x="508.98" xml:space="preserve" y="869.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717282307" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f6.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="171" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,509,888) scale(1,1) translate(0,0)" writing-mode="lr" x="509.98" xml:space="preserve" y="894.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717347843" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="173" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,512,915) scale(1,1) translate(0,0)" writing-mode="lr" x="512.98" xml:space="preserve" y="921.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717413379" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="175" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,512,939) scale(1,1) translate(0,0)" writing-mode="lr" x="512.98" xml:space="preserve" y="945.61" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130717478915" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,803.07,33.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="804.05" xml:space="preserve" y="40.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130711646211" ObjectName="P"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="185" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,803.07,55.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="804.05" xml:space="preserve" y="62.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130711711747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="187" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,803.07,77.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="804.05" xml:space="preserve" y="84.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130711777283" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="189" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1422.09,19.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.07" xml:space="preserve" y="26.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713546755" ObjectName="P"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f6.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="192" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1422.09,41.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.07" xml:space="preserve" y="48.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713612291" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="193" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1422.09,63.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.07" xml:space="preserve" y="70.28" zvalue="1">sddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713677827" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f6.2" Plane="0" fill="rgb(255,85,255)" font-family="SimSun" font-size="16" id="194" stroke="rgb(255,85,255)" text-anchor="middle" transform="rotate(0,631,351.667) scale(1,1) translate(0,0)" writing-mode="lr" x="631.98" xml:space="preserve" y="358.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713022467" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f6.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="195" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,630,375.667) scale(1,1) translate(0,0)" writing-mode="lr" x="630.98" xml:space="preserve" y="382.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713088003" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="196" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,630,401.667) scale(1,1) translate(0,0)" writing-mode="lr" x="630.98" xml:space="preserve" y="408.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713153539" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="197" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,631,425.667) scale(1,1) translate(0,0)" writing-mode="lr" x="631.98" xml:space="preserve" y="432.28" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130713219075" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f6.2" Plane="0" fill="rgb(255,85,255)" font-family="SimSun" font-size="16" id="199" stroke="rgb(255,85,255)" text-anchor="middle" transform="rotate(0,1688,351.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.98" xml:space="preserve" y="358.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130712498179" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f6.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="201" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1689,376.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1689.98" xml:space="preserve" y="383.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130712563715" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="202" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1692,400.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.98" xml:space="preserve" y="407.28" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130712629251" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="203" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1694,424.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1694.98" xml:space="preserve" y="431.28" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130712694787" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f6.2" Plane="0" fill="rgb(255,85,255)" font-family="SimSun" font-size="16" id="204" stroke="rgb(255,85,255)" text-anchor="middle" transform="rotate(0,1812,881) scale(1,1) translate(0,0)" writing-mode="lr" x="1812.98" xml:space="preserve" y="887.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716758019" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f6.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="205" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1811,908) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.98" xml:space="preserve" y="914.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716823555" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f6.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="206" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1812,931) scale(1,1) translate(0,0)" writing-mode="lr" x="1812.98" xml:space="preserve" y="937.61" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716889091" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f6.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="207" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1815.5,955) scale(1,1) translate(0,0)" writing-mode="lr" x="1816.48" xml:space="preserve" y="961.61" zvalue="1">ddddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716954627" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="208" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1244.06,764.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.9" xml:space="preserve" y="770.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714398723" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="214" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1249.06,786.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.9" xml:space="preserve" y="792.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714464259" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="215">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="215" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1244.06,808.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.9" xml:space="preserve" y="814.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714660867" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="216" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1250.06,837.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.9" xml:space="preserve" y="843.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714529795" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="217" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1248.53,866.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.38" xml:space="preserve" y="872.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714595331" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="219" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1245.56,889.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1246.4" xml:space="preserve" y="895.75" zvalue="1">sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130714857475" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="220" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1239.06,644.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.04" xml:space="preserve" y="650.75" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130715971587" ObjectName="HYW"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f6.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="221" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1239.06,695.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.04" xml:space="preserve" y="701.75" zvalue="1">dddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716037123" ObjectName="HRW"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="222" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1239.06,620.139) scale(1,1) translate(0,6.7517e-14)" writing-mode="lr" x="1239.87" xml:space="preserve" y="626.75" zvalue="1">sdd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130716168195" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="168" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1288.99,1053.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.82" xml:space="preserve" y="1060.11" zvalue="1">sddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719772675" ObjectName="P"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.1" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="169" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,1288.99,1075.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.82" xml:space="preserve" y="1082.11" zvalue="1">sddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719838211" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="172" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1288.99,1097.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.82" xml:space="preserve" y="1104.11" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481130719903747" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>