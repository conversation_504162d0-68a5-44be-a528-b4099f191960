<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://www.cim.com" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:dfg="http://dfg.dongfang-china.com/2010/SVGExtensions/MX" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-261" id="thSvg" viewBox="-465.768 -477.816 2969.89 1728.07">
  <defs>
    <style type="text/css"><![CDATA[
      .def {stroke:#ff0000;fill:#aaffaa}
      .kV220 {stroke:#cc00cc;fill:#cc00cc}
      .kV500 {stroke:#e56f0e;fill:#e56f0e}
      .kV110 {stroke:#cc0000;fill:#cc0000}
      .kV35 {stroke:#cccc00;fill:#cccc00}
      .kV1 {stroke:#cc0000;fill:#00cc00}
      .kVdisp {stroke:#cc0000;fill:#00cc00}
      .kV10 {stroke:#00cc00;fill:#00cc00}
      .kv11 {stroke:#ffff00;fill:#000000}
      .kV6 {stroke:#00cccc;fill:#00cccc}
      .JieDi {stroke:#3a86ff;fill:#3a86ff}
      .BuDaiDian {stroke:#585858;fill:#585858}
      .BuQueDing {stroke:#bdbdbd;fill:#bdbdbd}
      .DianYuanDian {stroke:#ff9720;fill:#ff9720}
      .QuYuGD {stroke:#ff5c3c;fill:#ff5c3c}
      .AllLevel {stroke:#ccec82;fill:#ccec82}
      .LoadLine {stroke:#31ff76;fill:#31ff76}
      .Island0 {stroke:#f8e576;fill:#f8e576}
      .Island1 {stroke:#a5ffab;fill:#a5ffab}
      .Island2 {stroke:#aef2ff;fill:#aef2ff}
      .Island3 {stroke:#b6b1fc;fill:#b6b1fc}
      .Island4 {stroke:#deafff;fill:#deafff}
      .Island5 {stroke:#ff9ec3;fill:#ff9ec3}
      .NFkV220 {stroke:#cc00cc;fill:none}
      .NFkV500 {stroke:#e56f0e;fill:none}
      .NFkV110 {stroke:#cc0000;fill:none}
      .NFkV35 {stroke:#cccc00;fill:none}
      .NFkV1 {stroke:#cc0000;fill:none}
      .NFkVdisp {stroke:#cc0000;fill:none}
      .NFkV10 {stroke:#00cc00;fill:none}
      .NFkv11 {stroke:#ffff00;fill:none}
      .NFkV6 {stroke:#00cccc;fill:none}
      .NFJieDi {stroke:#3a86ff;fill:none}
      .NFBuDaiDian {stroke:#585858;fill:none}
      .NFBuQueDing {stroke:#bdbdbd;fill:none}
      .NFDianYuanDian {stroke:#ff9720;fill:none}
      .NFQuYuGD {stroke:#ff5c3c;fill:none}
      .NFAllLevel {stroke:#ccec82;fill:none}
      .NFLoadLine {stroke:#31ff76;fill:none}
      .NFIsland0 {stroke:#f8e576;fill:none}
      .NFIsland1 {stroke:#a5ffab;fill:none}
      .NFIsland2 {stroke:#aef2ff;fill:none}
      .NFIsland3 {stroke:#b6b1fc;fill:none}
      .NFIsland4 {stroke:#deafff;fill:none}
      .NFIsland5 {stroke:#ff9ec3;fill:none}
    ]]></style>
    <symbol id="terminal" viewBox="-3 -3 6 6" visibility="hidden">
      <circle cx="0" cy="0" fill="none" r="2" stroke="rgb(255,255,0)"/>
    </symbol>
    <symbol dfg:desc="调试一" dfg:flg="g_si" id="Tag:shape0" viewBox="0 0 50 25">
      <text fill="#ff0000" font-family="SimSun" font-size="24" stroke="none" x="4" y="-3.5"/>
      <rect fill="#ffffff" height="20" rx="0" ry="10" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="5.25" y="20.5">调试</text>
    </symbol>
    <symbol dfg:desc="保供电" dfg:flg="g_si" id="Tag:shape1" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="4.25" y="22">保供</text>
    </symbol>
    <symbol dfg:desc="开关检修" dfg:flg="g_si" id="Tag:shape2" viewBox="0 0 65 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="60" x="2.5" y="3"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="4.25" y="19.25">开关检修</text>
    </symbol>
    <symbol dfg:desc="线路检修" dfg:flg="g_si" id="Tag:shape3" viewBox="0 0 65 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="60" x="2.5" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="6" y="18.25">线路检修</text>
    </symbol>
    <symbol dfg:desc="重合闸投入" dfg:flg="g_si" id="Tag:shape4" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="6" y="20.5">重投</text>
    </symbol>
    <symbol dfg:desc="备自投投入" dfg:flg="g_si" id="Tag:shape5" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="8.25" y="19">备投</text>
    </symbol>
    <symbol dfg:desc="冷备用" dfg:flg="g_si" id="Tag:shape6" viewBox="0 0 35 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="30" x="2.5" y="2.75"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="7.5" y="21.5">冷</text>
    </symbol>
    <symbol dfg:desc="热备用" dfg:flg="g_si" id="Tag:shape7" viewBox="0 0 35 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="30" x="2.75" y="2.75"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="8.5" y="20.5">热</text>
    </symbol>
    <symbol dfg:desc="保护投入" dfg:flg="g_si" id="Tag:shape8" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.25"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="7" y="19.25">保投</text>
    </symbol>
    <symbol dfg:desc="考试" dfg:flg="g_si" id="Tag:shape9" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#8888ff" stroke-width="3" width="45" x="2.5" y="2.75"/>
      <text fill="#0000cc" font-family="Sans Serif" font-size="16" stroke="none" x="9" y="20">异常</text>
    </symbol>
    <symbol dfg:desc="故障" dfg:flg="g_si" id="Tag:shape10" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="8.5" y="19.5">故障</text>
    </symbol>
    <symbol dfg:desc="无保护" dfg:flg="g_si" id="Tag:shape11" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.25"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="7.25" y="19.5">无保</text>
    </symbol>
    <symbol dfg:desc="退出运行" dfg:flg="g_si" id="Tag:shape12" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.75"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="8.75" y="19.5">退出</text>
    </symbol>
    <symbol dfg:desc="重同期" dfg:flg="g_si" id="Tag:shape14" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="5" y="19">重同期</text>
    </symbol>
    <symbol dfg:desc="禁止操作" dfg:flg="g_si" id="Tag:shape15" viewBox="-2 -28 65 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="60" x="0.75" y="-25.25"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="2.75" y="-9">禁止操作</text>
    </symbol>
    <symbol dfg:desc="重合闸退出" dfg:flg="g_si" id="Tag:shape18" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.25"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="7.25" y="19.25">重退</text>
    </symbol>
    <symbol dfg:desc="备自投退出" dfg:flg="g_si" id="Tag:shape19" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.25" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="7" y="19.25">备退</text>
    </symbol>
    <symbol dfg:desc="保护退出" dfg:flg="g_si" id="Tag:shape20" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.25" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="6.75" y="19.5">保退</text>
    </symbol>
    <symbol dfg:desc="禁止合闸，有人工作！" dfg:flg="g_si" id="Tag:shape21" viewBox="-2 -43 105 44">
      
    </symbol>
    <symbol dfg:desc="重硬退" dfg:flg="g_si" id="Tag:shape22" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.75"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="5.25" y="18.5">重硬退</text>
    </symbol>
    <symbol dfg:desc="全站停电检修" dfg:flg="g_si" id="Tag:shape23" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="11" stroke="none" x="2.5" y="17.25">全站检修</text>
    </symbol>
    <symbol dfg:desc="检修" dfg:flg="g_si" id="Tag:shape24" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="9" y="20.25">检修</text>
    </symbol>
    <symbol dfg:desc="2区" dfg:flg="g_si" id="Tag:shape25" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="19" stroke="none" x="10" y="20.75">2区</text>
    </symbol>
    <symbol dfg:desc="备自投试验" dfg:flg="g_si" id="Tag:shape26" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.5" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="9" stroke="none" x="2.5" y="16">备自投试验</text>
    </symbol>
    <symbol dfg:desc="重软退" dfg:flg="g_si" id="Tag:shape27" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.86152" y="2.43383"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="5.36152" y="18.1838">重软退</text>
    </symbol>
    <symbol dfg:desc="保护异常" dfg:flg="g_si" id="Tag:shape28" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="2.75" y="2.25"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="9" stroke="none" x="5" y="16">保护异常</text>
    </symbol>
    <symbol dfg:desc="禁止合闸线路有人工作" dfg:flg="g_si" id="Tag:shape29" viewBox="-1 -44 106 45">
      
    </symbol>
    <symbol dfg:desc="操作中" dfg:flg="g_si" id="Tag:shape30" viewBox="0 0 35 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="31.25" x="2" y="3"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="4" y="19.25">自愈</text>
    </symbol>
    <symbol dfg:desc="末端接地" dfg:flg="g_si" id="Tag:shape31" viewBox="0 0 50 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="45" x="3" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="16" stroke="none" x="7.75" y="19.5">倒供</text>
    </symbol>
    <symbol dfg:desc="111" dfg:flg="g_si" id="Tag:shape110" viewBox="0 0 45 20">
      <rect fill="#1d3b59" height="20" stroke="#547f7f" stroke-width="3" width="45" x="0" y="0"/>
      <text fill="#ffff7f" font-family="Sans Serif" font-size="9" stroke="none" x="24.0618" y="13.3046">试验</text>
      <rect fill="#bf8d5a" height="14.7651" stroke="#000000" width="9.77312" x="5.83575" y="0.351551"/>
      <rect fill="#ffffff" height="4.71078" stroke="#000000" width="6.56189" x="7.50015" y="2.039"/>
      <polyline fill="none" points="13.5699,13.9917 13.5699,17.3695 19.6232,17.3695 19.6232,-0.0910312" stroke="#ff0000"/>
      <polyline fill="none" points="7.73371,13.9917 7.73371,19.4759 22.2883,19.4759 22.4289,-0.0703102" stroke="#00557f"/>
      <line stroke="#ff0000" x1="18.4213" x2="20.8118" y1="6.34357" y2="6.34357"/>
      <line stroke="#00557f" x1="21.2266" x2="23.6171" y1="6.20295" y2="6.20295"/>
      <line stroke="#000000" x1="10.6168" x2="13.148" y1="6.39823" y2="3.23427"/>
    </symbol>
    <symbol dfg:desc="10" dfg:flg="g_si" id="test" viewBox="0 0 80 25">
      <rect fill="#ffffff" height="20" stroke="#ff0000" stroke-width="3" width="75" x="2.5" y="2.5"/>
      <text fill="#ff0000" font-family="Sans Serif" font-size="13" stroke="none" x="15.25" y="18.25">考试</text>
    </symbol>
    <symbol dfg:desc="专线" dfg:flg="g_si" id="Tag:shape16" viewBox="0 0 35 35">
      <circle cx="-17.45" cy="-7.7" fill="#aaffaa" r="0.05" stroke="#ff0000"/>
      <circle cx="13" cy="12.25" fill="#ff0000" r="12" stroke="#ff0000" transform="matrix(1.22414,0,0,1.10484,2.50431,3.81452)"/>
      <text dfg:cls="rtext" fill="#ffffff" font-family="Sans Serif" font-size="19" stroke="none" transform="matrix(1.22414,0,0,1.10484,1.24999,0.25)" x="4" y="23">专</text>
    </symbol>
    <symbol dfg:desc="联络" dfg:flg="g_si" id="Tag:shape17" viewBox="0 0 35 35">
      <circle cx="-18.325" cy="-7.4379" fill="#aaffaa" r="0.05" stroke="#ff0000"/>
      <circle cx="13" cy="12.25" fill="#ff0000" r="12" stroke="#ff0000" transform="matrix(0.87069,0,0,0.862903,0.834046,1.10081)"/>
      <text dfg:cls="rtext" fill="#ffffff" font-family="Sans" font-size="21" stroke="none" transform="matrix(0.87069,0,0,0.862903,-1.37501,-1.9879)" x="4" y="25">联</text>
    </symbol>
    <symbol dfg:desc="间隔正常信号" dfg:flg="g_so" dfg:val="0" id="DynamicPoint:bayArea_0" preserveAspectRatio="none slice" viewBox="0 0 120 40"/>
    <symbol dfg:desc="间隔异常信号" dfg:flg="g_so" dfg:val="1" id="DynamicPoint:bayArea_1" preserveAspectRatio="none slice" viewBox="0 0 120 40">
      <rect dfg:man="s200" fill="#ff0000" fill-opacity="0.784314" height="8" id="db" rx="12" ry="3.2" stroke="#000000" stroke-dasharray="8,4" stroke-opacity="0.392157" stroke-width="2" width="120" x="0" y="32"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="0" id="Disconnector:刀闸_0" viewBox="-10 -25 20 50">
      <line stroke-linecap="round" stroke-linejoin="round" stroke-width="3" x1="-0.249278" x2="-8.78526" y1="9.18792" y2="-11.3692"/>
      <use x="0" xlink:href="#terminal" y="-25"/>
      <use x="0" xlink:href="#terminal" y="22"/>
      <line stroke-width="3" x1="0" x2="0" y1="-24.0672" y2="-14.1007"/>
      <line stroke-width="3" x1="0" x2="0" y1="23.7719" y2="9.37586"/>
      <line stroke-width="3" x1="-7.96478" x2="7.96478" y1="-13.8054" y2="-13.8054"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="18.3893"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="-24.4833"/>
    </symbol>
    <symbol dfg:flg="g_so" dfg:val="1" id="Disconnector:刀闸_1" viewBox="-10 -25 20 50">
      <use x="0" xlink:href="#terminal" y="-25"/>
      <use x="0" xlink:href="#terminal" y="22"/>
      <line stroke-width="3" x1="0" x2="0" y1="-13.8054" y2="10.4094"/>
      <line stroke-width="3" x1="0" x2="0" y1="-24.0672" y2="-14.1007"/>
      <line stroke-width="3" x1="0" x2="0" y1="23.7719" y2="9.37586"/>
      <line stroke-width="3" x1="-7.96478" x2="7.96478" y1="-13.8054" y2="-13.8054"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="18.3893"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="-24.4833"/>
    </symbol>
    <symbol dfg:desc="不确定1" dfg:flg="g_so" dfg:val="2" id="Disconnector:刀闸_2" viewBox="-10 -25 20 50">
      <line stroke-width="3" x1="6.125" x2="-10.625" y1="-16.625" y2="8.125"/>
      <line stroke-width="3" x1="-10.625" x2="6.125" y1="-16.625" y2="7.875"/>
      <line stroke-width="3" x1="0" x2="0" y1="-24.0672" y2="-14.1007"/>
      <line stroke-width="3" x1="0" x2="0" y1="23.7719" y2="9.37586"/>
      <line stroke-width="3" x1="-7.96478" x2="7.96478" y1="-13.8054" y2="-13.8054"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="18.3893"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="-24.4833"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Breaker:开关竖1_0" viewBox="0 0 20 50">
      <rect fill="none" height="40" stroke-width="4" width="17" x="1.5" y="5"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="1"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="42.75"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Breaker:开关竖1_1" viewBox="0 0 20 50">
      <rect fill="rgb(0,255,0)" height="40" stroke-width="4" width="17" x="1.5" y="5"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="1"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="42.75"/>
    </symbol>
    <symbol dfg:desc="gz1" dfg:flg="g_so" dfg:val="2" id="Breaker:开关竖1_2" viewBox="0 0 20 50">
      <rect fill="none" height="40" stroke-width="4" width="17" x="1.5" y="5"/>
      <line stroke-width="3" x1="1.5" x2="18" y1="45.75" y2="4.5"/>
      <line stroke-width="3" x1="1.5" x2="18.5" y1="4.5" y2="44.75"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="1"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="42.75"/>
    </symbol>
    <symbol dfg:desc="gz2" dfg:flg="g_so" dfg:val="3" id="Breaker:开关竖1_3" viewBox="0 0 20 50">
      <rect fill="none" height="40" stroke-width="4" width="17" x="1.75" y="5.375"/>
      <line stroke-width="3" x1="1.75" x2="18.25" y1="46.125" y2="4.875"/>
      <line stroke-width="3" x1="1.75" x2="18.75" y1="4.875" y2="45.125"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="1"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="42.75"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="GroundDisconnector:左刀右地1_0" viewBox="0 0 50 20">
      <line stroke-width="3" x1="14.3909" x2="36.1008" y1="2.17617" y2="10.1141"/>
      <line stroke-width="3" x1="39.9267" x2="39.9267" y1="2.75" y2="17.25"/>
      <line stroke-width="3" x1="44.1767" x2="44.1767" y1="5.5" y2="14.5"/>
      <line stroke-width="3" x1="48.1314" x2="48.1314" y1="7.5" y2="12.5"/>
      <line stroke-width="3" x1="33.8122" x2="39.1319" y1="10" y2="10"/>
      <line stroke-width="3" x1="11.1767" x2="11.1767" y1="2.27218" y2="17.5802"/>
      <use height="8" terminal-index="0" width="8" x="1.13895" xlink:href="#terminal" y="7"/>
      <line stroke-width="3" x1="2.70975" x2="10.3233" y1="10" y2="10"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="GroundDisconnector:左刀右地1_1" viewBox="0 0 50 20">
      <line stroke-width="3" x1="9.94083" x2="36.3961" y1="10" y2="10"/>
      <line stroke-width="3" x1="39.9267" x2="39.9267" y1="2.75" y2="17.25"/>
      <line stroke-width="3" x1="44.1767" x2="44.1767" y1="5.5" y2="14.5"/>
      <line stroke-width="3" x1="48.1314" x2="48.1314" y1="7.5" y2="12.5"/>
      <line stroke-width="3" x1="33.8122" x2="39.1319" y1="10" y2="10"/>
      <line stroke-width="3" x1="11.1767" x2="11.1767" y1="2.27218" y2="17.5802"/>
      <use height="8" terminal-index="0" width="8" x="1.13895" xlink:href="#terminal" y="7"/>
      <line stroke-width="3" x1="2.70975" x2="10.3233" y1="10" y2="10"/>
    </symbol>
    <symbol dfg:desc="中间态1" dfg:flg="g_so" dfg:val="2" id="GroundDisconnector:左刀右地1_2" viewBox="0 0 50 20">
      <line stroke-width="3" x1="14.0982" x2="35.6578" y1="1.49413" y2="17.9396"/>
      <line stroke-width="3" transform="matrix(-1,0,0,-1,49.2446,28.3386)" x1="13.4392" x2="35.2178" y1="26.7882" y2="10.1036"/>
      <line stroke-width="3" x1="39.9267" x2="39.9267" y1="2.75" y2="17.25"/>
      <line stroke-width="3" x1="44.1767" x2="44.1767" y1="5.5" y2="14.5"/>
      <line stroke-width="3" x1="48.1314" x2="48.1314" y1="7.5" y2="12.5"/>
      <line stroke-width="3" x1="33.8122" x2="39.1319" y1="10" y2="10"/>
      <line stroke-width="3" x1="11.1767" x2="11.1767" y1="2.27218" y2="17.5802"/>
      <use height="8" terminal-index="0" width="8" x="1.13895" xlink:href="#terminal" y="7"/>
      <line stroke-width="3" x1="2.70975" x2="10.3233" y1="10" y2="10"/>
    </symbol>
    <symbol dfg:desc="站变1" dfg:flg="g_si" id="Other:shape79_0" viewBox="-1 -1 50 28">
      <line stroke-width="1.66667" x1="33" x2="31" y1="13" y2="9"/>
      <line stroke-width="2.45098" x1="39" x2="34" y1="13" y2="13"/>
      <line stroke-width="1.66667" x1="33" x2="31" y1="13" y2="17"/>
      <ellipse cx="34" cy="13" fill="none" rx="12.5" ry="12" stroke-width="2.07148"/>
      <line stroke-width="1.66667" x1="14" x2="12" y1="13" y2="9"/>
      <line stroke-width="2.45098" x1="20" x2="15" y1="13" y2="13"/>
      <line stroke-width="1.66667" x1="14" x2="12" y1="13" y2="17"/>
      <ellipse cx="15" cy="13" fill="none" rx="12.5" ry="12" stroke-width="2.07148"/>
      <use height="8" terminal-index="0" width="8" x="-0.5" xlink:href="#terminal" y="9.75"/>
      <use height="8" terminal-index="1" width="8" x="42.7002" xlink:href="#terminal" y="9.2505"/>
      <use height="8" terminal-index="2" width="8" x="29.75" xlink:href="#terminal" y="9.75"/>
    </symbol>
    <symbol dfg:desc="令克" dfg:flg="g_si" id="Other:Linker_0" viewBox="0 0 10 30">
      <rect fill="none" height="28.8886" rx="0" ry="6.78882" width="9.20081" x="0.395734" y="0.593601"/>
      <use height="8" terminal-index="0" width="8" x="2.13244" xlink:href="#terminal" y="0.50416"/>
      <use height="8" terminal-index="1" width="8" x="2.17695" xlink:href="#terminal" y="22.8818"/>
      <line x1="4.92441" x2="4.92441" y1="0" y2="29.779"/>
    </symbol>
    <symbol dfg:desc="避雷器横1" dfg:flg="g_si" id="Arrester:避雷器横1_0" viewBox="0 0 60 20">
      <rect fill="none" height="30" stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" width="15" x="2.5" y="12.5"/>
      <line stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" x1="2.6638" x2="17.3362" y1="50.25" y2="50.25"/>
      <line stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" x1="4.96564" x2="15.0344" y1="54.0729" y2="54.0729"/>
      <line stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" x1="7.1789" x2="12.8211" y1="57.8957" y2="57.8957"/>
      <line stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" x1="10" x2="10" y1="42.25" y2="49.25"/>
      <line stroke-width="3" transform="matrix(0,-1,1,0,-0.012635,20)" x1="10" x2="10" y1="2.12957" y2="30.3048"/>
      <polygon dfg:shp="pg_is" points="-59.7031,57.5544 -62.3122,66.3026 -57.0939,66.3026" stroke-width="2" transform="matrix(0,-1,-1,0,91.837,-49.7031)"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="0.934"/>
    </symbol>
    <symbol dfg:desc="电压互感器" dfg:flg="g_si" id="PT:电压互感器_0" viewBox="0 0 80 50">
      <circle cx="40" cy="15.5" fill="none" r="12.5" stroke-width="2"/>
      <line stroke-width="2" transform="matrix(0.532137,0,0,0.605634,74.3611,-26.0911)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(0.532137,0,0,0.605634,93.1532,-15.3422)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(0.532137,0,0,0.605634,74.2531,-4.04922)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(-0.266069,0.524494,-0.460844,-0.302817,54.8961,69.8651)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(-0.266069,0.524494,-0.460844,-0.302817,73.6881,80.614)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(-0.266069,0.524494,-0.460844,-0.302817,54.7881,91.907)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <circle cx="40" cy="35.25" fill="none" r="12.5" stroke-width="2"/>
      <line stroke-width="2" transform="matrix(-0.266069,-0.524494,0.460844,-0.302817,-9.00298,2.20534)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(-0.266069,-0.524494,0.460844,-0.302817,9.7891,12.9543)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <line stroke-width="2" transform="matrix(-0.266069,-0.524494,0.460844,-0.302817,-9.11095,24.2472)" x1="-64.5" x2="-64.5" y1="59.8208" y2="68.891"/>
      <circle cx="58.75" cy="25" fill="none" r="12.5" stroke-width="2"/>
      <circle cx="21.25" cy="25" fill="none" r="12.5" stroke-width="2"/>
      <line x1="21.0877" x2="21.0877" y1="21.8148" y2="28.1515"/>
      <line x1="21.1916" x2="16.4131" y1="27.736" y2="26.2817"/>
      <line x1="21.2954" x2="16.5169" y1="22.1265" y2="23.4769"/>
      <use height="8" terminal-index="0" width="8" x="37" xlink:href="#terminal" y="1.05133"/>
    </symbol>
    <symbol dfg:desc="避雷器竖" dfg:flg="g_si" id="Arrester:避雷器竖_0" viewBox="0 0 20 60">
      <rect fill="none" height="30" stroke-width="3" width="15" x="2.5" y="12.5"/>
      <line stroke-width="3" x1="2.6638" x2="17.3362" y1="50.25" y2="50.25"/>
      <line stroke-width="3" x1="4.96564" x2="15.0344" y1="54.0729" y2="54.0729"/>
      <line stroke-width="3" x1="7.1789" x2="12.8211" y1="57.8957" y2="57.8957"/>
      <line stroke-width="3" x1="10" x2="10" y1="42.25" y2="49.25"/>
      <line stroke-width="3" x1="10" x2="10" y1="2.12957" y2="30.3048"/>
      <polygon dfg:shp="pg_is" points="-59.7031,57.5544 -62.3122,66.3026 -57.0939,66.3026" stroke-width="2" transform="matrix(1,0,0,-1,69.7031,91.8496)"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="0.934"/>
    </symbol>
    <symbol dfg:desc="站变3" dfg:flg="g_si" id="Other:shape49_0" viewBox="0 -1 37 23">
      <line stroke-width="0.901647" transform="matrix(0.866025,0.5,-0.5,0.866025,24.0729,-1.87091)" x1="10" x2="10" y1="9" y2="6"/>
      <line stroke-width="0.901647" transform="matrix(0.866025,0.5,-0.5,0.866025,8.32294,-1.87091)" x1="10" x2="10" y1="9" y2="6"/>
      <ellipse cx="26.5694" cy="10.5" fill="none" rx="9" ry="9.5" stroke-width="1.61115"/>
      <line stroke-width="0.901611" transform="matrix(0.866025,0.5,-0.5,0.866025,25.0245,-1.44147)" x1="6" x2="9" y1="11" y2="9"/>
      <line stroke-width="0.901611" transform="matrix(0.866025,0.5,-0.5,0.866025,9.27451,-1.44147)" x1="6" x2="9" y1="11" y2="9"/>
      <ellipse cx="13.5694" cy="10.5" fill="none" rx="9" ry="9.5" stroke-width="1.61115"/>
      <line stroke-width="0.901611" transform="matrix(0.866025,0.5,-0.5,0.866025,24.0729,-1.87091)" x1="13" x2="10" y1="11" y2="9"/>
      <line stroke-width="0.901611" transform="matrix(0.866025,0.5,-0.5,0.866025,8.32294,-1.87091)" x1="13" x2="10" y1="11" y2="9"/>
      <use height="8" terminal-index="0" width="8" x="0.81943" xlink:href="#terminal" y="7.5"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Disconnector:xc1_0" viewBox="-11 -15 22 30">
      <polyline fill="none" points="-10,-1 -5.3e-06,-11 10,-1" stroke-width="3"/>
      <polyline fill="none" points="-10,10.3145 -5.3e-06,0.314455 10,10.3145" stroke-width="3"/>
      <line stroke-width="3" x1="0" x2="0" y1="0" y2="13.4314"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="-14.0703"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="7.8594"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Disconnector:xc1_1" viewBox="-11 -15 22 30">
      <polyline fill="none" points="-10.086,-0.918877 -0.0859665,-10.9189 9.91403,-0.918877" stroke-width="3"/>
      <line stroke-width="3" x1="0" x2="0" y1="-10" y2="13.4314"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="-14.0703"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="7.8594"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Disconnector:xc2_0" viewBox="-11 -15 22 30">
      <polyline fill="none" points="-10.0703,-1 -0.0703103,-11 9.92969,-1" stroke-width="3" transform="matrix(1,0,0,-1,0.070305,0.0108034)"/>
      <polyline fill="none" points="-10.0703,10.3145 -0.0703103,0.314455 9.92969,10.3145" stroke-width="3" transform="matrix(1,0,0,-1,0.070305,0.0108034)"/>
      <line stroke-width="3" transform="matrix(1,0,0,-1,0,-0.0442865)" x1="0" x2="0" y1="0" y2="13.7282"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="-14"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="8"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Disconnector:xc2_1" viewBox="-11 -15 22 30">
      <polyline fill="none" points="-10.0703,-1 -0.0703103,-11 9.92969,-1" stroke-width="3" transform="matrix(1,0,0,-1,0.070305,0.0321107)"/>
      <line stroke-width="3" x1="0" x2="0" y1="10" y2="-13.6072"/>
      <use height="8" terminal-index="0" width="8" x="-3" xlink:href="#terminal" y="-14"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="8"/>
    </symbol>
    <symbol dfg:desc="避雷器横" dfg:flg="g_si" id="Arrester:避雷器横_0" viewBox="0 0 60 20">
      <rect fill="none" height="30" stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" width="15" x="2.5" y="12.5"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" x1="2.6638" x2="17.3362" y1="50.25" y2="50.25"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" x1="4.96564" x2="15.0344" y1="54.0729" y2="54.0729"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" x1="7.1789" x2="12.8211" y1="57.8957" y2="57.8957"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" x1="10" x2="10" y1="42.25" y2="49.25"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,60.0126,0)" x1="10" x2="10" y1="2.12957" y2="30.3048"/>
      <polygon dfg:shp="pg_is" points="-59.7031,57.5544 -62.3122,66.3026 -57.0939,66.3026" stroke-width="2" transform="matrix(0,1,1,0,-31.837,69.7031)"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="0.934"/>
    </symbol>
    <symbol dfg:desc="电缆1" dfg:flg="g_si" id="Other:电缆1_0" viewBox="0 0 20 60">
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="53.8032"/>
      <polygon dfg:shp="pg_is" fill="none" points="10,38.5662 2.74815,52.7476 17.2519,52.7476" stroke-width="2"/>
      <polygon dfg:shp="pg_is" fill="none" points="-47.4044,26.0405 -54.6563,40.2219 -40.1526,40.2219" stroke-width="2" transform="matrix(1,0,0,-1,57.4044,48.933)"/>
      <line stroke-width="2" x1="10" x2="10" y1="1.9513" y2="58.75"/>
      <use height="8" terminal-index="1" width="8" x="7" xlink:href="#terminal" y="0.5532"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Disconnector:小车3_0" viewBox="0 0 22 60">
      <polyline fill="none" points="-10,-1 -5.3e-06,-11 10,-1" stroke-width="3" transform="matrix(-1,0,0,-1,11,43.763)"/>
      <polyline fill="none" points="-10,10.3145 -5.3e-06,0.314455 10,10.3145" stroke-width="3" transform="matrix(-1,0,0,-1,11,44.763)"/>
      <polyline fill="none" points="0.999991,14.1999 11,4.19989 21,14.1999" stroke-width="3"/>
      <polyline fill="none" points="0.999991,25.5144 11,15.5143 21,25.5144" stroke-width="3"/>
      <line stroke-width="3" x1="11" x2="11" y1="15.6445" y2="42.9308"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="1.29762"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="52.952"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Disconnector:小车3_1" viewBox="0 0 22 60">
      <polyline fill="none" points="-10,10.3145 -5.3e-06,0.314455 10,10.3145" stroke-width="3" transform="matrix(-1,0,0,-1,11,55.439)"/>
      <polyline fill="none" points="0.999991,14.4402 11,4.44008 21,14.4402" stroke-width="3"/>
      <line stroke-width="3" x1="11" x2="11" y1="5.68201" y2="55"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="1.29762"/>
      <use height="8" terminal-index="1" width="8" x="8" xlink:href="#terminal" y="52.952"/>
    </symbol>
    <symbol dfg:desc="负荷竖" dfg:flg="g_si" id="EnergyConsumer:load2_0" viewBox="0 0 20 50">
      <line stroke-width="3" transform="matrix(0,1,-1,0,18,-0.550012)" x1="2.52156" x2="35.8445" y1="8" y2="8"/>
      <polygon dfg:shp="pg_eq" fill="none" points="-66.4171,59.75 -72.1906,69.75 -60.6436,69.75" stroke-width="3" transform="matrix(1,0,0,-1,76.4171,104.5)"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="0.84883"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Disconnector:带熔断器隔离小车_0" viewBox="0 0 22 60">
      <polyline fill="none" points="-10,-1 -5.3e-06,-11 10,-1" stroke-width="3" transform="matrix(-1,0,0,-1,10.75,44.7816)"/>
      <polyline fill="none" points="-10,10.3145 -5.3e-06,0.314455 10,10.3145" stroke-width="3" transform="matrix(-1,0,0,-1,10.75,49.5316)"/>
      <polyline fill="none" points="0.749996,15.2185 10.75,5.21845 20.75,15.2185" stroke-width="3"/>
      <polyline fill="none" points="0.999996,22.283 11,12.2829 21,22.283" stroke-width="3"/>
      <line stroke-width="3" x1="10.75" x2="10.75" y1="11.75" y2="48.25"/>
      <rect fill="none" height="15" stroke-width="3" width="8.25" x="6.75" y="22.5"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="1.25"/>
      <use height="8" terminal-index="1" width="8" x="7.75" xlink:href="#terminal" y="54.5"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Disconnector:带熔断器隔离小车_1" viewBox="0 0 22 60">
      <polyline fill="none" points="-10,-1 -5.3e-06,-11 10,-1" stroke-width="3" transform="matrix(-1,0,0,-1,11.25,45.0316)"/>
      <polyline fill="none" points="1.25,15.4685 11.25,5.46845 21.25,15.4685" stroke-width="3"/>
      <line stroke-width="3" x1="11.25" x2="11.25" y1="5.5" y2="56"/>
      <rect fill="none" height="17.75" stroke-width="3" width="8.25" x="7" y="22"/>
      <use height="8" terminal-index="0" width="8" x="8" xlink:href="#terminal" y="1.25"/>
      <use height="8" terminal-index="1" width="8" x="7.75" xlink:href="#terminal" y="54.5"/>
    </symbol>
    <symbol dfg:desc="开" dfg:flg="g_so" dfg:val="0" id="Disconnector:令克_0" viewBox="0 0 23 50">
      <rect fill="none" height="27.25" stroke-width="2" transform="matrix(0.965926,0.258819,-0.258819,0.965926,11.2279,1.55456)" width="7.25" x="6" y="9"/>
      <line stroke-width="2" transform="matrix(0.965926,0.258819,-0.258819,0.965926,11.6982,-1.16644)" x1="9.75" x2="9.75" y1="7" y2="44"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="43.5654"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="18.3893"/>
    </symbol>
    <symbol dfg:desc="合" dfg:flg="g_so" dfg:val="1" id="Disconnector:令克_1" viewBox="0 0 23 50">
      <rect fill="none" height="27.25" stroke-width="2" width="7.25" x="6.5" y="11"/>
      <line stroke-width="2" x1="10" x2="10" y1="6.25" y2="43.25"/>
      <use height="8" terminal-index="0" width="8" x="7" xlink:href="#terminal" y="43.5654"/>
      <use height="8" terminal-index="1" width="8" x="-3" xlink:href="#terminal" y="18.3893"/>
    </symbol>
    <symbol dfg:flg="g_si" id="Capacitor:shape0_0" viewBox="-2 -20 23 20">
      <line stroke-width="3" x1="0" x2="19" y1="-11" y2="-11"/>
      <line stroke-width="3" x1="9.5" x2="9.5" y1="-10.772" y2="-18"/>
      <line stroke-width="3" x1="0" x2="19" y1="-3" y2="-3"/>
      <use height="8" terminal-index="0" width="8" x="6.5" xlink:href="#terminal" y="-19.067"/>
    </symbol>
    <symbol dfg:desc="绕组1" dfg:flg="g_po" dfg:val="0" id="Transformer2:两卷变4_0" viewBox="0 0 60 90">
      <circle cx="30" cy="27.307" fill="none" r="23.2172" stroke-width="4"/>
      <use height="8" terminal-index="0" width="8" x="27" xlink:href="#terminal" y="1.11402"/>
      <line stroke-width="3" x1="0.75" x2="53.0003" y1="57.75" y2="3.51744"/>
      <polyline fill="none" points="46.5628,3.39045 54.4338,1.99936 53.4345,9.46043" stroke-width="3"/>
      <line stroke-width="3" transform="matrix(0,1,-1,0,50.6638,15.7692)" x1="0" x2="10.0832" y1="18.9959" y2="18.9959"/>
      <line stroke-width="3" x1="33.1065" x2="24.2043" y1="25.1063" y2="30.6459"/>
      <line stroke-width="3" x1="31.0833" x2="39.5242" y1="25.0274" y2="30.6413"/>
      <use height="8" terminal-index="1" width="8" x="27" xlink:href="#terminal" y="24"/>
    </symbol>
    <symbol dfg:desc="绕组2" dfg:flg="g_po" dfg:val="1" id="Transformer2:两卷变4_1" viewBox="0 0 60 90">
      <circle cx="30" cy="62.4614" fill="none" r="23.2172" stroke-width="4"/>
      <use height="8" terminal-index="2" width="8" x="27" xlink:href="#terminal" y="82.7558"/>
      <polygon dfg:shp="pg_eq" fill="none" points="30,58.8606 22.0449,72.6394 37.9551,72.6394" stroke-width="3"/>
    </symbol>
    <symbol dfg:desc="自愈" dfg:flg="g_si" id="Other:自愈_0" viewBox="0 0 35 35">
      <circle cx="18" cy="16.875" fill="#ff0000" r="15.375"/>
      <text dfg:cls="rtext" fill="#ffffff" font-family="Sans Serif" font-size="24" stroke="none" x="6.625" y="26.75">自</text>
    </symbol>
    <symbol dfg:desc="联络" dfg:flg="g_si" id="Other:联络_0" viewBox="0 0 35 35">
      <circle cx="18" cy="17.875" fill="#ff0000" r="15.375"/>
      <text dfg:cls="rtext" fill="#ffffff" font-family="Sans Serif" font-size="24" stroke="none" x="5.625" y="28.75">联</text>
    </symbol>
  </defs>
  
  <g id="HeadClass">
    <rect fill="rgb(0,0,0)" height="1728.066" stroke="none" width="2969.8866" x="-465.768" y="-477.816"/>
  </g>
  <g id="OtherClass">
    <rect fill="none" height="1248.27" stroke="rgb(0,60,90)" stroke-width="1" width="329.646" x="-460.19" y="-434.003"/>
    <rect fill="none" height="280.842" stroke="rgb(0,60,90)" stroke-width="1" width="308.077" x="-447.552" y="525.116"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-458.254" x2="-129.773" y1="-175.117" y2="-175.117"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-459.768" x2="-129.57" y1="75.2845" y2="75.2845"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-451.8" x2="-137.277" y1="125.575" y2="125.575"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-386.004" x2="-137.783" y1="173.882" y2="173.882"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-451.8" x2="-137.277" y1="222.189" y2="222.189"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-387.496" x2="-137.783" y1="270.496" y2="270.496"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-388.61" x2="-137.783" y1="318.802" y2="318.802"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-386.177" x2="-386.177" y1="82.5825" y2="419.445"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-298.6" x2="-298.6" y1="81.9825" y2="419.35"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-219.092" x2="-219.092" y1="81.2685" y2="419.445"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-451.977" x2="-138.309" y1="367.109" y2="367.109"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-459.053" x2="-131.216" y1="429.4" y2="429.4"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-459.263" x2="-130.686" y1="-259.831" y2="-259.831"/>
    <line stroke="rgb(0,60,90)" stroke-width="1" x1="-348.539" x2="-348.539" y1="-326.241" y2="-259.591"/>
    <rect fill="rgb(170,255,170)" height="60.9975" stroke="rgb(0,60,90)" stroke-width="1" width="178.482" x="-390.917" y="-248.711"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="440.389" y2="440.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="661.717" x2="661.717" y1="284.389" y2="440.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="378.389" y2="378.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="347.389" y2="347.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="315.389" y2="315.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="524.717" y1="284.389" y2="440.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="284.389" y2="284.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="524.717" x2="661.717" y1="409.389" y2="409.389"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="435.211" y2="435.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1741.06" x2="1741.06" y1="279.211" y2="435.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="373.211" y2="373.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="342.211" y2="342.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="310.211" y2="310.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1604.06" y1="279.211" y2="435.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="279.211" y2="279.211"/>
    <line stroke="rgb(255,255,255)" stroke-width="1" x1="1604.06" x2="1741.06" y1="404.211" y2="404.211"/>
  </g>
  <g id="MeasurementClass">
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-133298" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="24" stroke="rgb(0,255,0)" x="604.717" y="371.389">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133298" ObjectName="BBShuiMZ.BB_ShuiMZ_1T_Tmp"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="0" id="ME-133299" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="24" stroke="rgb(0,255,0)" x="604.717" y="339.389">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133299" ObjectName="BBShuiMZ.BB_ShuiMZ_1T_Tap"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-133296" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="24" stroke="rgb(0,255,0)" x="1684.06" y="366.211">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133296" ObjectName="BBShuiMZ.BB_ShuiMZ_2T_Tmp"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="0" id="ME-133297" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="24" stroke="rgb(0,255,0)" x="1684.06" y="334.211">0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133297" ObjectName="BBShuiMZ.BB_ShuiMZ_2T_Tap"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133278" prefix="Ua ">
      <text fill="rgb(255,255,0)" font-family="SimSun" font-size="29" stroke="rgb(255,255,0)" x="1705.88" y="-144.132">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133278" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133276" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="29" stroke="rgb(0,255,0)" x="1705.88" y="-115.132">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133276" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133274" prefix="Uc ">
      <text fill="rgb(255,0,0)" font-family="SimSun" font-size="29" stroke="rgb(255,0,0)" x="1705.88" y="-86.132">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133274" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Uc"/></metadata>
    </g>
    <g MeasureType="3Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133279" prefix="3Uo ">
      <text fill="rgb(255,255,255)" font-family="SimSun" font-size="29" stroke="rgb(255,255,255)" x="1705.88" y="-57.132">3Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133279" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_U"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133277" prefix="Uab ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="1705.88" y="-28.132">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133277" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Uab"/></metadata>
    </g>
    <g MeasureType="Ubc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133275" prefix="Ubc ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="1705.88" y="0.867999999999995">Ubc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133275" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Ubc"/></metadata>
    </g>
    <g MeasureType="Uca" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133273" prefix="Uca ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="1705.88" y="29.868">Uca 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133273" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM_Uca"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133305" prefix="Ua ">
      <text fill="rgb(255,255,0)" font-family="SimSun" font-size="29" stroke="rgb(255,255,0)" x="76.351" y="316.792">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133305" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133303" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="29" stroke="rgb(0,255,0)" x="76.351" y="345.792">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133303" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133301" prefix="Uc ">
      <text fill="rgb(255,0,0)" font-family="SimSun" font-size="29" stroke="rgb(255,0,0)" x="76.351" y="374.792">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133301" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Uc"/></metadata>
    </g>
    <g MeasureType="3Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133306" prefix="3Uo ">
      <text fill="rgb(255,255,255)" font-family="SimSun" font-size="29" stroke="rgb(255,255,255)" x="76.351" y="403.792">3Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133306" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_U"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133304" prefix="Uab ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="76.351" y="432.792">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133304" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Uab"/></metadata>
    </g>
    <g MeasureType="Ubc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133302" prefix="Ubc ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="76.351" y="461.792">Ubc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133302" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Ubc"/></metadata>
    </g>
    <g MeasureType="Uca" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133300" prefix="Uca ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="76.351" y="490.792">Uca 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133300" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM_Uca"/></metadata>
    </g>
    <g MeasureType="Ua" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133312" prefix="Ua ">
      <text fill="rgb(255,255,0)" font-family="SimSun" font-size="29" stroke="rgb(255,255,0)" x="2366.84" y="316.83">Ua 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133312" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Ua"/></metadata>
    </g>
    <g MeasureType="Ub" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133310" prefix="Ub ">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="29" stroke="rgb(0,255,0)" x="2366.84" y="345.83">Ub 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133310" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Ub"/></metadata>
    </g>
    <g MeasureType="Uc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133308" prefix="Uc ">
      <text fill="rgb(255,0,0)" font-family="SimSun" font-size="29" stroke="rgb(255,0,0)" x="2366.84" y="374.83">Uc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133308" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Uc"/></metadata>
    </g>
    <g MeasureType="3Uo" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133313" prefix="3Uo ">
      <text fill="rgb(255,255,255)" font-family="SimSun" font-size="29" stroke="rgb(255,255,255)" x="2366.84" y="403.83">3Uo 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133313" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_U"/></metadata>
    </g>
    <g MeasureType="Uab" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133311" prefix="Uab ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="2366.84" y="432.83">Uab 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133311" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Uab"/></metadata>
    </g>
    <g MeasureType="Ubc" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133309" prefix="Ubc ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="2366.84" y="461.83">Ubc 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133309" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Ubc"/></metadata>
    </g>
    <g MeasureType="Uca" PreSymbol="0" align="1" appendix="" decimal="2" id="ME-133307" prefix="Uca ">
      <text fill="rgb(0,191,255)" font-family="SimSun" font-size="29" stroke="rgb(0,191,255)" x="2366.84" y="490.83">Uca 0.00</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133307" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM_Uca"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133285" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="950.221" y="-91.619">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133285" ObjectName="BBShuiMZ.BB_ShuiMZ_303BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133284" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="950.221" y="-63.619">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133284" ObjectName="BBShuiMZ.BB_ShuiMZ_303BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133286" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="950.221" y="-35.619">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133286" ObjectName="BBShuiMZ.BB_ShuiMZ_303BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133281" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1403.12" y="-99.039">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133281" ObjectName="BBShuiMZ.BB_ShuiMZ_304BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133280" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1403.12" y="-71.039">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133280" ObjectName="BBShuiMZ.BB_ShuiMZ_304BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133282" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1403.12" y="-43.039">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133282" ObjectName="BBShuiMZ.BB_ShuiMZ_304BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133269" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="-24.7711" y="1190.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133269" ObjectName="BBShuiMZ.BB_ShuiMZ_901BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133268" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="-24.7711" y="1218.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133268" ObjectName="BBShuiMZ.BB_ShuiMZ_901BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133270" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="-24.7711" y="1246.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133270" ObjectName="BBShuiMZ.BB_ShuiMZ_901BK_Ia"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133230" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="135.893" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133230" ObjectName="BBShuiMZ.BB_ShuiMZ_921BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133231" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="135.893" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133231" ObjectName="BBShuiMZ.BB_ShuiMZ_921BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133261" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="423.764" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133261" ObjectName="BBShuiMZ.BB_ShuiMZ_903BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133260" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="423.764" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133260" ObjectName="BBShuiMZ.BB_ShuiMZ_903BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133262" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="423.764" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133262" ObjectName="BBShuiMZ.BB_ShuiMZ_903BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133257" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="575.371" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133257" ObjectName="BBShuiMZ.BB_ShuiMZ_904BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133256" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="575.371" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133256" ObjectName="BBShuiMZ.BB_ShuiMZ_904BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133258" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="575.371" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133258" ObjectName="BBShuiMZ.BB_ShuiMZ_904BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133253" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="719.701" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133253" ObjectName="BBShuiMZ.BB_ShuiMZ_905BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133252" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="719.701" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133252" ObjectName="BBShuiMZ.BB_ShuiMZ_905BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133254" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="719.701" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133254" ObjectName="BBShuiMZ.BB_ShuiMZ_905BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133249" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="868.597" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133249" ObjectName="BBShuiMZ.BB_ShuiMZ_906BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133248" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="868.597" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133248" ObjectName="BBShuiMZ.BB_ShuiMZ_906BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133250" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="868.597" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133250" ObjectName="BBShuiMZ.BB_ShuiMZ_906BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133245" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1515.6" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133245" ObjectName="BBShuiMZ.BB_ShuiMZ_908BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133244" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1515.6" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133244" ObjectName="BBShuiMZ.BB_ShuiMZ_908BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133246" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1515.6" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133246" ObjectName="BBShuiMZ.BB_ShuiMZ_908BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133241" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1663.03" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133241" ObjectName="BBShuiMZ.BB_ShuiMZ_910BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133240" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1663.03" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133240" ObjectName="BBShuiMZ.BB_ShuiMZ_910BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133242" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1663.03" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133242" ObjectName="BBShuiMZ.BB_ShuiMZ_910BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133237" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1806.43" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133237" ObjectName="BBShuiMZ.BB_ShuiMZ_911BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133236" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1806.43" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133236" ObjectName="BBShuiMZ.BB_ShuiMZ_911BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133238" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1806.43" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133238" ObjectName="BBShuiMZ.BB_ShuiMZ_911BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133233" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1959.64" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133233" ObjectName="BBShuiMZ.BB_ShuiMZ_912BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133232" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1959.64" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133232" ObjectName="BBShuiMZ.BB_ShuiMZ_912BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133234" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1959.64" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133234" ObjectName="BBShuiMZ.BB_ShuiMZ_912BK_Ia"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133228" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="2252.05" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133228" ObjectName="BBShuiMZ.BB_ShuiMZ_922BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133229" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="2252.05" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133229" ObjectName="BBShuiMZ.BB_ShuiMZ_922BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133265" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="2386.25" y="1193.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133265" ObjectName="BBShuiMZ.BB_ShuiMZ_902BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133264" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="2386.25" y="1221.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133264" ObjectName="BBShuiMZ.BB_ShuiMZ_902BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133266" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="2386.25" y="1249.25">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133266" ObjectName="BBShuiMZ.BB_ShuiMZ_902BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133293" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="655.606" y="198.903">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133293" ObjectName="BBShuiMZ.BB_ShuiMZ_301BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133292" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="655.606" y="226.903">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133292" ObjectName="BBShuiMZ.BB_ShuiMZ_301BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133294" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="655.606" y="254.903">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133294" ObjectName="BBShuiMZ.BB_ShuiMZ_301BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133289" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,0)" x="1672.35" y="200.411">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133289" ObjectName="BBShuiMZ.BB_ShuiMZ_302BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133288" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="27" stroke="rgb(0,255,0)" x="1672.35" y="228.411">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133288" ObjectName="BBShuiMZ.BB_ShuiMZ_302BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133290" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="27" stroke="rgb(255,0,0)" x="1672.35" y="256.411">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133290" ObjectName="BBShuiMZ.BB_ShuiMZ_302BK_Ia"/></metadata>
    </g>
    <g MeasureType="P" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-162835" prefix="">
      <text fill="rgb(255,255,0)" font-family="Sans Serif" font-size="29" stroke="rgb(255,255,0)" x="1185.11" y="848.669">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-162835" ObjectName="BBShuiMZ.BBShuiMZ_900BK_P"/></metadata>
    </g>
    <g MeasureType="Q" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-162836" prefix="">
      <text fill="rgb(0,255,0)" font-family="Sans Serif" font-size="29" stroke="rgb(0,255,0)" x="1185.11" y="877.669">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-162836" ObjectName="BBShuiMZ.BBShuiMZ_900BK_Q"/></metadata>
    </g>
    <g MeasureType="Ia" PreSymbol="0" align="1" appendix="" decimal="1" id="ME-133272" prefix="">
      <text fill="rgb(255,0,0)" font-family="Sans Serif" font-size="29" stroke="rgb(255,0,0)" x="1185.11" y="906.669">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-133272" ObjectName="BBShuiMZ.BB_ShuiMZ_900BK_Ia"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-188154" prefix="">
      <text fill="rgb(255,255,0)" font-family="SimSun" font-size="24" stroke="rgb(255,255,0)" x="-282.183" y="156.267">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-188154" ObjectName="BBShuiMZ.BB_ShuiM_P"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="" decimal="1" id="ME-188155" prefix="">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="24" stroke="rgb(0,255,0)" x="-279.332" y="203.773">0.0</text>
      <metadata><cge:Meas_Ref ObjectID="ME-188155" ObjectName="BBShuiMZ.BB_ShuiM_Q"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="%" decimal="1" id="ME-188156" prefix="">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="24" stroke="rgb(0,255,0)" x="-284.083" y="253.178">0.0%</text>
      <metadata><cge:Meas_Ref ObjectID="ME-188156" ObjectName="BBShuiMZ.BBShuiM_1B_FZL"/></metadata>
    </g>
    <g MeasureType="" PreSymbol="0" appendix="%" decimal="1" id="ME-188157" prefix="">
      <text fill="rgb(0,255,0)" font-family="SimSun" font-size="24" stroke="rgb(0,255,0)" x="-286.933" y="300.684">0.0%</text>
      <metadata><cge:Meas_Ref ObjectID="ME-188157" ObjectName="BBShuiMZ.BBShuiM_2B_FZL"/></metadata>
    </g>
  </g>
  <g id="ConnectiveNodeClass">
    <g>
      <path class="NFkV35" d="M 784.035,76.0709 L 784.035,30.6816" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" ObjectIDND0="27998@1" ObjectIDND1="27980@-1"/></metadata>
    <path d="M 784.035,76.0709 L 784.035,30.6816" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,-106.398 L 784.035,-157.029" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="groundswitch" ObjectIDND0="28001@0" ObjectIDND1="27996@0" ObjectIDND2="28000@0"/></metadata>
    <path d="M 784.035,-106.398 L 784.035,-157.029" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,-12.191 L 784.035,-64.648" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="groundswitch" ObjectIDND0="27996@1" ObjectIDND1="27998@0" ObjectIDND2="27997@0"/></metadata>
    <path d="M 784.035,-12.191 L 784.035,-64.648" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 838.139,-244.636 L 784.249,-244.636" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="27999@0" ObjectIDND1="28001@1" ObjectIDND2="28002@1" ObjectIDND3="34343@1"/></metadata>
    <path d="M 838.139,-244.636 L 784.249,-244.636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 836.562,-127.967 L 784.035,-127.967" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="breaker" ObjectIDND0="28000@0" ObjectIDND1="28001@0" ObjectIDND2="27996@0"/></metadata>
    <path d="M 836.562,-127.967 L 784.035,-127.967" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 838.139,-28.6404 L 784.035,-28.6404" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="breaker" DevType2="switch" ObjectIDND0="27997@0" ObjectIDND1="27996@1" ObjectIDND2="27998@0"/></metadata>
    <path d="M 838.139,-28.6404 L 784.035,-28.6404" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,-329.253 L 676.089,-329.253 L 676.089,-313.288" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="groundswitch" DevType3="ACline" ObjectIDND0="28002@1" ObjectIDND1="28001@1" ObjectIDND2="27999@0" ObjectIDND3="34343@1"/></metadata>
    <path d="M 784.035,-329.253 L 676.089,-329.253 L 676.089,-313.288" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 676.089,-270.415 L 676.089,-252.836" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="28002@0"/></metadata>
    <path d="M 676.089,-270.415 L 676.089,-252.836" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 676.134,-230.458 L 676.134,-213.571" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="28002@0"/></metadata>
    <path d="M 676.134,-230.458 L 676.134,-213.571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 807.546,-316.913 L 784.594,-316.913" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="switch" DevType3="ACline" ObjectIDND0="28001@1" ObjectIDND1="27999@0" ObjectIDND2="28002@1" ObjectIDND3="34343@1"/></metadata>
    <path d="M 807.546,-316.913 L 784.594,-316.913" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,-199.902 L 784.702,-339.565" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="switch" DevType3="ACline" ObjectIDND0="28001@1" ObjectIDND1="27999@0" ObjectIDND2="28002@1" ObjectIDND3="34343@1"/></metadata>
    <path d="M 784.035,-199.902 L 784.702,-339.565" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,76.0709 L 1550.18,30.3009" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" ObjectIDND0="28005@1" ObjectIDND1="27980@-1"/></metadata>
    <path d="M 1550.18,76.0709 L 1550.18,30.3009" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,-12.5717 L 1550.18,-65.029" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="groundswitch" ObjectIDND0="28005@0" ObjectIDND1="28003@1" ObjectIDND2="28004@0"/></metadata>
    <path d="M 1550.18,-12.5717 L 1550.18,-65.029" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,-106.779 L 1550.18,-157.41" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="groundswitch" ObjectIDND0="28003@0" ObjectIDND1="34688@0" ObjectIDND2="28007@0"/></metadata>
    <path d="M 1550.18,-106.779 L 1550.18,-157.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1605.73,-245.017 L 1550.18,-245.017" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="switch" DevType3="ACline" ObjectIDND0="28006@0" ObjectIDND1="34688@1" ObjectIDND2="28008@1" ObjectIDND3="34344@1"/></metadata>
    <path d="M 1605.73,-245.017 L 1550.18,-245.017" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1604.15,-128.348 L 1550.18,-128.348" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="groundswitch" ObjectIDND0="28003@0" ObjectIDND1="34688@0" ObjectIDND2="28007@0"/></metadata>
    <path d="M 1604.15,-128.348 L 1550.18,-128.348" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1605.73,-29.0211 L 1550.18,-29.0211" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="groundswitch" ObjectIDND0="28005@0" ObjectIDND1="28003@1" ObjectIDND2="28004@0"/></metadata>
    <path d="M 1605.73,-29.0211 L 1550.18,-29.0211" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1548.98,-331.542 L 1443.68,-332.743 L 1443.68,-313.669" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="groundswitch" DevType3="ACline" ObjectIDND0="28008@1" ObjectIDND1="34688@1" ObjectIDND2="28006@0" ObjectIDND3="34344@1"/></metadata>
    <path d="M 1548.98,-331.542 L 1443.68,-332.743 L 1443.68,-313.669" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1443.68,-270.796 L 1443.68,-253.217" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="28008@0"/></metadata>
    <path d="M 1443.68,-270.796 L 1443.68,-253.217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1443.72,-230.839 L 1443.72,-213.952" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="28008@0"/></metadata>
    <path d="M 1443.72,-230.839 L 1443.72,-213.952" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1573.77,-332.743 L 1548.98,-331.542" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="switch" DevType2="groundswitch" DevType3="ACline" ObjectIDND0="28008@1" ObjectIDND1="34688@1" ObjectIDND2="28006@0" ObjectIDND3="34344@1"/></metadata>
    <path d="M 1573.77,-332.743 L 1548.98,-331.542" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,-200.283 L 1548.98,-331.542" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="switch" DevType3="ACline" ObjectIDND0="34688@1" ObjectIDND1="28006@0" ObjectIDND2="28008@1" ObjectIDND3="34344@1"/></metadata>
    <path d="M 1550.18,-200.283 L 1548.98,-331.542" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,279.039 L 783.523,313.225" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="transformer2" ObjectIDND0="27990@1" ObjectIDND1="28069@0"/></metadata>
    <path d="M 784.035,279.039 L 783.523,313.225" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,184.505 L 784.035,237.289" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="switch" DevType2="groundswitch" ObjectIDND0="27990@0" ObjectIDND1="27992@0" ObjectIDND2="27991@0"/></metadata>
    <path d="M 784.035,184.505 L 784.035,237.289" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 784.035,141.632 L 784.035,76.0709" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" ObjectIDND0="27992@1" ObjectIDND1="27980@-1"/></metadata>
    <path d="M 784.035,141.632 L 784.035,76.0709" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 837.861,208.503 L 784.035,208.503" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="breaker" DevType2="switch" ObjectIDND0="27991@0" ObjectIDND1="27990@0" ObjectIDND2="27992@0"/></metadata>
    <path d="M 837.861,208.503 L 784.035,208.503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1252.73,76.0709 L 1252.73,129.916" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="bus" ObjectIDND0="27984@0" ObjectIDND1="27983@1" ObjectIDND2="27980@-1"/></metadata>
    <path d="M 1252.73,76.0709 L 1252.73,129.916" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1254.46,301.583 L 1254.46,327.865" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" ObjectIDND0="27985@0" ObjectIDND1="27983@0"/></metadata>
    <path d="M 1254.46,301.583 L 1254.46,327.865" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1252.73,172.788 L 1254.42,279.206" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" ObjectIDND0="27985@0" ObjectIDND1="27983@0"/></metadata>
    <path d="M 1252.73,172.788 L 1254.42,279.206" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1310.87,103.757 L 1252.73,103.757" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="bus" ObjectIDND0="27984@0" ObjectIDND1="27983@1" ObjectIDND2="27980@-1"/></metadata>
    <path d="M 1310.87,103.757 L 1252.73,103.757" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1310.87,195.224 L 1253.09,195.224" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" ObjectIDND0="27985@0" ObjectIDND1="27983@0"/></metadata>
    <path d="M 1310.87,195.224 L 1253.09,195.224" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1329.22,273.442 L 1329.22,248.899 L 1253.94,248.899" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" ObjectIDND0="27985@0" ObjectIDND1="27983@0"/></metadata>
    <path d="M 1329.22,273.442 L 1329.22,248.899 L 1253.94,248.899" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 993.617,76.0709 L 992.653,135.853" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="switch" ObjectIDND0="27980@-1" ObjectIDND1="33871@1"/></metadata>
    <path d="M 993.617,76.0709 L 992.653,135.853" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 992.697,240.354 L 992.697,282.769" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="33871@0"/></metadata>
    <path d="M 992.697,240.354 L 992.697,282.769" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 992.653,179.117 L 992.653,217.976" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" ObjectIDND0="33871@0"/></metadata>
    <path d="M 992.653,179.117 L 992.653,217.976" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,657.994 L 1.55414,668.578" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28015@1" ObjectIDND1="28014@0"/></metadata>
    <path d="M 1.55414,657.994 L 1.55414,668.578" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,700.515 L 1.55414,713.134" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28014@1" ObjectIDND1="28016@0"/></metadata>
    <path d="M 1.55414,700.515 L 1.55414,713.134" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 744.804,421.895 L 783.613,421.895" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="28016@1" ObjectIDND1="28069@2"/></metadata>
    <path d="M 744.804,421.895 L 783.613,421.895" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,184.415 L 1550.18,237.2" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="groundswitch" ObjectIDND0="27995@0" ObjectIDND1="27993@0" ObjectIDND2="27994@0"/></metadata>
    <path d="M 1550.18,184.415 L 1550.18,237.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,278.95 L 1550.28,311.472" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="transformer2" ObjectIDND0="27993@1" ObjectIDND1="28070@0"/></metadata>
    <path d="M 1550.18,278.95 L 1550.28,311.472" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.18,141.542 L 1550.18,76.0709" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="bus" ObjectIDND0="27995@1" ObjectIDND1="27980@-1"/></metadata>
    <path d="M 1550.18,141.542 L 1550.18,76.0709" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1605.1,208.414 L 1550.18,208.414" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="breaker" DevType2="groundswitch" ObjectIDND0="27995@0" ObjectIDND1="27993@0" ObjectIDND2="27994@0"/></metadata>
    <path d="M 1605.1,208.414 L 1550.18,208.414" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1512.04,421.806 L 1550.26,421.806" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="28019@1" ObjectIDND1="28070@2"/></metadata>
    <path d="M 1512.04,421.806 L 1550.26,421.806" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 783.523,336.111 L 863.068,336.235 L 863.068,339.403" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="transformer2" ObjectIDND0="28069@1"/></metadata>
    <path d="M 783.523,336.111 L 863.068,336.235 L 863.068,339.403" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 159.059,657.475 L 159.059,668.059" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28053@1" ObjectIDND1="28052@0"/></metadata>
    <path d="M 159.059,657.475 L 159.059,668.059" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 159.059,699.996 L 159.059,712.615" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28052@1" ObjectIDND1="28054@0"/></metadata>
    <path d="M 159.059,699.996 L 159.059,712.615" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 451.465,668.099 L 451.465,678.683" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28021@1" ObjectIDND1="28020@0"/></metadata>
    <path d="M 451.465,668.099 L 451.465,678.683" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 451.465,710.62 L 451.465,723.239" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28020@1" ObjectIDND1="28022@0"/></metadata>
    <path d="M 451.465,710.62 L 451.465,723.239" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,713.134 L 1.55414,700.515" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28016@0" ObjectIDND1="28014@1"/></metadata>
    <path d="M 1.55414,713.134 L 1.55414,700.515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,668.578 L 1.55414,657.994" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28014@0" ObjectIDND1="28015@1"/></metadata>
    <path d="M 1.55414,668.578 L 1.55414,657.994" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 159.059,641.228 L 159.721,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="28053@0" ObjectIDND1="27981@-1"/></metadata>
    <path d="M 159.059,641.228 L 159.721,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,641.747 L 1.55414,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27981@-1" ObjectIDND1="28015@0"/></metadata>
    <path d="M 1.55414,641.747 L 1.55414,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 142.383,753.841 L 158.665,754.581" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="28055@0" ObjectIDND1="28054@1" ObjectIDND2="28057@0"/></metadata>
    <path d="M 142.383,753.841 L 158.665,754.581" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 177.907,754.581 L 158.665,754.581" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="28055@0" ObjectIDND1="28054@1" ObjectIDND2="28057@0"/></metadata>
    <path d="M 177.907,754.581 L 158.665,754.581" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 159.059,728.973 L 159.266,784.467" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="switch" ObjectIDND0="28054@1" ObjectIDND1="28055@0" ObjectIDND2="28057@0"/></metadata>
    <path d="M 159.059,728.973 L 159.266,784.467" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 158.773,837.719 L 159.582,869.624" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="xcswitch" DevType2="groundswitch" ObjectIDND0="28057@0" ObjectIDND1="28054@1" ObjectIDND2="28055@0"/></metadata>
    <path d="M 158.773,837.719 L 159.582,869.624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 159.582,912.497 L 159.95,987.459" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="capacitor" ObjectIDND0="28057@1" ObjectIDND1="28056@0" ObjectIDND2="34493@0"/></metadata>
    <path d="M 159.582,912.497 L 159.95,987.459" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 142.208,947.495 L 160.34,947.907" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="capacitor" ObjectIDND0="28057@1" ObjectIDND1="28056@0" ObjectIDND2="34493@0"/></metadata>
    <path d="M 142.208,947.495 L 160.34,947.907" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 176.824,947.907 L 160.34,947.907" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="capacitor" ObjectIDND0="28056@0" ObjectIDND1="28057@1" ObjectIDND2="34493@0"/></metadata>
    <path d="M 176.824,947.907 L 160.34,947.907" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 322.548,565.873 L 322.548,655.088" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27981@-1" ObjectIDND1="27986@0"/></metadata>
    <path d="M 322.548,565.873 L 322.548,655.088" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 322.548,706.742 L 322.548,806.764" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27986@1"/></metadata>
    <path d="M 322.548,706.742 L 322.548,806.764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 306.68,751.254 L 322.548,751.254" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27986@1"/></metadata>
    <path d="M 306.68,751.254 L 322.548,751.254" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 322.593,829.142 L 322.593,902.08" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27986@1"/></metadata>
    <path d="M 322.593,829.142 L 322.593,902.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 451.465,651.852 L 451.465,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="28021@0" ObjectIDND1="27981@-1"/></metadata>
    <path d="M 451.465,651.852 L 451.465,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 451.465,739.597 L 451.465,934.725" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="load" ObjectIDND0="28022@1" ObjectIDND1="28023@0" ObjectIDND2="34495@0"/></metadata>
    <path d="M 451.465,739.597 L 451.465,934.725" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 432.248,802.747 L 452.234,803.273" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="load" ObjectIDND0="28022@1" ObjectIDND1="28023@0" ObjectIDND2="34495@0"/></metadata>
    <path d="M 432.248,802.747 L 452.234,803.273" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 466.961,803.273 L 452.234,803.273" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28023@0" ObjectIDND1="28022@1" ObjectIDND2="34495@0"/></metadata>
    <path d="M 466.961,803.273 L 452.234,803.273" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 603.072,666.915 L 603.072,677.499" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28026@1" ObjectIDND1="28024@0"/></metadata>
    <path d="M 603.072,666.915 L 603.072,677.499" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 603.072,709.436 L 603.072,722.055" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28024@1" ObjectIDND1="28027@0"/></metadata>
    <path d="M 603.072,709.436 L 603.072,722.055" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 603.072,650.668 L 603.072,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="28026@0" ObjectIDND1="27981@-1"/></metadata>
    <path d="M 603.072,650.668 L 603.072,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 603.072,738.413 L 603.072,933.541" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28027@1" ObjectIDND1="34496@0" ObjectIDND2="28025@0"/></metadata>
    <path d="M 603.072,738.413 L 603.072,933.541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 583.855,801.563 L 603.841,802.089" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28027@1" ObjectIDND1="34496@0" ObjectIDND2="28025@0"/></metadata>
    <path d="M 583.855,801.563 L 603.841,802.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 618.568,802.089 L 603.841,802.089" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28025@0" ObjectIDND1="28027@1" ObjectIDND2="34496@0"/></metadata>
    <path d="M 618.568,802.089 L 603.841,802.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 747.402,666.915 L 747.402,677.499" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28029@1" ObjectIDND1="28028@0"/></metadata>
    <path d="M 747.402,666.915 L 747.402,677.499" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 747.402,709.436 L 747.402,722.055" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28028@1" ObjectIDND1="28030@0"/></metadata>
    <path d="M 747.402,709.436 L 747.402,722.055" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 747.402,650.668 L 747.402,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="28029@0" ObjectIDND1="27981@-1"/></metadata>
    <path d="M 747.402,650.668 L 747.402,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 747.402,738.413 L 747.402,933.541" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28030@1" ObjectIDND1="34497@0" ObjectIDND2="28031@0"/></metadata>
    <path d="M 747.402,738.413 L 747.402,933.541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 728.186,801.563 L 748.172,802.089" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28030@1" ObjectIDND1="34497@0" ObjectIDND2="28031@0"/></metadata>
    <path d="M 728.186,801.563 L 748.172,802.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 762.899,802.089 L 748.172,802.089" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28031@0" ObjectIDND1="28030@1" ObjectIDND2="34497@0"/></metadata>
    <path d="M 762.899,802.089 L 748.172,802.089" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 896.298,668.918 L 896.298,679.503" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28033@1" ObjectIDND1="28032@0"/></metadata>
    <path d="M 896.298,668.918 L 896.298,679.503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 896.298,711.439 L 896.298,724.059" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28032@1" ObjectIDND1="28034@0"/></metadata>
    <path d="M 896.298,711.439 L 896.298,724.059" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 896.298,652.671 L 896.298,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="bus" ObjectIDND0="28033@0" ObjectIDND1="27981@-1"/></metadata>
    <path d="M 896.298,652.671 L 896.298,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 896.298,740.417 L 896.298,935.545" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28034@1" ObjectIDND1="34498@0" ObjectIDND2="28035@0"/></metadata>
    <path d="M 896.298,740.417 L 896.298,935.545" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 877.082,803.567 L 897.068,804.093" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28034@1" ObjectIDND1="34498@0" ObjectIDND2="28035@0"/></metadata>
    <path d="M 877.082,803.567 L 897.068,804.093" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 911.795,804.093 L 897.068,804.093" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28035@0" ObjectIDND1="28034@1" ObjectIDND2="34498@0"/></metadata>
    <path d="M 911.795,804.093 L 897.068,804.093" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1045.07,654.247 L 1045.07,565.873" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27981@-1" ObjectIDND1="28064@0"/></metadata>
    <path d="M 1045.07,654.247 L 1045.07,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1.55414,729.492 L 1.55414,781.102 L 67.6412,781.102 L 67.6412,579.692 L 55.843,572.881 L 55.843,554.77 L 68.0239,547.737 L 784.035,547.737 L 783.523,394.867" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="28016@1" ObjectIDND1="28069@2"/></metadata>
    <path d="M 1.55414,729.492 L 1.55414,781.102 L 67.6412,781.102 L 67.6412,579.692 L 55.843,572.881 L 55.843,554.77 L 68.0239,547.737 L 784.035,547.737 L 783.523,394.867" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1141.67,670.122 L 1141.67,680.706" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28010@1" ObjectIDND1="28009@0"/></metadata>
    <path d="M 1141.67,670.122 L 1141.67,680.706" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1141.67,712.643 L 1141.67,725.262" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28009@1" ObjectIDND1="28011@0"/></metadata>
    <path d="M 1141.67,712.643 L 1141.67,725.262" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1044.68,737.548 L 1043.84,810.381" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="28064@1"/></metadata>
    <path d="M 1044.68,737.548 L 1043.84,810.381" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2438.7,656.31 L 2438.7,666.895" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28018@1" ObjectIDND1="28017@0"/></metadata>
    <path d="M 2438.7,656.31 L 2438.7,666.895" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2438.7,698.831 L 2438.7,711.451" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28017@1" ObjectIDND1="28019@0"/></metadata>
    <path d="M 2438.7,698.831 L 2438.7,711.451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2281.13,655.792 L 2281.13,666.376" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28059@1" ObjectIDND1="28058@0"/></metadata>
    <path d="M 2281.13,655.792 L 2281.13,666.376" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2281.13,698.313 L 2281.13,710.932" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28058@1" ObjectIDND1="28060@0"/></metadata>
    <path d="M 2281.13,698.313 L 2281.13,710.932" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1988.72,666.416 L 1988.72,677" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28050@1" ObjectIDND1="28048@0"/></metadata>
    <path d="M 1988.72,666.416 L 1988.72,677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1988.72,708.937 L 1988.72,721.556" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28048@1" ObjectIDND1="28051@0"/></metadata>
    <path d="M 1988.72,708.937 L 1988.72,721.556" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2438.7,711.451 L 2438.7,698.831" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28017@1" ObjectIDND1="28019@0"/></metadata>
    <path d="M 2438.7,711.451 L 2438.7,698.831" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2438.7,666.894 L 2438.7,656.31" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28018@1" ObjectIDND1="28017@0"/></metadata>
    <path d="M 2438.7,666.894 L 2438.7,656.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2281.13,639.544 L 2280.47,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28059@0"/></metadata>
    <path d="M 2281.13,639.544 L 2280.47,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2438.7,640.063 L 2438.7,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28018@0"/></metadata>
    <path d="M 2438.7,640.063 L 2438.7,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2297.81,752.158 L 2281.53,752.898" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="switch" ObjectIDND0="28060@1" ObjectIDND1="28061@0" ObjectIDND2="28063@0"/></metadata>
    <path d="M 2297.81,752.158 L 2281.53,752.898" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2262.29,752.898 L 2281.53,752.898" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="switch" ObjectIDND0="28061@0" ObjectIDND1="28060@1" ObjectIDND2="28063@0"/></metadata>
    <path d="M 2262.29,752.898 L 2281.53,752.898" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2281.13,727.289 L 2280.93,782.783" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="switch" ObjectIDND0="28060@1" ObjectIDND1="28061@0" ObjectIDND2="28063@0"/></metadata>
    <path d="M 2281.13,727.289 L 2280.93,782.783" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2280.93,836.033 L 2280.61,867.941" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="groundswitch" DevType2="switch" ObjectIDND0="28060@1" ObjectIDND1="28061@0" ObjectIDND2="28063@0"/></metadata>
    <path d="M 2280.93,836.033 L 2280.61,867.941" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2280.61,910.813 L 2280.24,985.776" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="capacitor" ObjectIDND0="28063@1" ObjectIDND1="28062@0" ObjectIDND2="34494@0"/></metadata>
    <path d="M 2280.61,910.813 L 2280.24,985.776" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2297.98,945.812 L 2279.85,946.224" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="switch" DevType1="groundswitch" DevType2="capacitor" ObjectIDND0="28063@1" ObjectIDND1="28062@0" ObjectIDND2="34494@0"/></metadata>
    <path d="M 2297.98,945.812 L 2279.85,946.224" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2263.37,946.224 L 2279.85,946.224" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="switch" DevType2="capacitor" ObjectIDND0="28062@0" ObjectIDND1="28063@1" ObjectIDND2="34494@0"/></metadata>
    <path d="M 2263.37,946.224 L 2279.85,946.224" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.64,564.19 L 2117.64,653.405" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="27988@0"/></metadata>
    <path d="M 2117.64,564.19 L 2117.64,653.405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.64,705.059 L 2117.64,805.081" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27988@1"/></metadata>
    <path d="M 2117.64,705.059 L 2117.64,805.081" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2117.61,827.458 L 2117.61,900.397" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27988@1"/></metadata>
    <path d="M 2117.61,827.458 L 2117.61,900.397" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2133.51,749.571 L 2117.64,749.571" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="27988@1"/></metadata>
    <path d="M 2133.51,749.571 L 2117.64,749.571" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1988.73,650.169 L 1988.73,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28050@0"/></metadata>
    <path d="M 1988.73,650.169 L 1988.73,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1988.73,737.914 L 1988.73,933.042" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28051@1" ObjectIDND1="34502@0" ObjectIDND2="28049@0"/></metadata>
    <path d="M 1988.73,737.914 L 1988.73,933.042" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 2007.94,801.064 L 1987.96,801.59" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28051@1" ObjectIDND1="34502@0" ObjectIDND2="28049@0"/></metadata>
    <path d="M 2007.94,801.064 L 1987.96,801.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1973.23,801.59 L 1987.96,801.59" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28049@0" ObjectIDND1="28051@1" ObjectIDND2="34502@0"/></metadata>
    <path d="M 1973.23,801.59 L 1987.96,801.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1837.12,665.232 L 1837.12,675.816" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28046@1" ObjectIDND1="28044@0"/></metadata>
    <path d="M 1837.12,665.232 L 1837.12,675.816" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1837.12,707.753 L 1837.12,720.372" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28044@1" ObjectIDND1="28047@0"/></metadata>
    <path d="M 1837.12,707.753 L 1837.12,720.372" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1837.12,648.985 L 1837.12,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28046@0"/></metadata>
    <path d="M 1837.12,648.985 L 1837.12,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1837.12,736.73 L 1837.12,931.858" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28047@1" ObjectIDND1="34501@0" ObjectIDND2="28045@0"/></metadata>
    <path d="M 1837.12,736.73 L 1837.12,931.858" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1856.34,799.88 L 1836.35,800.406" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28047@1" ObjectIDND1="34501@0" ObjectIDND2="28045@0"/></metadata>
    <path d="M 1856.34,799.88 L 1836.35,800.406" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1821.62,800.406 L 1836.35,800.406" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28045@0" ObjectIDND1="28047@1" ObjectIDND2="34501@0"/></metadata>
    <path d="M 1821.62,800.406 L 1836.35,800.406" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1692.79,665.232 L 1692.79,675.816" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28042@1" ObjectIDND1="28040@0"/></metadata>
    <path d="M 1692.79,665.232 L 1692.79,675.816" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1692.79,707.753 L 1692.79,720.372" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28040@1" ObjectIDND1="28043@0"/></metadata>
    <path d="M 1692.79,707.753 L 1692.79,720.372" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1692.79,648.985 L 1692.79,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28042@0"/></metadata>
    <path d="M 1692.79,648.985 L 1692.79,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1692.79,736.73 L 1692.79,931.858" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28043@1" ObjectIDND1="34500@0" ObjectIDND2="28041@0"/></metadata>
    <path d="M 1692.79,736.73 L 1692.79,931.858" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1712,799.88 L 1692.02,800.406" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28043@1" ObjectIDND1="34500@0" ObjectIDND2="28041@0"/></metadata>
    <path d="M 1712,799.88 L 1692.02,800.406" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1677.29,800.406 L 1692.02,800.406" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28041@0" ObjectIDND1="28043@1" ObjectIDND2="34500@0"/></metadata>
    <path d="M 1677.29,800.406 L 1692.02,800.406" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1543.89,667.235 L 1543.89,677.819" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="breaker" ObjectIDND0="28037@1" ObjectIDND1="28036@0"/></metadata>
    <path d="M 1543.89,667.235 L 1543.89,677.819" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1543.89,709.756 L 1543.89,722.375" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="breaker" DevType1="xcswitch" ObjectIDND0="28036@1" ObjectIDND1="28038@0"/></metadata>
    <path d="M 1543.89,709.756 L 1543.89,722.375" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1543.9,650.989 L 1543.9,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28037@0"/></metadata>
    <path d="M 1543.9,650.989 L 1543.9,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1543.89,738.734 L 1543.89,933.862" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28038@1" ObjectIDND1="34499@0" ObjectIDND2="28039@0"/></metadata>
    <path d="M 1543.89,738.734 L 1543.89,933.862" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1563.11,801.884 L 1543.13,802.409" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="load" DevType2="groundswitch" ObjectIDND0="28038@1" ObjectIDND1="34499@0" ObjectIDND2="28039@0"/></metadata>
    <path d="M 1563.11,801.884 L 1543.13,802.409" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1528.4,802.409 L 1543.13,802.409" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="groundswitch" DevType1="xcswitch" DevType2="load" ObjectIDND0="28039@0" ObjectIDND1="28038@1" ObjectIDND2="34499@0"/></metadata>
    <path d="M 1528.4,802.409 L 1543.13,802.409" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1395.12,652.563 L 1395.12,564.19" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28066@0"/></metadata>
    <path d="M 1395.12,652.563 L 1395.12,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1300.13,668.438 L 1300.13,723.578" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="28012@1" ObjectIDND1="28013@0"/></metadata>
    <path d="M 1300.13,668.438 L 1300.13,723.578" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1141.67,565.873 L 1141.67,653.875" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27981@-1" ObjectIDND1="28010@0"/></metadata>
    <path d="M 1141.67,565.873 L 1141.67,653.875" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1300.13,564.19 L 1300.13,652.191" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="bus" DevType1="xcswitch" ObjectIDND0="27982@-1" ObjectIDND1="28012@0"/></metadata>
    <path d="M 1300.13,564.19 L 1300.13,652.191" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1141.67,741.62 L 1141.67,799.297 L 1300.13,799.297 L 1300.13,739.937" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="xcswitch" ObjectIDND0="28011@1" ObjectIDND1="28013@1"/></metadata>
    <path d="M 1141.67,741.62 L 1141.67,799.297 L 1300.13,799.297 L 1300.13,739.937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1395.51,735.864 L 1396.31,808.327" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" ObjectIDND0="28066@1"/></metadata>
    <path d="M 1395.51,735.864 L 1396.31,808.327" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV10" d="M 1550.28,393.114 L 1550.18,520.825 L 2388.09,520.825 L 2388.09,549.728 L 2394.73,556.367 L 2394.73,575.029 L 2389.07,578.296 L 2389.07,763.225 L 2438.7,763.225 L 2438.7,727.809" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="xcswitch" DevType1="transformer2" ObjectIDND0="28019@1" ObjectIDND1="28070@2"/></metadata>
    <path d="M 1550.28,393.114 L 1550.18,520.825 L 2388.09,520.825 L 2388.09,549.728 L 2394.73,556.367 L 2394.73,575.029 L 2389.07,578.296 L 2389.07,763.225 L 2438.7,763.225 L 2438.7,727.809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g>
      <path class="NFkV35" d="M 1550.28,334.358 L 1479.22,334.389 L 1479.22,347.51" stroke-width="2"/>
      <metadata><cge:CN_Ref DevType0="transformer2" ObjectIDND0="28070@1"/></metadata>
    <path d="M 1550.28,334.358 L 1479.22,334.389 L 1479.22,347.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="BusbarSectionClass">
    <g id="BBShuiMZ.BB_ShuiMZ_35kVM">
      <path class="NFkV35" d="M 499.496,76.0709 L 1741.79,76.0709" stroke-width="10"/>
      <metadata><cge:PSR_Ref ObjectID="27980" ObjectName="BBShuiMZ.BB_ShuiMZ_35kVM"/><cge:TPSR_Ref TObjectID="27980"/></metadata>
    <path d="M 499.496,76.0709 L 1741.79,76.0709" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="BBShuiMZ.BB_ShuiMZ_10kVIM">
      <path class="NFkV10" d="M -57.9268,565.873 L 1163.62,565.873" stroke-width="10"/>
      <metadata><cge:PSR_Ref ObjectID="27981" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIM"/><cge:TPSR_Ref TObjectID="27981"/></metadata>
    <path d="M -57.9268,565.873 L 1163.62,565.873" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g id="BBShuiMZ.BB_ShuiMZ_10kVIIM">
      <path class="NFkV10" d="M 2498.12,564.19 L 1276.57,564.19" stroke-width="10"/>
      <metadata><cge:PSR_Ref ObjectID="27982" ObjectName="BBShuiMZ.BB_ShuiMZ_10kVIIM"/><cge:TPSR_Ref TObjectID="27982"/></metadata>
    <path d="M 2498.12,564.19 L 1276.57,564.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="ACLineSegmentClass">
    <g beginPointId="0" endPointId="133285" id="lvmingxian" runFlow="1">
      <path class="NFkV35" d="M 784.702,-428.052 L 784.702,-339.565" stroke-width="2"/>
      <metadata><cge:PSR_Ref ObjectID="34343" ObjectName="34343"/><cge:TPSR_Ref TObjectID="34343_SS-261"/></metadata>
    <path d="M 784.702,-428.052 L 784.702,-339.565" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
    <g beginPointId="0" endPointId="133281" id="35kV.minganxian" runFlow="1">
      <path class="NFkV35" d="M 1548.98,-420.029 L 1548.98,-331.542" stroke-width="2"/>
      <metadata><cge:PSR_Ref ObjectID="34344" ObjectName="34344"/><cge:TPSR_Ref TObjectID="34344_SS-261"/></metadata>
    <path d="M 1548.98,-420.029 L 1548.98,-331.542" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  </g>
  <g id="PowerTransformer2Class">
    <g id="BBShuiMZ.BB_ShuiMZ_1T" primType="transformer2">
      <g id="WD-0">
        <use class="kV35" height="90" transform="translate(753.523,309.111)" width="60" x="0" xlink:href="#Transformer2:两卷变4_0" y="0"/>
        <metadata><cge:PSR_Ref ObjectID="38594"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV10" height="90" transform="translate(753.523,309.111)" width="60" x="0" xlink:href="#Transformer2:两卷变4_1" y="0"/>
        <metadata><cge:PSR_Ref ObjectID="38597"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="28069" ObjectName="BBShuiMZ.BB_ShuiMZ_1T"/><cge:TPSR_Ref TObjectID="28069"/></metadata>
    <rect fill="white" height="90" opacity="0" stroke="white" transform="translate(753.523,309.111)" width="60" x="0" y="0"/></g>
    <g id="BBShuiMZ.BB_ShuiMZ_2T" primType="transformer2">
      <g id="WD-0">
        <use class="kV35" height="90" transform="translate(1520.28,307.358)" width="60" x="0" xlink:href="#Transformer2:两卷变4_0" y="0"/>
        <metadata><cge:PSR_Ref ObjectID="38598"/></metadata>
      </g>
      <g id="WD-1">
        <use class="kV10" height="90" transform="translate(1520.28,307.358)" width="60" x="0" xlink:href="#Transformer2:两卷变4_1" y="0"/>
        <metadata><cge:PSR_Ref ObjectID="38601"/></metadata>
      </g>
      <metadata><cge:PSR_Ref ObjectID="28070" ObjectName="BBShuiMZ.BB_ShuiMZ_2T"/><cge:TPSR_Ref TObjectID="28070"/></metadata>
    <rect fill="white" height="90" opacity="0" stroke="white" transform="translate(1520.28,307.358)" width="60" x="0" y="0"/></g>
  </g>
  <g id="BreakerClass">
    <g id="133043" primType="breaker">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" xlink:href="#Breaker:开关竖1_0" y="-110.398"/>
      <metadata><cge:PSR_Ref ObjectID="27996" ObjectName="BBShuiMZ.BBShuiM_303BK"/>
        <cge:Meas_Ref ObjectID="ME-133043"/><cge:TPSR_Ref TObjectID="27996"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" y="-110.398"/></g>
    <g id="133058" primType="breaker">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" xlink:href="#Breaker:开关竖1_0" y="-110.779"/>
      <metadata><cge:PSR_Ref ObjectID="28003" ObjectName="BBShuiMZ.BBShuiM_304BK"/>
        <cge:Meas_Ref ObjectID="ME-133058"/><cge:TPSR_Ref TObjectID="28003"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" y="-110.779"/></g>
    <g id="133023" primType="breaker">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" xlink:href="#Breaker:开关竖1_0" y="233.289"/>
      <metadata><cge:PSR_Ref ObjectID="27990" ObjectName="BBShuiMZ.BBShuiM_301BK"/>
        <cge:Meas_Ref ObjectID="ME-133023"/><cge:TPSR_Ref TObjectID="27990"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" y="233.289"/></g>
    <g id="133080" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="-7.35991" xlink:href="#Breaker:开关竖1_0" y="665.519"/>
      <metadata><cge:PSR_Ref ObjectID="28014" ObjectName="BBShuiMZ.BBShuiM_901BK"/>
        <cge:Meas_Ref ObjectID="ME-133080"/><cge:TPSR_Ref TObjectID="28014"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="-7.35991" y="665.519"/></g>
    <g id="133033" primType="breaker">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" xlink:href="#Breaker:开关竖1_0" y="233.2"/>
      <metadata><cge:PSR_Ref ObjectID="27993" ObjectName="BBShuiMZ.BBShuiM_302BK"/>
        <cge:Meas_Ref ObjectID="ME-133033"/><cge:TPSR_Ref TObjectID="27993"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" y="233.2"/></g>
    <g id="133188" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="150.145" xlink:href="#Breaker:开关竖1_0" y="664.999"/>
      <metadata><cge:PSR_Ref ObjectID="28052" ObjectName="BBShuiMZ.BBShuiM_921BK"/>
        <cge:Meas_Ref ObjectID="ME-133188"/><cge:TPSR_Ref TObjectID="28052"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="150.145" y="664.999"/></g>
    <g id="133100" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="442.551" xlink:href="#Breaker:开关竖1_0" y="675.624"/>
      <metadata><cge:PSR_Ref ObjectID="28020" ObjectName="BBShuiMZ.BBShuiM_903BK"/>
        <cge:Meas_Ref ObjectID="ME-133100"/><cge:TPSR_Ref TObjectID="28020"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="442.551" y="675.624"/></g>
    <g id="133111" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="594.158" xlink:href="#Breaker:开关竖1_0" y="674.439"/>
      <metadata><cge:PSR_Ref ObjectID="28024" ObjectName="BBShuiMZ.BBShuiM_904BK"/>
        <cge:Meas_Ref ObjectID="ME-133111"/><cge:TPSR_Ref TObjectID="28024"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="594.158" y="674.439"/></g>
    <g id="133122" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="738.488" xlink:href="#Breaker:开关竖1_0" y="674.439"/>
      <metadata><cge:PSR_Ref ObjectID="28028" ObjectName="BBShuiMZ.BBShuiM_905BK"/>
        <cge:Meas_Ref ObjectID="ME-133122"/><cge:TPSR_Ref TObjectID="28028"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="738.488" y="674.439"/></g>
    <g id="133133" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="887.384" xlink:href="#Breaker:开关竖1_0" y="676.443"/>
      <metadata><cge:PSR_Ref ObjectID="28032" ObjectName="BBShuiMZ.BBShuiM_906BK"/>
        <cge:Meas_Ref ObjectID="ME-133133"/><cge:TPSR_Ref TObjectID="28032"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="887.384" y="676.443"/></g>
    <g id="133073" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="1132.75" xlink:href="#Breaker:开关竖1_0" y="677.646"/>
      <metadata><cge:PSR_Ref ObjectID="28009" ObjectName="BBShuiMZ.BBShuiM_900BK"/>
        <cge:Meas_Ref ObjectID="ME-133073"/><cge:TPSR_Ref TObjectID="28009"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.8281" x="1132.75" y="677.646"/></g>
    <g id="133090" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,2447.61,663.835)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28017" ObjectName="BBShuiMZ.BBShuiM_902BK"/>
        <cge:Meas_Ref ObjectID="ME-133090"/><cge:TPSR_Ref TObjectID="28017"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2447.61,663.835)" width="17.8281" x="0" y="0"/></g>
    <g id="133206" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,2290.05,663.316)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28058" ObjectName="BBShuiMZ.BBShuiM_922BK"/>
        <cge:Meas_Ref ObjectID="ME-133206"/><cge:TPSR_Ref TObjectID="28058"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2290.05,663.316)" width="17.8281" x="0" y="0"/></g>
    <g id="133177" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,1997.64,673.94)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28048" ObjectName="BBShuiMZ.BBShuiM_912BK"/>
        <cge:Meas_Ref ObjectID="ME-133177"/><cge:TPSR_Ref TObjectID="28048"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1997.64,673.94)" width="17.8281" x="0" y="0"/></g>
    <g id="133166" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,1846.04,672.756)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28044" ObjectName="BBShuiMZ.BBShuiM_911BK"/>
        <cge:Meas_Ref ObjectID="ME-133166"/><cge:TPSR_Ref TObjectID="28044"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1846.04,672.756)" width="17.8281" x="0" y="0"/></g>
    <g id="133155" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,1701.71,672.756)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28040" ObjectName="BBShuiMZ.BBShuiM_910BK"/>
        <cge:Meas_Ref ObjectID="ME-133155"/><cge:TPSR_Ref TObjectID="28040"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1701.71,672.756)" width="17.8281" x="0" y="0"/></g>
    <g id="133144" primType="breaker">
      <use class="kV10" height="38.2475" transform="matrix(-1,0,0,1,1552.81,674.759)" width="17.8281" x="0" xlink:href="#Breaker:开关竖1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28036" ObjectName="BBShuiMZ.BBShuiM_908BK"/>
        <cge:Meas_Ref ObjectID="ME-133144"/><cge:TPSR_Ref TObjectID="28036"/></metadata>
    <rect fill="white" height="38.2475" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1552.81,674.759)" width="17.8281" x="0" y="0"/></g>
  </g>
  <g id="DisconnectorClass">
    <g id="133053" primType="switch">
      <use class="kV35" height="50" transform="matrix(-1,0,0,-1,784.035,9.1983)" width="20" x="-10" xlink:href="#Disconnector:刀闸_0" y="-25"/>
      <metadata><cge:PSR_Ref ObjectID="27998" ObjectName="BBShuiMZ.BBShuiM_3031SW"/>
        <cge:Meas_Ref ObjectID="ME-133053"/><cge:TPSR_Ref TObjectID="27998"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,784.035,9.1983)" width="20" x="-10" y="-25"/></g>
    <g id="133056" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" xlink:href="#Disconnector:刀闸_0" y="-203.419"/>
      <metadata><cge:PSR_Ref ObjectID="28001" ObjectName="BBShuiMZ.BBShuiM_3033SW"/>
        <cge:Meas_Ref ObjectID="ME-133056"/><cge:TPSR_Ref TObjectID="28001"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" y="-203.419"/></g>
    <g id="133057" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="666.089" xlink:href="#Disconnector:刀闸_0" y="-316.805"/>
      <metadata><cge:PSR_Ref ObjectID="28002" ObjectName="BBShuiMZ.BBShuiM_3039SW"/>
        <cge:Meas_Ref ObjectID="ME-133057"/><cge:TPSR_Ref TObjectID="28002"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="666.089" y="-316.805"/></g>
    <g id="133068" primType="switch">
      <use class="kV35" height="50" transform="matrix(-1,0,0,-1,1550.18,8.8176)" width="20" x="-10" xlink:href="#Disconnector:刀闸_0" y="-25"/>
      <metadata><cge:PSR_Ref ObjectID="28005" ObjectName="BBShuiMZ.BBShuiM_3041SW"/>
        <cge:Meas_Ref ObjectID="ME-133068"/><cge:TPSR_Ref TObjectID="28005"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,1550.18,8.8176)" width="20" x="-10" y="-25"/></g>
    <g id="158679" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" xlink:href="#Disconnector:刀闸_0" y="-203.8"/>
      <metadata><cge:PSR_Ref ObjectID="34688" ObjectName="BBShuiMZ.BBShuiM_3043SW"/>
        <cge:Meas_Ref ObjectID="ME-158679"/><cge:TPSR_Ref TObjectID="34688"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" y="-203.8"/></g>
    <g id="133071" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1433.68" xlink:href="#Disconnector:刀闸_0" y="-317.186"/>
      <metadata><cge:PSR_Ref ObjectID="28008" ObjectName="BBShuiMZ.BBShuiM_3049SW"/>
        <cge:Meas_Ref ObjectID="ME-133071"/><cge:TPSR_Ref TObjectID="28008"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1433.68" y="-317.186"/></g>
    <g id="133032" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" xlink:href="#Disconnector:刀闸_0" y="138.115"/>
      <metadata><cge:PSR_Ref ObjectID="27992" ObjectName="BBShuiMZ.BBShuiM_3011SW"/>
        <cge:Meas_Ref ObjectID="ME-133032"/><cge:TPSR_Ref TObjectID="27992"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="774.035" y="138.115"/></g>
    <g id="133006" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1242.73" xlink:href="#Disconnector:刀闸_0" y="126.399"/>
      <metadata><cge:PSR_Ref ObjectID="27983" ObjectName="BBShuiMZ.BBShuiM_0351SW"/>
        <cge:Meas_Ref ObjectID="ME-133006"/><cge:TPSR_Ref TObjectID="27983"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1242.73" y="126.399"/></g>
    <g id="133089" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="-6.95666" xlink:href="#Disconnector:xc1_0" y="638.836"/>
      <metadata><cge:PSR_Ref ObjectID="28015" ObjectName="BBShuiMZ.BBShuiM_901XC"/>
        <cge:Meas_Ref ObjectID="ME-133089"/><cge:TPSR_Ref TObjectID="28015"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="-6.95666" y="638.836"/></g>
    <g id="133089" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="-8.41351" xlink:href="#Disconnector:xc2_0" y="710.16"/>
      <metadata><cge:PSR_Ref ObjectID="28016" ObjectName="BBShuiMZ.BBShuiM_901XC1"/>
        <cge:Meas_Ref ObjectID="ME-133089"/><cge:TPSR_Ref TObjectID="28016"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="-8.41351" y="710.16"/></g>
    <g id="133042" primType="switch">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" xlink:href="#Disconnector:刀闸_0" y="138.025"/>
      <metadata><cge:PSR_Ref ObjectID="27995" ObjectName="BBShuiMZ.BBShuiM_3021SW"/>
        <cge:Meas_Ref ObjectID="ME-133042"/><cge:TPSR_Ref TObjectID="27995"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1540.18" y="138.025"/></g>
    <g id="133202" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="150.549" xlink:href="#Disconnector:xc1_0" y="638.316"/>
      <metadata><cge:PSR_Ref ObjectID="28053" ObjectName="BBShuiMZ.BBShuiM_921XC"/>
        <cge:Meas_Ref ObjectID="ME-133202"/><cge:TPSR_Ref TObjectID="28053"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="150.549" y="638.316"/></g>
    <g id="133202" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="149.092" xlink:href="#Disconnector:xc2_0" y="709.641"/>
      <metadata><cge:PSR_Ref ObjectID="28054" ObjectName="BBShuiMZ.BBShuiM_921XC1"/>
        <cge:Meas_Ref ObjectID="ME-133202"/><cge:TPSR_Ref TObjectID="28054"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="149.092" y="709.641"/></g>
    <g id="133109" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="442.954" xlink:href="#Disconnector:xc1_0" y="648.941"/>
      <metadata><cge:PSR_Ref ObjectID="28021" ObjectName="BBShuiMZ.BBShuiM_903XC"/>
        <cge:Meas_Ref ObjectID="ME-133109"/><cge:TPSR_Ref TObjectID="28021"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="442.954" y="648.941"/></g>
    <g id="133109" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="441.497" xlink:href="#Disconnector:xc2_0" y="720.265"/>
      <metadata><cge:PSR_Ref ObjectID="28022" ObjectName="BBShuiMZ.BBShuiM_903XC1"/>
        <cge:Meas_Ref ObjectID="ME-133109"/><cge:TPSR_Ref TObjectID="28022"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="441.497" y="720.265"/></g>
    <g id="133205" primType="switch">
      <use class="kV10" height="50" transform="matrix(-1,0,0,-1,159.582,891.014)" width="20" x="-10" xlink:href="#Disconnector:刀闸_0" y="-25"/>
      <metadata><cge:PSR_Ref ObjectID="28057" ObjectName="BBShuiMZ.BBShuiM_9214SW"/>
        <cge:Meas_Ref ObjectID="ME-133205"/><cge:TPSR_Ref TObjectID="28057"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,159.582,891.014)" width="20" x="-10" y="-25"/></g>
    <g id="133009" primType="xcswitch">
      <use class="kV10" height="60" transform="matrix(1,0,0,1,0,0)" width="22" x="311.548" xlink:href="#Disconnector:小车3_0" y="650.79"/>
      <metadata><cge:PSR_Ref ObjectID="27986" ObjectName="BBShuiMZ.BBShuiM_0951XC"/>
        <cge:Meas_Ref ObjectID="ME-133009"/><cge:TPSR_Ref TObjectID="27986"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="22" x="311.548" y="650.79"/></g>
    <g id="133121" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="594.561" xlink:href="#Disconnector:xc1_0" y="647.756"/>
      <metadata><cge:PSR_Ref ObjectID="28026" ObjectName="BBShuiMZ.BBShuiM_904XC"/>
        <cge:Meas_Ref ObjectID="ME-133121"/><cge:TPSR_Ref TObjectID="28026"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="594.561" y="647.756"/></g>
    <g id="133121" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="593.104" xlink:href="#Disconnector:xc2_0" y="719.081"/>
      <metadata><cge:PSR_Ref ObjectID="28027" ObjectName="BBShuiMZ.BBShuiM_904XC1"/>
        <cge:Meas_Ref ObjectID="ME-133121"/><cge:TPSR_Ref TObjectID="28027"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="593.104" y="719.081"/></g>
    <g id="133131" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="738.892" xlink:href="#Disconnector:xc1_0" y="647.756"/>
      <metadata><cge:PSR_Ref ObjectID="28029" ObjectName="BBShuiMZ.BBShuiM_905XC"/>
        <cge:Meas_Ref ObjectID="ME-133131"/><cge:TPSR_Ref TObjectID="28029"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="738.892" y="647.756"/></g>
    <g id="133131" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="737.435" xlink:href="#Disconnector:xc2_0" y="719.081"/>
      <metadata><cge:PSR_Ref ObjectID="28030" ObjectName="BBShuiMZ.BBShuiM_905XC1"/>
        <cge:Meas_Ref ObjectID="ME-133131"/><cge:TPSR_Ref TObjectID="28030"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="737.435" y="719.081"/></g>
    <g id="133142" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="887.787" xlink:href="#Disconnector:xc1_0" y="649.76"/>
      <metadata><cge:PSR_Ref ObjectID="28033" ObjectName="BBShuiMZ.BBShuiM_906XC"/>
        <cge:Meas_Ref ObjectID="ME-133142"/><cge:TPSR_Ref TObjectID="28033"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="887.787" y="649.76"/></g>
    <g id="133142" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="886.331" xlink:href="#Disconnector:xc2_0" y="721.084"/>
      <metadata><cge:PSR_Ref ObjectID="28034" ObjectName="BBShuiMZ.BBShuiM_906XC1"/>
        <cge:Meas_Ref ObjectID="ME-133142"/><cge:TPSR_Ref TObjectID="28034"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="886.331" y="721.084"/></g>
    <g id="133078" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="1133.16" xlink:href="#Disconnector:xc1_0" y="650.963"/>
      <metadata><cge:PSR_Ref ObjectID="28010" ObjectName="BBShuiMZ.BBShuiM_900XC"/>
        <cge:Meas_Ref ObjectID="ME-133078"/><cge:TPSR_Ref TObjectID="28010"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="17.0216" x="1133.16" y="650.963"/></g>
    <g id="133078" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="1131.7" xlink:href="#Disconnector:xc2_0" y="722.288"/>
      <metadata><cge:PSR_Ref ObjectID="28011" ObjectName="BBShuiMZ.BBShuiM_900XC1"/>
        <cge:Meas_Ref ObjectID="ME-133078"/><cge:TPSR_Ref TObjectID="28011"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="19.9353" x="1131.7" y="722.288"/></g>
    <g id="133224" primType="xcswitch">
      <use class="kV10" height="93.8603" transform="matrix(1,0,0,1,0,0)" width="42.1696" x="1023.99" xlink:href="#Disconnector:带熔断器隔离小车_0" y="647.598"/>
      <metadata><cge:PSR_Ref ObjectID="28064" ObjectName="BBShuiMZ.BBShuiM_961XC"/>
        <cge:Meas_Ref ObjectID="ME-133224"/><cge:TPSR_Ref TObjectID="28064"/></metadata>
    <rect fill="white" height="93.8603" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="42.1696" x="1023.99" y="647.598"/></g>
    <g id="133099" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,2436.21,652.152)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28018" ObjectName="BBShuiMZ.BBShuiM_902XC"/>
        <cge:Meas_Ref ObjectID="ME-133099"/><cge:TPSR_Ref TObjectID="28018"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2436.21,652.152)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133099" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,2437.67,723.477)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28019" ObjectName="BBShuiMZ.BBShuiM_902XC1"/>
        <cge:Meas_Ref ObjectID="ME-133099"/><cge:TPSR_Ref TObjectID="28019"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2437.67,723.477)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133220" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,2278.64,651.633)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28059" ObjectName="BBShuiMZ.BBShuiM_922XC"/>
        <cge:Meas_Ref ObjectID="ME-133220"/><cge:TPSR_Ref TObjectID="28059"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2278.64,651.633)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133220" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,2280.1,722.957)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28060" ObjectName="BBShuiMZ.BBShuiM_922XC1"/>
        <cge:Meas_Ref ObjectID="ME-133220"/><cge:TPSR_Ref TObjectID="28060"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2280.1,722.957)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133187" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,1986.24,662.257)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28050" ObjectName="BBShuiMZ.BBShuiM_912XC"/>
        <cge:Meas_Ref ObjectID="ME-133187"/><cge:TPSR_Ref TObjectID="28050"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1986.24,662.257)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133187" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,1987.7,733.582)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28051" ObjectName="BBShuiMZ.BBShuiM_912XC1"/>
        <cge:Meas_Ref ObjectID="ME-133187"/><cge:TPSR_Ref TObjectID="28051"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1987.7,733.582)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133223" primType="switch">
      <use class="kV10" height="50" transform="matrix(1,0,0,-1,2280.61,889.33)" width="20" x="-10" xlink:href="#Disconnector:刀闸_0" y="-25"/>
      <metadata><cge:PSR_Ref ObjectID="28063" ObjectName="BBShuiMZ.BBShuiM_9224SW"/>
        <cge:Meas_Ref ObjectID="ME-133223"/><cge:TPSR_Ref TObjectID="28063"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,-1,2280.61,889.33)" width="20" x="-10" y="-25"/></g>
    <g id="133010" primType="xcswitch">
      <use class="kV10" height="60" transform="matrix(-1,0,0,1,2128.65,649.107)" width="22" x="0" xlink:href="#Disconnector:小车3_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="27988" ObjectName="BBShuiMZ.BBShuiM_0952XC"/>
        <cge:Meas_Ref ObjectID="ME-133010"/><cge:TPSR_Ref TObjectID="27988"/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2128.65,649.107)" width="22" x="0" y="0"/></g>
    <g id="133176" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,1834.63,661.073)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28046" ObjectName="BBShuiMZ.BBShuiM_911XC"/>
        <cge:Meas_Ref ObjectID="ME-133176"/><cge:TPSR_Ref TObjectID="28046"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1834.63,661.073)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133176" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,1836.09,732.397)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28047" ObjectName="BBShuiMZ.BBShuiM_911XC1"/>
        <cge:Meas_Ref ObjectID="ME-133176"/><cge:TPSR_Ref TObjectID="28047"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1836.09,732.397)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133165" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,1690.3,661.073)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28042" ObjectName="BBShuiMZ.BBShuiM_910XC"/>
        <cge:Meas_Ref ObjectID="ME-133165"/><cge:TPSR_Ref TObjectID="28042"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1690.3,661.073)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133165" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,1691.76,732.397)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28043" ObjectName="BBShuiMZ.BBShuiM_910XC1"/>
        <cge:Meas_Ref ObjectID="ME-133165"/><cge:TPSR_Ref TObjectID="28043"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1691.76,732.397)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133153" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,1541.41,663.077)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28037" ObjectName="BBShuiMZ.BBShuiM_908XC"/>
        <cge:Meas_Ref ObjectID="ME-133153"/><cge:TPSR_Ref TObjectID="28037"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1541.41,663.077)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133153" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,1542.86,734.401)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28038" ObjectName="BBShuiMZ.BBShuiM_908XC1"/>
        <cge:Meas_Ref ObjectID="ME-133153"/><cge:TPSR_Ref TObjectID="28038"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1542.86,734.401)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133079" primType="xcswitch">
      <use class="kV10" height="22.2259" transform="matrix(-1,0,0,1,1297.64,664.28)" width="17.0216" x="-11" xlink:href="#Disconnector:xc1_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28012" ObjectName="BBShuiMZ.BBShuiM_9002XC"/>
        <cge:Meas_Ref ObjectID="ME-133079"/><cge:TPSR_Ref TObjectID="28012"/></metadata>
    <rect fill="white" height="22.2259" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1297.64,664.28)" width="17.0216" x="-11" y="-15"/></g>
    <g id="133079" primType="xcswitch">
      <use class="kV10" height="22.3064" transform="matrix(-1,0,0,1,1299.1,735.604)" width="19.9353" x="-11" xlink:href="#Disconnector:xc2_0" y="-15"/>
      <metadata><cge:PSR_Ref ObjectID="28013" ObjectName="BBShuiMZ.BBShuiM_9002XC1"/>
        <cge:Meas_Ref ObjectID="ME-133079"/><cge:TPSR_Ref TObjectID="28013"/></metadata>
    <rect fill="white" height="22.3064" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1299.1,735.604)" width="19.9353" x="-11" y="-15"/></g>
    <g id="133225" primType="xcswitch">
      <use class="kV10" height="93.8603" transform="matrix(-1,0,0,1,1416.21,645.915)" width="42.1696" x="0" xlink:href="#Disconnector:带熔断器隔离小车_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28066" ObjectName="BBShuiMZ.BBShuiM_962XC"/>
        <cge:Meas_Ref ObjectID="ME-133225"/><cge:TPSR_Ref TObjectID="28066"/></metadata>
    <rect fill="white" height="93.8603" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1416.21,645.915)" width="42.1696" x="0" y="0"/></g>
    <g id="182795" primType="switch">
      <use class="kV35" height="50" transform="matrix(-1,0,0,1,1002.65,132.551)" width="23" x="0" xlink:href="#Disconnector:令克_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="33871" ObjectName="BBShuiMZ.BBShuiM_0361SW"/>
        <cge:Meas_Ref ObjectID="ME-182795"/><cge:TPSR_Ref TObjectID="33871"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1002.65,132.551)" width="23" x="0" y="0"/></g>
  </g>
  <g id="GroundDisconnectorClass">
    <g id="133052" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="834" xlink:href="#GroundDisconnector:左刀右地1_0" y="-38.6404"/>
      <metadata><cge:PSR_Ref ObjectID="27997" ObjectName="BBShuiMZ.BBShuiM_30318SW"/>
        <cge:Meas_Ref ObjectID="ME-133052"/><cge:TPSR_Ref TObjectID="27997"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="834" y="-38.6404"/></g>
    <g id="133055" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="832.423" xlink:href="#GroundDisconnector:左刀右地1_0" y="-137.967"/>
      <metadata><cge:PSR_Ref ObjectID="28000" ObjectName="BBShuiMZ.BBShuiM_30338SW"/>
        <cge:Meas_Ref ObjectID="ME-133055"/><cge:TPSR_Ref TObjectID="28000"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="832.423" y="-137.967"/></g>
    <g id="133054" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="834" xlink:href="#GroundDisconnector:左刀右地1_0" y="-254.636"/>
      <metadata><cge:PSR_Ref ObjectID="27999" ObjectName="BBShuiMZ.BBShuiM_30337SW"/>
        <cge:Meas_Ref ObjectID="ME-133054"/><cge:TPSR_Ref TObjectID="27999"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="834" y="-254.636"/></g>
    <g id="133067" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1601.59" xlink:href="#GroundDisconnector:左刀右地1_0" y="-39.0211"/>
      <metadata><cge:PSR_Ref ObjectID="28004" ObjectName="BBShuiMZ.BBShuiM_30418SW"/>
        <cge:Meas_Ref ObjectID="ME-133067"/><cge:TPSR_Ref TObjectID="28004"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1601.59" y="-39.0211"/></g>
    <g id="133070" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1600.01" xlink:href="#GroundDisconnector:左刀右地1_0" y="-138.348"/>
      <metadata><cge:PSR_Ref ObjectID="28007" ObjectName="BBShuiMZ.BBShuiM_30438SW"/>
        <cge:Meas_Ref ObjectID="ME-133070"/><cge:TPSR_Ref TObjectID="28007"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1600.01" y="-138.348"/></g>
    <g id="133069" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1601.59" xlink:href="#GroundDisconnector:左刀右地1_0" y="-255.017"/>
      <metadata><cge:PSR_Ref ObjectID="28006" ObjectName="BBShuiMZ.BBShuiM_30437SW"/>
        <cge:Meas_Ref ObjectID="ME-133069"/><cge:TPSR_Ref TObjectID="28006"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1601.59" y="-255.017"/></g>
    <g id="133031" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="833.722" xlink:href="#GroundDisconnector:左刀右地1_0" y="198.503"/>
      <metadata><cge:PSR_Ref ObjectID="27991" ObjectName="BBShuiMZ.BBShuiM_30118SW"/>
        <cge:Meas_Ref ObjectID="ME-133031"/><cge:TPSR_Ref TObjectID="27991"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="833.722" y="198.503"/></g>
    <g id="133007" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1306.73" xlink:href="#GroundDisconnector:左刀右地1_0" y="93.7571"/>
      <metadata><cge:PSR_Ref ObjectID="27984" ObjectName="BBShuiMZ.BBShuiM_03517SW"/>
        <cge:Meas_Ref ObjectID="ME-133007"/><cge:TPSR_Ref TObjectID="27984"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1306.73" y="93.7571"/></g>
    <g id="133008" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1306.73" xlink:href="#GroundDisconnector:左刀右地1_0" y="185.224"/>
      <metadata><cge:PSR_Ref ObjectID="27985" ObjectName="BBShuiMZ.BBShuiM_03518SW"/>
        <cge:Meas_Ref ObjectID="ME-133008"/><cge:TPSR_Ref TObjectID="27985"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1306.73" y="185.224"/></g>
    <g id="133041" primType="groundswitch">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="50" x="1600.96" xlink:href="#GroundDisconnector:左刀右地1_0" y="198.414"/>
      <metadata><cge:PSR_Ref ObjectID="27994" ObjectName="BBShuiMZ.BBShuiM_30218SW"/>
        <cge:Meas_Ref ObjectID="ME-133041"/><cge:TPSR_Ref TObjectID="27994"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="50" x="1600.96" y="198.414"/></g>
    <g id="133203" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,173.768,764.581)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28055" ObjectName="BBShuiMZ.BBShuiM_92137SW"/>
        <cge:Meas_Ref ObjectID="ME-133203"/><cge:TPSR_Ref TObjectID="28055"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,173.768,764.581)" width="50" x="0" y="0"/></g>
    <g id="133204" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,172.685,957.907)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28056" ObjectName="BBShuiMZ.BBShuiM_92147SW"/>
        <cge:Meas_Ref ObjectID="ME-133204"/><cge:TPSR_Ref TObjectID="28056"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,172.685,957.907)" width="50" x="0" y="0"/></g>
    <g id="133110" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,462.822,813.273)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28023" ObjectName="BBShuiMZ.BBShuiM_90337SW"/>
        <cge:Meas_Ref ObjectID="ME-133110"/><cge:TPSR_Ref TObjectID="28023"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,462.822,813.273)" width="50" x="0" y="0"/></g>
    <g id="133120" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,614.429,812.089)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28025" ObjectName="BBShuiMZ.BBShuiM_90437SW"/>
        <cge:Meas_Ref ObjectID="ME-133120"/><cge:TPSR_Ref TObjectID="28025"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,614.429,812.089)" width="50" x="0" y="0"/></g>
    <g id="133132" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,758.76,812.089)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28031" ObjectName="BBShuiMZ.BBShuiM_90537SW"/>
        <cge:Meas_Ref ObjectID="ME-133132"/><cge:TPSR_Ref TObjectID="28031"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,758.76,812.089)" width="50" x="0" y="0"/></g>
    <g id="133143" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(1,0,0,-1,907.656,814.093)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28035" ObjectName="BBShuiMZ.BBShuiM_90637SW"/>
        <cge:Meas_Ref ObjectID="ME-133143"/><cge:TPSR_Ref TObjectID="28035"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,-1,907.656,814.093)" width="50" x="0" y="0"/></g>
    <g id="133221" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,2266.43,762.898)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28061" ObjectName="BBShuiMZ.BBShuiM_92237SW"/>
        <cge:Meas_Ref ObjectID="ME-133221"/><cge:TPSR_Ref TObjectID="28061"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,2266.43,762.898)" width="50" x="0" y="0"/></g>
    <g id="133222" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,2267.51,956.224)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28062" ObjectName="BBShuiMZ.BBShuiM_92247SW"/>
        <cge:Meas_Ref ObjectID="ME-133222"/><cge:TPSR_Ref TObjectID="28062"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,2267.51,956.224)" width="50" x="0" y="0"/></g>
    <g id="133186" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,1977.37,811.59)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28049" ObjectName="BBShuiMZ.BBShuiM_91237SW"/>
        <cge:Meas_Ref ObjectID="ME-133186"/><cge:TPSR_Ref TObjectID="28049"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,1977.37,811.59)" width="50" x="0" y="0"/></g>
    <g id="133175" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,1825.76,810.405)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28045" ObjectName="BBShuiMZ.BBShuiM_91137SW"/>
        <cge:Meas_Ref ObjectID="ME-133175"/><cge:TPSR_Ref TObjectID="28045"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,1825.76,810.405)" width="50" x="0" y="0"/></g>
    <g id="133164" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,1681.43,810.405)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28041" ObjectName="BBShuiMZ.BBShuiM_91037SW"/>
        <cge:Meas_Ref ObjectID="ME-133164"/><cge:TPSR_Ref TObjectID="28041"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,1681.43,810.405)" width="50" x="0" y="0"/></g>
    <g id="133154" primType="groundswitch">
      <use class="kV10" height="20" transform="matrix(-1,0,0,-1,1532.54,812.409)" width="50" x="0" xlink:href="#GroundDisconnector:左刀右地1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="28039" ObjectName="BBShuiMZ.BBShuiM_90837SW"/>
        <cge:Meas_Ref ObjectID="ME-133154"/><cge:TPSR_Ref TObjectID="28039"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,-1,1532.54,812.409)" width="50" x="0" y="0"/></g>
  </g>
  <g id="DynamicPointClass">
    <g id="1868" primType="dynp_bay">
      <use class="AllLevel" height="40" transform="matrix(1,0,0,1,0,0)" width="120" x="-436.273" xlink:href="#DynamicPoint:bayArea_0" y="-54.4729"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-1868"/></metadata>
    <rect fill="white" height="40" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="120" x="-436.273" y="-54.4729"/></g>
    <g id="1874" primType="dynp_bay">
      <use class="AllLevel" height="40" transform="matrix(1,0,0,1,0,0)" width="120" x="-275.42" xlink:href="#DynamicPoint:bayArea_0" y="-58.1426"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-1874"/></metadata>
    <rect fill="white" height="40" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="120" x="-275.42" y="-58.1426"/></g>
    <g id="1881" primType="dynp_bay">
      <use class="AllLevel" height="40" transform="matrix(1,0,0,1,0,0)" width="120" x="-436.823" xlink:href="#DynamicPoint:bayArea_0" y="20.7581"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-1881"/></metadata>
    <rect fill="white" height="40" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="120" x="-436.823" y="20.7581"/></g>
    <g id="1871" primType="dynp_bay">
      <use class="AllLevel" height="40" transform="matrix(1,0,0,1,0,0)" width="120" x="-273.823" xlink:href="#DynamicPoint:bayArea_0" y="20.7581"/>
      <metadata><cge:PSR_Ref ObjectID="0"/>
        <cge:Meas_Ref ObjectID="ME-1871"/></metadata>
    <rect fill="white" height="40" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="120" x="-273.823" y="20.7581"/></g>
  </g>
  <g id="EnergyConsumerClass">
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="441.465" xlink:href="#EnergyConsumer:load2_0" y="930.877"/>
      <metadata><cge:PSR_Ref ObjectID="34495" ObjectName="BBShuiMZ.BB_ShuiMZ_903Ld_S"/><cge:TPSR_Ref TObjectID="34495"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="441.465" y="930.877"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="593.072" xlink:href="#EnergyConsumer:load2_0" y="929.692"/>
      <metadata><cge:PSR_Ref ObjectID="34496" ObjectName="BBShuiMZ.BB_ShuiMZ_904Ld_S"/><cge:TPSR_Ref TObjectID="34496"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="593.072" y="929.692"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="737.402" xlink:href="#EnergyConsumer:load2_0" y="929.692"/>
      <metadata><cge:PSR_Ref ObjectID="34497" ObjectName="BBShuiMZ.BB_ShuiMZ_905Ld_S"/><cge:TPSR_Ref TObjectID="34497"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="737.402" y="929.692"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(1,0,0,1,0,0)" width="20" x="886.298" xlink:href="#EnergyConsumer:load2_0" y="931.696"/>
      <metadata><cge:PSR_Ref ObjectID="34498" ObjectName="BBShuiMZ.BB_ShuiMZ_906Ld_S"/><cge:TPSR_Ref TObjectID="34498"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="886.298" y="931.696"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(-1,0,0,1,1998.73,929.193)" width="20" x="0" xlink:href="#EnergyConsumer:load2_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="34502" ObjectName="BBShuiMZ.BB_ShuiMZ_912Ld_S"/><cge:TPSR_Ref TObjectID="34502"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1998.73,929.193)" width="20" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(-1,0,0,1,1847.12,928.009)" width="20" x="0" xlink:href="#EnergyConsumer:load2_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="34501" ObjectName="BBShuiMZ.BB_ShuiMZ_911Ld_S"/><cge:TPSR_Ref TObjectID="34501"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1847.12,928.009)" width="20" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(-1,0,0,1,1702.79,928.009)" width="20" x="0" xlink:href="#EnergyConsumer:load2_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="34500" ObjectName="BBShuiMZ.BB_ShuiMZ_910Ld_S"/><cge:TPSR_Ref TObjectID="34500"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1702.79,928.009)" width="20" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="NFkV10" height="50" transform="matrix(-1,0,0,1,1553.9,930.012)" width="20" x="0" xlink:href="#EnergyConsumer:load2_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="34499" ObjectName="BBShuiMZ.BB_ShuiMZ_908Ld_S"/><cge:TPSR_Ref TObjectID="34499"/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1553.9,930.012)" width="20" x="0" y="0"/></g>
  </g>
  <g id="CapacitorClass">
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="23" x="148.45" xlink:href="#Capacitor:shape0_0" y="983.526"/>
      <metadata><cge:PSR_Ref ObjectID="34493" ObjectName="BBShuiMZ.BB_ShuiMZ_921C_S"/><cge:TPSR_Ref TObjectID="34493"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="23" x="148.45" y="983.526"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="23" x="2268.74" xlink:href="#Capacitor:shape0_0" y="981.843"/>
      <metadata><cge:PSR_Ref ObjectID="34494" ObjectName="BBShuiMZ.BB_ShuiMZ_922C_S"/><cge:TPSR_Ref TObjectID="34494"/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="23" x="2268.74" y="981.843"/></g>
  </g>
  <g id="VoltageTransformerClass">
    <g id="DEV-0">
      <use class="kV35" height="50" transform="matrix(1,0,0,1,0,0)" width="80" x="1214.46" xlink:href="#PT:电压互感器_0" y="323.814"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="80" x="1214.46" y="323.814"/></g>
    <g id="DEV-0">
      <use class="kV10" height="50" transform="matrix(1,0,0,1,0,0)" width="80" x="282.593" xlink:href="#PT:电压互感器_0" y="898.028"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="80" x="282.593" y="898.028"/></g>
    <g id="DEV-0">
      <use class="kV10" height="50" transform="matrix(-1,0,0,1,2157.6,896.345)" width="80" x="0" xlink:href="#PT:电压互感器_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="50" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2157.6,896.345)" width="80" x="0" y="0"/></g>
  </g>
  <g id="ArresterClass">
    <g id="DEV-0">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="803.625" xlink:href="#Arrester:避雷器横1_0" y="-326.913"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="803.625" y="-326.913"/></g>
    <g id="DEV-0">
      <use class="kV35" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="1569.85" xlink:href="#Arrester:避雷器横1_0" y="-342.743"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="1569.85" y="-342.743"/></g>
    <g id="DEV-0">
      <use class="kV35" height="60" transform="matrix(1,0,0,1,0,0)" width="20" x="1319.22" xlink:href="#Arrester:避雷器竖_0" y="269.508"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="1319.22" y="269.508"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="688.725" xlink:href="#Arrester:避雷器横_0" y="411.895"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="688.725" y="411.895"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="1455.96" xlink:href="#Arrester:避雷器横_0" y="411.806"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="1455.96" y="411.806"/></g>
    <g id="DEV-0">
      <use class="kV35" height="60" transform="matrix(1,0,0,1,0,0)" width="20" x="853.068" xlink:href="#Arrester:避雷器竖_0" y="335.469"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="853.068" y="335.469"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="86.3047" xlink:href="#Arrester:避雷器横_0" y="743.841"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="86.3047" y="743.841"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="86.1291" xlink:href="#Arrester:避雷器横_0" y="937.495"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="86.1291" y="937.495"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="250.601" xlink:href="#Arrester:避雷器横_0" y="741.254"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="250.601" y="741.254"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="376.17" xlink:href="#Arrester:避雷器横_0" y="792.747"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="376.17" y="792.747"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="527.777" xlink:href="#Arrester:避雷器横_0" y="791.563"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="527.777" y="791.563"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="672.107" xlink:href="#Arrester:避雷器横_0" y="791.563"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="672.107" y="791.563"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(1,0,0,1,0,0)" width="60" x="821.003" xlink:href="#Arrester:避雷器横_0" y="793.567"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="60" x="821.003" y="793.567"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,2353.89,742.158)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2353.89,742.158)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,2354.06,935.812)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2354.06,935.812)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,2189.59,739.571)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2189.59,739.571)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,2064.02,791.064)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2064.02,791.064)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,1912.42,789.88)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1912.42,789.88)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,1768.09,789.88)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1768.09,789.88)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="20" transform="matrix(-1,0,0,1,1619.19,791.883)" width="60" x="0" xlink:href="#Arrester:避雷器横_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="20" opacity="0" stroke="white" transform="matrix(-1,0,0,1,1619.19,791.883)" width="60" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV35" height="60" transform="matrix(1,0,0,1,0,0)" width="16.3107" x="1471.06" xlink:href="#Arrester:避雷器竖_0" y="338.768"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="16.3107" x="1471.06" y="338.768"/></g>
  </g>
  <g id="OtherClass">
    <g id="DEV-0">
      <use class="kV35" height="28" transform="matrix(0,1,-1,0,688.884,-216.071)" width="50" x="-1" xlink:href="#Other:shape79_0" y="-1"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="28" opacity="0" stroke="white" transform="matrix(0,1,-1,0,688.884,-216.071)" width="50" x="-1" y="-1"/></g>
    <g id="DEV-0">
      <use class="kV35" height="30" transform="matrix(1,0,0,1,0,0)" width="10" x="670.957" xlink:href="#Other:Linker_0" y="-256.34"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10" x="670.957" y="-256.34"/></g>
    <g id="DEV-0">
      <use class="kV35" height="28" transform="matrix(0,1,-1,0,1456.47,-216.452)" width="50" x="-1" xlink:href="#Other:shape79_0" y="-1"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="28" opacity="0" stroke="white" transform="matrix(0,1,-1,0,1456.47,-216.452)" width="50" x="-1" y="-1"/></g>
    <g id="DEV-0">
      <use class="kV35" height="30" transform="matrix(1,0,0,1,0,0)" width="10" x="1438.55" xlink:href="#Other:Linker_0" y="-256.721"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10" x="1438.55" y="-256.721"/></g>
    <g id="DEV-0">
      <use class="kV35" height="30" transform="matrix(1,0,0,1,0,0)" width="10" x="1249.28" xlink:href="#Other:Linker_0" y="275.702"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10" x="1249.28" y="275.702"/></g>
    <g id="DEV-0">
      <use class="kV35" height="30" transform="matrix(1,0,0,1,0,0)" width="10" x="987.52" xlink:href="#Other:Linker_0" y="214.472"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10" x="987.52" y="214.472"/></g>
    <g id="DEV-0">
      <use class="kV35" height="23" transform="matrix(0,1,-1,0,1003.2,278.95)" width="37" x="0" xlink:href="#Other:shape49_0" y="-1"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="23" opacity="0" stroke="white" transform="matrix(0,1,-1,0,1003.2,278.95)" width="37" x="0" y="-1"/></g>
    <g id="DEV-0">
      <use class="kV10" height="60" transform="matrix(1,0,0,1,0,0)" width="20" x="149.266" xlink:href="#Other:电缆1_0" y="780.914"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="20" x="149.266" y="780.914"/></g>
    <g id="DEV-0">
      <use class="kV10" height="30" transform="matrix(1,0,0,1,0,0)" width="10" x="317.416" xlink:href="#Other:Linker_0" y="803.26"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="10" x="317.416" y="803.26"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56.0264" transform="matrix(0,1,-1,0,1037.82,803.333)" width="68.2793" x="0" xlink:href="#Other:shape49_0" y="-34.0264"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56.0264" opacity="0" stroke="white" transform="matrix(0,1,-1,0,1037.82,803.333)" width="68.2793" x="0" y="-34.0264"/></g>
    <g id="DEV-0">
      <use class="kV10" height="60" transform="matrix(-1,0,0,1,2290.93,779.23)" width="20" x="0" xlink:href="#Other:电缆1_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="60" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2290.93,779.23)" width="20" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="30" transform="matrix(-1,0,0,1,2122.78,801.576)" width="10" x="0" xlink:href="#Other:Linker_0" y="0"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="30" opacity="0" stroke="white" transform="matrix(-1,0,0,1,2122.78,801.576)" width="10" x="0" y="0"/></g>
    <g id="DEV-0">
      <use class="kV10" height="56.0264" transform="matrix(0,1,-1,0,1390.29,801.278)" width="68.2793" x="0" xlink:href="#Other:shape49_0" y="-34.0264"/>
      <metadata><cge:PSR_Ref ObjectID="0" ObjectName=""/></metadata>
    <rect fill="white" height="56.0264" opacity="0" stroke="white" transform="matrix(0,1,-1,0,1390.29,801.278)" width="68.2793" x="0" y="-34.0264"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="35" transform="matrix(1,0,0,1,0,0)" width="35" x="435.534" xlink:href="#Other:自愈_0" y="912.538"/>
    <metadata/><rect fill="white" height="35" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35" x="435.534" y="912.538"/></g>
    <g id="DEV-0">
      <use class="BuDaiDian" height="35" transform="matrix(1,0,0,1,0,0)" width="35" x="428.828" xlink:href="#Other:联络_0" y="559.107"/>
    <metadata/><rect fill="white" height="35" opacity="0" stroke="white" transform="matrix(1,0,0,1,0,0)" width="35" x="428.828" y="559.107"/></g>
  </g>
  <g id="Base_MotifButtonClass">
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
    <g>
      
    <metadata/></g>
  </g>
  <g id="TextClass">
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="24" stroke="rgb(255,255,255)" x="-341.887" y="-282.001">至中调220kV厂站图</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-450.345" y="115.331">类别</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-373.614" y="114.826">名称</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-273.5" y="115.331">值</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-450.345" y="179.003">总加</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-386.114" y="161.572">总有功</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-386.114" y="209.932">总无功</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-437.845" y="260.835">负</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-363.114" y="256.917">1B</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-363.114" y="304.317">2B</text>
    <text fill="rgb(0,136,0)" font-family="华文黑体" font-size="35" stroke="rgb(0,136,0)" x="-322.919" y="-359.956">水鸣站</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-423.417" y="-283.858">电话</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-437.845" y="303.018">载</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-437.845" y="345.2">率</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-448.176" y="-52.8509">公共间隔</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-279.547" y="-53.3159">母线间隔</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-454.238" y="21.8841">备自投间隔</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-277.906" y="22.3341">站变间隔</text>
    <text fill="rgb(255,255,255)" font-family="方正黑体" font-size="29" stroke="rgb(255,255,255)" x="-375.581" y="-119.613">间隔光字牌</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="745.757" y="-440.692">绿鸣线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1527.84" y="-452.816">鸣安线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="363.639" y="82.0454">35kV母线</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="531.717" y="432.389">绕温:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="531.717" y="339.389">档位:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="531.717" y="371.389">油温1:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="531.717" y="401.389">油温2:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="540.797" y="309.594">1B 10MVA</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1611.06" y="427.211">绕温:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1611.06" y="334.211">档位:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1611.06" y="366.211">油温1:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1611.06" y="396.211">油温2:</text>
    <text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1620.14" y="304.416">2B 10MVA</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="10.2324" y="890.817"> #1</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="10.2324" y="916.817">主变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="414.87" y="1036.21">合光线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="569.956" y="1036.21">新丰线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="715.733" y="1036.21">新和线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="863.741" y="1036.21">黎旺线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1169.16" y="1035.53">母线分段</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="2418.03" y="1007.35"> #2</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="2418.03" y="1033.35">主变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1502.93" y="1035.53">江正线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1637.02" y="1035.53">水鸣圩镇线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1809.79" y="1035.53">备用I</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1951.8" y="1035.53">备用II</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1293.19" y="542.595">10kVII段母线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="978.504" y="543.627">10kVI段母线</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1064.14" y="703.528">0961</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="-76.398">303</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="-169.419">3033</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="18.1983">3031</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="690.089" y="-282.805">3039</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="868.492" y="-206.292">30337</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="873.604" y="-92.967">30338</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="891.902" y="-0.328952000000001">30318</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1587.59" y="5.9789">30418</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1563.17" y="-169.799">3043</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1586.01" y="-93.348">30438</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1587.59" y="-210.017">30437</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1568.19" y="47.9161">3041</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1457.68" y="-283.185">3049</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1558.15" y="-55.0412">304</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="267.289">301</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="172.115">3011</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="819.722" y="243.503">30118</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1292.73" y="230.224">03518</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1266.73" y="160.399">0351</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1350.96" y="138.757">03517</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1563.69" y="267.2">302</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1563.69" y="172.026">3021</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1586.96" y="243.414">30218</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="14.8407" y="693.642">901</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="171.973" y="693.123">921</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="187.919" y="792.798">92137</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="158.685" y="982.907">92147</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="173.582" y="900.014">9214</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="337.548" y="689.79">0951</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="464.379" y="703.748">903</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="448.822" y="838.273">90337</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="615.986" y="702.563">904</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="600.429" y="837.089">90437</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="760.316" y="702.563">905</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="744.76" y="837.089">90537</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="909.212" y="704.567">906</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="893.656" y="839.093">90637</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1154.58" y="705.77">900</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1307.94" y="701.372">9002</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1419.21" y="701.845">0962</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1556.81" y="702.883">908</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1468.54" y="837.409">90837</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1705.71" y="700.88">910</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1617.43" y="835.405">91037</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1856.81" y="705.959">911</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1761.76" y="835.405">91137</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2001.64" y="702.064">912</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1913.37" y="836.59">91237</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2137.97" y="715.597">0952</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2294.05" y="691.44">922</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2202.43" y="787.898">92237</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2203.51" y="981.224">92247</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2294.61" y="898.33">9224</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2451.18" y="691.959">902</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1013.67" y="166.551">0361</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="35" stroke="rgb(255,255,255)" x="-432.908" y="473.176">水鸣站具备远方</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="35" stroke="rgb(255,255,255)" x="-432.908" y="511.176">操作功能设备清单</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="29" stroke="rgb(255,255,255)" x="-366.355" y="632.264">AVC</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="690.282" y="-403.5">T长岗岭</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="1204.32" y="395.683">35kVI段</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="1204.32" y="425.683">母线PT</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="938.503" y="349.393">35kV3号</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="938.503" y="379.393">   站变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="1426.33" y="321.095">2号主变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="814.097" y="324.082">1号主变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="270.023" y="1002.21">10kVI段</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="270.023" y="1032.21">母线PT</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="2060.23" y="1001.53">10kVII段</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="2060.23" y="1031.53">母线PT</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="1340.08" y="1003.94">10kV2号</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="1340.08" y="1033.94">   站变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="987.358" y="1003.94">10kV1号</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="987.358" y="1033.94">   站变</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="102.289" y="1045.41">10kV1号</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="102.289" y="1075.41">电容</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="102.289" y="1105.41">2400kvar</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="2228.85" y="1045.41">10kV2号</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="2228.85" y="1075.41">电容</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="27" stroke="rgb(255,255,255)" x="2228.85" y="1105.41">2400kvar</text>
    <text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="714.282" y="-371.786">T绿珠</text>
  </g>
  <g id="LinkPointClass">
    <g href="yl_OS2_联系方式_博白电话.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="50" stroke-width="1" width="90" x="-440.385" y="-322.2"/></g>
    <g href="yl_OS2_博白县调_索引.fac.svg" style="fill-opacity:0;stroke-opacity:0"><rect height="96.097" stroke-width="1" width="249.852" x="-418.819" y="-422.451"/></g>
    <g href="yl_35kV水鸣站GG间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-448.176" y="-52.8509">公共间隔</text></g>
    <g href="yl_35kV水鸣站MX间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-279.547" y="-53.3159">母线间隔</text></g>
    <g href="yl_35kV水鸣站ZB间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="方正黑体" font-size="32" stroke="rgb(255,255,255)" x="-277.906" y="22.3341">站变间隔</text></g>
    <g href="yl_35kV_T接_岗绿线绿鸣线.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="745.757" y="-440.692">绿鸣线</text></g>
    <g href="yl_bb_35kV_永安站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="1527.84" y="-452.816">鸣安线</text></g>
    <g href="yl_35kV水鸣站_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="540.797" y="309.594">1B 10MVA</text></g>
    <g href="yl_35kV水鸣站_2主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="SimSun" font-size="24" stroke="rgb(255,255,255)" x="1620.14" y="304.416">2B 10MVA</text></g>
    <g href="yl_35kV水鸣站303间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="-76.398">303</text></g>
    <g href="yl_35kV水鸣站304间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1558.15" y="-55.0412">304</text></g>
    <g href="yl_35kV水鸣站_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="797.962" y="267.289">301</text></g>
    <g href="yl_35kV水鸣站_2主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1563.69" y="267.2">302</text></g>
    <g href="yl_35kV水鸣站_1主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="14.8407" y="693.642">901</text></g>
    <g href="yl_35kV水鸣站921间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="171.973" y="693.123">921</text></g>
    <g href="yl_35kV水鸣站903间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="464.379" y="703.748">903</text></g>
    <g href="yl_35kV水鸣站904间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="615.986" y="702.563">904</text></g>
    <g href="yl_35kV水鸣站905间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="760.316" y="702.563">905</text></g>
    <g href="yl_35kV水鸣站906间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="909.212" y="704.567">906</text></g>
    <g href="yl_35kV水鸣站MX间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1154.58" y="705.77">900</text></g>
    <g href="yl_35kV水鸣站908间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1556.81" y="702.883">908</text></g>
    <g href="yl_35kV水鸣站910间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1705.71" y="700.88">910</text></g>
    <g href="yl_35kV水鸣站911间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="1856.81" y="705.959">911</text></g>
    <g href="yl_35kV水鸣站912间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2001.64" y="702.064">912</text></g>
    <g href="yl_35kV水鸣站922间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2294.05" y="691.44">922</text></g>
    <g href="yl_35kV水鸣站_2主变间隔图.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="21" stroke="rgb(255,255,255)" x="2451.18" y="691.959">902</text></g>
    <g href="yl_bb_博白水鸣站可遥控设备清单.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="35" stroke="rgb(255,255,255)" x="-432.908" y="511.176">操作功能设备清单</text><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="35" stroke="rgb(255,255,255)" x="-432.908" y="473.176">水鸣站具备远方</text></g>
    <g href="yl_AVC_35kV水鸣站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="29" stroke="rgb(255,255,255)" x="-366.355" y="632.264">AVC</text></g>
    <g href="yl_bb_110kV_长岗岭站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="690.282" y="-403.5">T长岗岭</text></g>
    <g href="yl_bb_35kV_绿珠站.fac.svg" style="fill-opacity:0;stroke-opacity:0"><text fill="rgb(255,255,255)" font-family="Sans Serif" font-size="24" stroke="rgb(255,255,255)" x="714.282" y="-371.786">T绿珠</text></g>
  </g>
</svg>