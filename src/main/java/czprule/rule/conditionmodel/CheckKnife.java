package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-16 下午03:23:54   
* 修改人：FU   
* 修改备注：   刀闸操作判断
* @version    
*    
*/
public class CheckKnife implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null){
			return true;
		}
		PowerDevice pd = rbm.getPd();
		if(pd==null){
			return true;
		}
		//识别设备类型判断设备是否是刀闸
		if(!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			return true;
		}
		//目标状态
		String endstate=rbm.getEndState();
		if(endstate.equals("0")){
			//合上
			//刀闸合上操作顺序闭锁
			new CheckKnifeOnSequence().execute(rbm);
			//刀闸与设备间锁规则
			new CheckKnifeWithPowerDevice().execute(rbm);
			//刀闸与开关闭锁规则
			new CheckKnifeOnWithSwitchSequence().execute(rbm);
			//禁止带接地合刀闸规则
			new CheckBanJDKnifeOn().execute(rbm);
			//热倒刀闸合闸原则
			new CheckRDKnifeOn().execute(rbm);
			//冷倒刀闸合闸原则
			new CheckLDKnifeOn().execute(rbm);
			//旁路刀闸操作规则
			new CheckPLKnifeOn().execute(rbm);
			//刀闸拉合大电流空载设备规则
			new CheckDDLKnifeOnOff().execute(rbm);
		}else{
			//拉开
			//刀闸拉开操作顺序闭锁
			new CheckKnifeOffSequence().execute(rbm);
			//刀闸与设备间锁规则
			new CheckKnifeWithPowerDevice().execute(rbm);
			//刀闸与开关闭锁规则
			new CheckKnifeOnWithSwitchSequence().execute(rbm);
			//旁路刀闸操作规则
			new CheckPLKnifeOff().execute(rbm);
			//刀闸拉合大电流空载设备规则
			new CheckDDLKnifeOnOff().execute(rbm);
		}
		return true;
	}

}
