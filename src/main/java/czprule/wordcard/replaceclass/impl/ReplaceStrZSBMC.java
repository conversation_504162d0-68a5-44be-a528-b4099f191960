package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrZSBMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主设备名称".equals(tempStr)){
			List<PowerDevice> list = RuleExeUtil.getDeviceList(stationDev, SystemConstants.PowerTransformer+","+ SystemConstants.InOutLine+"," +SystemConstants.ElecCapacity+"," +SystemConstants.ElecShock+","+SystemConstants.Term, SystemConstants.PowerTransformer, true, true, true);
		    if(list.size() > 0)
		    	replaceStr= CZPService.getService().getDevName(list.get(0));
		}
		return replaceStr;
	}

}
