package com.tellhow.czp.operationcard.dao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.map.ListOrderedMap;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.operationcard.model.BaseCardModel;
import com.tellhow.czp.user.User;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.czp.util.WebServiceUtil;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.model.CardItemModel;

public class TicketDBManager {
	private String username = ""+CBSystemConstants.opcardUser;

	
	
	/**
	 * 描述：保存新建操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String isModel, String cardKind, String buildKind,
			String equipID, String jxpNo) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,AREANO) VALUES('" + zbid + "','"
				+ czrw + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind + ",'"
				+ CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','ORG_1378372745364_7539')";
		state.execute(zbsql);

		/*
		 * List<String> sqls=new ArrayList<String>(); String changzhan="";
		 * String caozuozhiling=""; String cbid=""; String sql=
		 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
		 * ;
		 */
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateUnit + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "')";
			state.execute(mxsql);
			/*
			 * if(!changzhan.equals("")&&!changzhan.equals(operateUnit)){
			 * sql=sql
			 * +"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
			 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
			 * cbid.length()-1); sqls.add(sql); sql=
			 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
			 * ; changzhan=""; caozuozhiling=""; cbid=""; }
			 * changzhan=operateUnit; caozuozhiling+=(operateContent+";");
			 * cbid+=(mxid+";");
			 */
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}

	/**
	 * 描述：保存新建操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param bzsx
	 *            备注事项
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		String devicename = "";
		String tylx= "";
		String zbczdw = ""; 
		
		if(list.size()>0){
			CardItemModel bcm = list.get(0);
			zbczdw = bcm.getShowName();
		}
		
		// 插入操作票主表 
		// 注意：CZLX为事项类型，TYLX为操作类型
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,DEVICENAME,CZLX,TYLX,YDCZSJ) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','" + devicename + "','','" + tylx + "'," + date + ")";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			
			String czdwid = "";
			String czdw = "";
			
//			String sql = "select organid,dwmc  from "+username+"T_NW_ZDDDGXFW where organid in (select (case when ywdw is null then organid else ywdw end) organid  from  opcard.T_NW_ZDDDGXFW where dwmc = '"+operateShow+"')";
			String sql = "SELECT ORGANID,DWMC FROM  "+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGAN a,"+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGANDISPREL b where a.organid=b.slorganid and dwmc = '"+operateShow+"'";
			List<Map<String,Object>> organidlist=DBManager.queryForList(sql);
			czdw = operateShow;
			if(organidlist.size()>0){
				czdwid = StringUtils.ObjToString(organidlist.get(0).get("ORGANID"));
//				czdw = StringUtils.ObjToString(organidlist.get(0).get("DWMC"));
			}
			
			if(cardKind.equals("0")){//综合票
				String mxsql = "INSERT INTO " + username + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,CZDWID,ZLXH) " + "VALUES('" + mxid + "','" + zbid + "','"
						+ operateShow + "','" + operateContent + "','" + operateNum + "','1','" + operateShow + "','" + czdwid  + "','1')";
				state.execute(mxsql);
			}else{//逐项票
				String mxsql = "INSERT INTO " + username + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,CZDWID) " + "VALUES('" + mxid + "','" + zbid + "','"
						+ czdw + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "','" + czdwid  + "')";
				state.execute(mxsql);
			}
			
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}
	
	/**
	 * 描述：保存新建操作票(对应Web标准版)
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param bzsx
	 *            备注事项
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketDBBZB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		String devicename = "";
		String tylx= "";
		String zbczdw = ""; 
		
		if(list.size()>0){
			CardItemModel bcm = list.get(0);
			zbczdw = bcm.getShowName();
		}
		
		// 插入操作票主表 
		// 注意：CZLX为事项类型，TYLX为操作类型
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,DEVICENAME,CZLX,TYLX,AREANO,YDCZSJ) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','" + devicename + "','','" + tylx + "','"+CBSystemConstants.qybm+"'," + date + ")";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			
			String czdwid = "";
			String czdw = "";
			
			String sql = "select organid,dwmc  from "+username+"TH_DC_DCMS_ORGAN where organid in (select (case when ywdw is null then organid else ywdw end) organid  from  "+username+"TH_DC_DCMS_ORGAN where dwmc = '"+operateShow+"')";
			List<Map<String,Object>> organidlist=DBManager.queryForList(sql);
			
			if(organidlist.size()>0){
				czdwid = StringUtils.ObjToString(organidlist.get(0).get("ORGANID"));
				czdw = StringUtils.ObjToString(organidlist.get(0).get("DWMC"));
			}
			
			if(cardKind.equals("0")){//综合票
				String mxsql = "INSERT INTO " + username + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,CZDWID,ZLXH) " + "VALUES('" + mxid + "','" + zbid + "','"
						+ operateShow + "','" + operateContent + "','" + operateNum + "','1','" + operateShow + "','" + czdwid  + "','1')";
				state.execute(mxsql);
			}else{//逐项票
				String mxsql = "INSERT INTO " + username + "T_A_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,CZDWID) " + "VALUES('" + mxid + "','" + zbid + "','"
						+ czdw + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "','" + czdwid  + "')";
				state.execute(mxsql);
			}
			
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}
	/**
	 * 描述：保存新建操作票(广西)
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @return String[] czpzbinfo  操作票主表ID，操作任务，备注事项，是否典型票，操作票类型，开票类型，设备ID，检修票编号，检修票内容，保护类型,检修票URL
	 * @throws SQLException
	 */
	public void gxInsertTicketDB(Connection conn, List<CardItemModel> list,String[]czpzbinfo) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		
//		jxpNo = CBSystemConstants.jxpID;
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,JXPNR,BHLTYPE,JXPURL,STATES,STATUSCHECK) VALUES('" + czpzbinfo[0] + "','"
				+ czpzbinfo[1] + "','" + czpzbinfo[2] + "'," + czpzbinfo[5] + ",'" + CBSystemConstants.getUser().getUserName() + "'," + date + ",'" + czpzbinfo[3] + "'," + czpzbinfo[4]
				+ ",'" + QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode) + "','" + czpzbinfo[6] + "','" + czpzbinfo[7] + "','" + czpzbinfo[8] + "','" +czpzbinfo[9]+ "','" +czpzbinfo[10]+ "',0,'" +czpzbinfo[11]+ "')";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String cardstates = bcm.getTwoDevice()?"1":"0";
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,REMARK,CARDSTATES) " + "VALUES('" + mxid + "','" + czpzbinfo[0] + "','"
					+ operateUnit + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow + "','"+remark+"','"+cardstates+"')";
			state.execute(mxsql);
		}

		state.close();
	}
	
	public String gxInsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String isModel, String cardKind, String buildKind,
			String equipID, String jxpNo, String bhltype) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,BHLTYPE) VALUES('" + zbid + "','"
				+ czrw + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserID() + "'," + date + ",'" + isModel + "'," + cardKind + ",'"
				+ QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode) + "','" + equipID + "','" + jxpNo + "','" + bhltype + "')";
		state.execute(zbsql);

		/*
		 * List<String> sqls=new ArrayList<String>(); String changzhan="";
		 * String caozuozhiling=""; String cbid=""; String sql=
		 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
		 * ;
		 */
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String cardstates = bcm.getTwoDevice()?"1":"0";
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,REMARK,CARDSTATES) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateUnit + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "','"+remark+"','"+cardstates+"')";
			state.execute(mxsql);
			/*
			 * if(!changzhan.equals("")&&!changzhan.equals(operateUnit)){
			 * sql=sql
			 * +"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
			 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
			 * cbid.length()-1); sqls.add(sql); sql=
			 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
			 * ; changzhan=""; caozuozhiling=""; cbid=""; }
			 * changzhan=operateUnit; caozuozhiling+=(operateContent+";");
			 * cbid+=(mxid+";");
			 */
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}

	//深圳
	public String szInsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo,String ylflag) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,YLFLAG) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','" + ylflag + "')";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();;
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,ZLXH) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateShow + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow+ "','" + operateNum + "')";
			state.execute(mxsql);
		}

		state.close();
		return zbid;
	}
	//海南
	public String hananInsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo,String ylflag) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,YLFLAG) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','" + ylflag + "')";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			//解决操作票不一致和czdw为空的bug
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();;
			
			String operateContent = bcm.getCardDesc();
			String mxid = bcm.getUuIds();
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}

			
			String xwzx="";//巡维中心
			String czdw =operateUnit;
			if(czdw.contains("kV")){
				czdw=czdw.substring(czdw.indexOf("kV")+2);
			}
			
			String xwsql ="select organid,dwmc from "+CBSystemConstants.opcardUser+"t_nw_zdddgxfw where organid in  (select decode(fjdw,'',organid,fjdw) from "+CBSystemConstants.opcardUser+"t_nw_zdddgxfw  where dwmc = '"+czdw+"')";
			List listxw = DBManager.query(xwsql);
     		Map temp = new HashMap();
     		if(listxw.size()>0){
     			temp = (Map)listxw.get(0);
     			xwzx = StringUtils.ObjToString(temp.get("dwmc"));
     		}
     		if(xwzx.equals("")){
     			xwzx=operateShow;
     		}
     		
     		//地调czdw,czsn两个字段都是巡维，中调是变电站、巡维
     		if(CZPImpl.getPropertyValue("RolePara").equals("2")){//地调
     			operateShow =xwzx;
     		}
     		
			
			
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,ORDERNUMBER,CZSN,ZLXH) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ xwzx + "','" + operateContent + "'," + operateNum + ",'" + operateItem + "','" + operateShow+ "','" + operateNum + "')";
			
			if(cardKind.equals("1")){
				mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,ORDERNUMBER,CZSN,ZLXH,ISEND) " + "VALUES('" + mxid + "','" + zbid + "','"
						+ xwzx + "','" + operateContent + "'," + operateNum + ",'" + operateItem + "','" + operateShow+ "','" + operateNum + "','0')";
				
			}
			
			state.execute(mxsql);
		}

		state.close();
		return zbid;
	}
	//数字转汉字
	public String toChinese(String string) {
        String[] s1 = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
        String[] s2 = { "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千" };

        String result = "";

        int n = string.length();
        for (int i = 0; i < n; i++) {

            int num = string.charAt(i) - '0';

            if (i != n - 1 && num != 0) {
                result += s1[num] + s2[n - 2 - i];
            } else {
                result += s1[num];
            }
//            System.out.println("  "+result);
        }

//        System.out.println("----------------");
//        System.out.println(result);
        return result;

    }
	public String gxInsertTicketDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String bzsx, String isModel, String cardKind,
			String buildKind, String equipID, String jxpNo,String bhltype) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		
//		jxpNo = CBSystemConstants.jxpID;
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BZSX,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,BHLTYPE) VALUES('" + zbid + "','"
				+ czrw + "','" + bzsx + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserID() + "'," + date + ",'" + isModel + "'," + cardKind
				+ ",'" + QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode) + "','" + equipID + "','" + jxpNo + "','" +bhltype+ "')";
		state.execute(zbsql);
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			// String operateNum = String.valueOf(i+1);
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String cardstates = bcm.getTwoDevice()?"1":"0";
			if (operateItem.equals("")) {
				operateItem = operateNum;
				// operateItem= bcm.getCardNum();
			}
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,REMARK,CARDSTATES) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateUnit + "','" + operateContent + "'," + operateNum + "," + operateItem + ",'" + operateShow + "','"+remark+"','"+cardstates+"')";
			state.execute(mxsql);
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}

	/**
	 * 描述：编辑操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public void updateTicketDB(String zbid, List<CardItemModel> list, String czrw) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "','" + operateNum + "','"
						+ operateItem + "')";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			ShowMessage.view("保存成功！");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			ShowMessage.view("保存失败！");
		}
	}
    
	/**
	 * 描述：编辑典型票广西
	 * 
	 */
	public void updateTicketDXP(String zbid, List<CardItemModel> list, String czrw) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String operateRemark = bcm.getRemark();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,REMARK) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "','" + operateNum + "','"
						+ operateItem + "','" + operateRemark + "')";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			ShowMessage.view("保存成功！");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			ShowMessage.view("保存失败！");
		}
	}
	/**
	 * 描述：编辑典型票广西
	 * 
	 */
	@SuppressWarnings("finally")
	public Boolean updateTicketMLP(String zbid, List<CardItemModel> list, String[] zbnr) {
		boolean issuss = true;
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + zbnr[0] + "',BZSX='"+zbnr[1]+"',JXPNO='"+zbnr[2]+"',JXPNR='"+zbnr[3]+"',JXPURL='"+zbnr[4]+"'  WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String operateRemark = bcm.getRemark();
				String twoDevice = bcm.getTwoDevice()==true?"1":"0";
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,REMARK,CARDSTATES) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "','" + operateNum + "','"
						+ operateItem + "','" + operateRemark + "','"+twoDevice+"')";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			issuss = true;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			issuss = false;
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			
		}finally{
			return issuss;
		}
	}
	
	/**
	 * 描述：编辑操作票，新增备注栏位
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public void updateTicketBZDB(String zbid, List<CardItemModel> list, String czrw, String bz) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "', BZSX = '" + bz + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String showName = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "','" + operateNum + "','"
						+ operateItem + "','" + showName + "')";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			ShowMessage.view("保存成功！");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			ShowMessage.view("保存失败！");
		}
	}

	/*
	 * 编辑票操作 可更新票号
	 */
	public void updateTicketDB(String zbid, List<CardItemModel> list, String czrw, String bh) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "', BH = '" + bh + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "'," + operateNum + ","
						+ operateItem + ")";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			ShowMessage.view("保存成功！");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			ShowMessage.view("保存失败！");
		}
	}

	/* 重写update语句，添加备注事项方法 */
	public void updateTicketDB_bzsx(String zbid, List<CardItemModel> list, String czrw, String bzsx, String bh) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "',BZSX='" + bzsx + "', BH = '" + bh + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM) " + "VALUES('"
						+ java.util.UUID.randomUUID().toString() + "','" + zbid + "','" + operateUnit + "','" + operateContent + "'," + operateNum + ","
						+ operateItem + ")";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
			ShowMessage.view("保存成功！");
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			ShowMessage.view("保存失败！");
		}
	}

	/**
	 * 描述：监控票保存后再次保存编辑操作票
	 * 
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public void updateTicketDBJK(String zbid, List<CardItemModel> list, String czrw) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String sql = "UPDATE " + username + "t_a_CZPZB SET CZRW='" + czrw + "' WHERE ZBID='" + zbid + "'";
			state.execute(sql);
			sql = "DELETE FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "'";
			state.execute(sql);
			for (int i = 0; i < list.size(); i++) {
				CardItemModel bcm = list.get(i);
				String operateNum = bcm.getCardNum();
				String operateItem = bcm.getCardItem();
				String operateUnit = bcm.getStationName();
				String operateContent = bcm.getCardDesc();
				String uuid = bcm.getUuIds();
				String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM) " + "VALUES('" + uuid + "','" + zbid + "','"
						+ operateUnit + "','" + operateContent + "'," + operateNum + "," + operateItem + ")";
				state.execute(mxsql);
			}
			conn.commit();
			state.close();
			conn.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}
	}

	/**
	 * 描述：将操作票保存为典型票
	 * 
	 * @param zbid
	 *            主表ID
	 */
	public boolean InsertDXTicketDB(String zbid) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		String kind = CBSystemConstants.apptype;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String dxpzbid = java.util.UUID.randomUUID().toString();// 主表ID
			String sql = "";
			if (kind.equals("0")) {
				sql = "INSERT INTO "
						+ username
						+ "t_a_CZPZB(ZBID,CARDKIND,CZRW,BZSX,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID) "// 增加了BZSX
						+ " SELECT '" + dxpzbid + "',CARDKIND,CZRW" + ",BZSX,'" + CBSystemConstants.getUser().getUserID() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			} else {
				sql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID,CARDKIND) " + " SELECT '" + dxpzbid + "',CZRW,'"
						+ CBSystemConstants.getUser().getUserID() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID ,'3' FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			}
			state.execute(sql);
			sql = "SELECT '' ID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,remark FROM " + username + "t_a_CZPMX WHERE F_ZBID='" + zbid + "'";
			List czpList = DBManager.query(sql);
			Map temp = new HashMap();
			for (int i = 0; i < czpList.size(); i++) {
				temp = (Map) czpList.get(i);
//				String zbid2 = java.util.UUID.randomUUID().toString();// 主表ID
				String[] tempStr = new String[] { java.util.UUID.randomUUID().toString(), dxpzbid, StringUtils.ObjToString(temp.get("CZDW")),
						StringUtils.ObjToString(temp.get("CZNR")), StringUtils.ObjToString(temp.get("CARDORDER")),
						StringUtils.ObjToString(temp.get("CARDITEM")), StringUtils.ObjToString(temp.get("CZSN")),StringUtils.ObjToString(temp.get("REMARK")) };
				sql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN,REMARK) values('" + tempStr[0] + "'," + "'" + tempStr[1]
						+ "'," + "'" + tempStr[2] + "'," + "'" + tempStr[3] + "'," + "'" + tempStr[4] + "'," + "'" + tempStr[5] + "'," + "'" + tempStr[6]
						+ "','"+tempStr[7]+"')";
				state.execute(sql);
				conn.commit();
			}
			state.close();
			conn.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			return false;
		}
		return true;
	}

	/**
	 * 描述：查询操作票主表
	 * 
	 * @param beginTime
	 *            查询起始时间
	 * @param endTime
	 *            查询结束时间
	 * @param conditionTask
	 *            操作任务
	 * @param npr
	 *            拟票人
	 * @param devType
	 *            设备类型
	 * @return [主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态、操作票类型]
	 */
	public List<String[]> queryTicketZB(String beginTime, String endTime, String conditionTask, String npr, String devType, String role) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		if (!beginTime.equals("")) {
			queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " and t.czrw like '%" + conditionTask + "%' ";
		}
		if (!"".equals(npr)) {
			queryStr = queryStr + " and t.npr='" + npr + "'";
		}
		if (!"".equals(devType)) {
			queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
		}
		
//		User user = CBSystemConstants.getUser();

//		String uisql = "";
//		if (user.getUnitCode().equalsIgnoreCase("system")) {
//			uisql = "u )";
//		} else {
//			uisql = "u where u.unicode ='" + user.getUnitCode() + "')";
//		}
//		String rosql = "";
//		if (role.equals("")) {
//			rosql = "r";
//		} else {
//			rosql = " r where r.rolecode = " + role;
//		}

		String sql = "SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','智能票','1','点图票','2','手工票') AS BUILDKIND ,"
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,"
				+ " (SELECT T2.Unitcode FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO T2  WHERE T2.USERID = T.NPR) AS UNICODE,"
				+ "TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
				+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG,t.bzsx " + "FROM " + username
				
				+ OPEService.getService().TicketDBManager1() 
				+ queryStr + "and opcode='"+CBSystemConstants.opCode+"' AND T.ISMODEL='0' AND STATES = 1 ORDER BY T.NPSJ DESC";
		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < czpList.size(); i++) {
			temp = (Map) czpList.get(i);
			String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG")),
					StringUtils.ObjToString(temp.get("bzsx")) };

			results.add(tempStr);
		}
		return results;
	}

	/**
	 * 描述：查询操作票主表
	 * 
	 * @param beginTime
	 *            查询起始时间
	 * @param endTime
	 *            查询结束时间
	 * @param conditionTask
	 *            操作任务
	 * @param npr
	 *            拟票人
	 * @param devType
	 *            设备类型
	 * @param cz_kind
	 *            操作票类型，--宗令，逐项，新投票
	 * @return [主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态、设备类型，操作票类型]
	 */
	public List<String[]> queryTicketZB(String beginTime, String endTime, String conditionTask, String npr, String devType, String role, String cz_kind) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		if (!beginTime.equals("")) {
			queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " and t.czrw like '%" + conditionTask + "%' ";
		}
		if (!"".equals(npr)) {
			queryStr = queryStr + " and t.npr='" + npr + "'";
		}
		if (!"".equals(cz_kind)) {
			queryStr = queryStr + " and t.cardkind='" + cz_kind + "'";
		}
		if (!"".equals(devType)) {
			queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
		}

		User user = CBSystemConstants.getUser();

		String uisql = "";
		if (user.getUnitCode().equalsIgnoreCase("system")) {
			uisql = "u )";
		} else {
			uisql = "u where u.unicode ='" + user.getUnitCode() + "')";
		}
		String rosql = "";
		if (role.equals("")) {
			rosql = "r";
		} else {
			rosql = " r where r.rolecode = " + role;
		}

		String sql = "Select * from(Select * from (SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','智能票','1','点图票','2','手工票') AS BUILDKIND ,"
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,"
				+ " (SELECT T2.Unitcode FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO T2  WHERE T2.USERID = T.NPR) AS UNICODE,"
				+ "(SELECT T3.rolecode FROM "+CBSystemConstants.opcardUser+"t_a_opcodeinfo T3 WHERE T3.opcode = T.opcode) AS rolecode,"
				+ "TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
				+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG,t.bzsx " + "FROM " + username
				// +
				// "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  "
				// edit 2014.6.24
				+ OPEService.getService().TicketDBManager1() + queryStr + "AND T.ISMODEL='0' and T.CARDKIND!='3' ORDER BY T.NPSJ DESC)" + uisql + rosql;
		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < czpList.size(); i++) {
			temp = (Map) czpList.get(i);
			String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG")),
					StringUtils.ObjToString(temp.get("bzsx")) };

			results.add(tempStr);
		}
		return results;
	}
	
	// 查询操作票主表
	public List<String[]> queryTicketZBData(String beginTime, String endTime, String conditionTask, String npr, String devType,String czdw) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		String queryCzdw = "";
		if (!beginTime.equals("")) {
			queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " and t.czrw like '%" + conditionTask.replace(" ", "%").replace("　", "%") + "%' ";
		}
		
		if (!"".equals(czdw)) {
			queryCzdw = queryCzdw + "  and r.czdw like '%" + czdw.replace(" ", "%").replace("　", "%") + "%' ";
		}
		
		if (!"".equals(npr)) {
			queryStr = queryStr + " and t.npr='" + npr + "'";
		}
		if (!"".equals(devType)) {
			queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
		}
		User user = CBSystemConstants.getUser();

		String uisql = "";
		if (user.getUnitCode().equalsIgnoreCase("system")) {
			uisql = "u )";
		} else {
			uisql = "u where u.unicode ='" +CBSystemConstants.unitCode + "')";
		}
		String usersql = "select rolecode from "+CBSystemConstants.opcardUser+"t_a_userrole t where t.userid='" + user.getUserID() + "'";
		List urlist = DBManager.query(usersql);
		String rosql = "";
		Map map = new HashMap();
		if (urlist.size() > 0) {
			for (int i = 0; i < urlist.size(); i++) {
				map = (Map) urlist.get(i);
				if (i == 0) {
					rosql += " r  where r.rolecode = " + map.get("rolecode");
				} else {
					rosql += " or r.rolecode = " + map.get("rolecode");
				}
			}
		}
		String cardKind = "";
		// 用户类型为监控，查询为监控票
		if (CBSystemConstants.roleCode.equals("2")) {
			cardKind = " ";
		} else {// 调度票查询，非监控票
			cardKind = " and T.CARDKIND!='3' ";
		}

		String sql = "Select * from(Select * from (SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','智能票','1','点图票','2','手工票') AS BUILDKIND ,"
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,"
				+ " (SELECT T2.Unitcode FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO T2  WHERE T2.USERID = T.NPR) AS UNICODE,"
				+ "(SELECT T3.rolecode FROM "+CBSystemConstants.opcardUser+"t_a_opcodeinfo T3 WHERE T3.opcode = T.opcode) AS rolecode,"
				+ "TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
				+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG ,DECODE(CARDKIND,'0',(SELECT  czdw  FROM "+CBSystemConstants.opcardUser+"t_a_CZPMX   WHERE f_zbid=T.zbid AND ROWNUM =1),'') AS CZDW  " + "FROM " + username
				// +
				// "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  "
				// edit 2014.6.24
				+ OPEService.getService().TicketDBManager1() + queryStr + "AND T.ISMODEL='0' " + cardKind + "ORDER BY T.NPSJ DESC)" + uisql + rosql+ queryCzdw;
		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < czpList.size(); i++) {
			temp = (Map) czpList.get(i);
			String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG")), StringUtils.ObjToString(temp.get("CZDW")) };

			results.add(tempStr);
		}
		return results;
	}
	
	

	// 查询操作票主表
	public List<String[]> queryTicketZB(String beginTime, String endTime, String conditionTask, String npr, String devType) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		if (!beginTime.equals("")) {
			queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " and t.czrw like '%" + conditionTask.replace(" ", "%").replace("　", "%") + "%' ";
		}
		if (!"".equals(npr)) {
			queryStr = queryStr + " and t.npr='" + npr + "'";
		}
		if (!"".equals(devType)) {
			queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
		}
		User user = CBSystemConstants.getUser();

		String uisql = "";
		if (user.getUnitCode().equalsIgnoreCase("system")) {
			uisql = "u )";
		} else {
			//uisql = "u where u.unicode ='" +CBSystemConstants.unitCode + "')";
		}
		String usersql = "select rolecode from "+CBSystemConstants.opcardUser+"t_a_userrole t where t.userid='" + user.getUserID() + "'";
		List urlist = DBManager.query(usersql);
		String rosql = "";
		Map map = new HashMap();
		if (urlist.size() > 0) {
			for (int i = 0; i < urlist.size(); i++) {
				map = (Map) urlist.get(i);
				if (i == 0) {
					rosql += " r  where r.rolecode = " + map.get("rolecode");
				} else {
					rosql += " or r.rolecode = " + map.get("rolecode");
				}
			}
		}
		String cardKind = "";
		// 用户类型为监控，查询为监控票
		if (CBSystemConstants.roleCode.equals("2")) {
			cardKind = " ";
		} else {// 调度票查询，非监控票
			cardKind = " and T.CARDKIND!='3' ";
		}

		String sql = "Select * from(Select * from (SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','智能票','1','手工票','2','点图票') AS BUILDKIND ,"
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,"
				+ " (SELECT T2.Unitcode FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO T2  WHERE T2.USERNAME = T.NPR) AS UNICODE,"
				+ "(SELECT T3.rolecode FROM "+CBSystemConstants.opcardUser+"t_a_opcodeinfo T3 WHERE T3.opcode = T.opcode) AS rolecode,"
				+ "TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERNAME=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
				+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG " + "FROM " + username
				// +
				// "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  "
				// edit 2014.6.24
				+ OPEService.getService().TicketDBManager1() + queryStr + "AND T.ISMODEL='0' " + cardKind + "ORDER BY T.NPSJ DESC))" + uisql + rosql;
		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < czpList.size(); i++) {
			temp = (Map) czpList.get(i);
			String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG"))};

			results.add(tempStr);
		}
		return results;
	}

	public List<String[]> queryTicketZBJK(String beginTime, String endTime, String conditionTask, String npr, String devType) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		if (!beginTime.equals("")) {
			queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
		} else {
			queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " and t.czrw like '%" + conditionTask.replace(" ", "%").replace("　", "%") + "%' ";
		}
		if (!"".equals(npr)) {
			queryStr = queryStr + " and t.npr='" + npr + "'";
		}
		if (!"".equals(devType)) {
			queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
		}

		String sql = "SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','智能票','1','点图票','2','手工票','3','调度接转令') AS BUILDKIND ,"
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
				+ "(SELECT T2.USERNAME FROM "
				+ username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
				+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG " + "FROM " + username
				// +
				// "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  "
				// edit 2014.6.24
				+ OPEService.getService().TicketDBManager2() + queryStr + "AND T.ISMODEL='0' and t.opcode='"+CBSystemConstants.opCode+"' and T.CARDKIND='3' ORDER BY T.NPSJ DESC";

		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < czpList.size(); i++) {
			temp = (Map) czpList.get(i);
			String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG")) };

			results.add(tempStr);
		}
		return results;
	}
	
	// 查询操作票主表
			public List<String[]> queryTicketZBByOpcode(String beginTime, String endTime, String conditionTask, String npr, String devType) {

				List<String[]> results = new ArrayList<String[]>();
				String queryStr = "";
				if (!beginTime.equals("")) {
					queryStr = " npsj between to_date('" + beginTime + " 00:00:00','yyyy-MM-dd hh24:mi:ss') ";
				} else {
					queryStr = " npsj between to_date('1900-1-1','yyyy-MM-dd') ";
				}
				if (!endTime.equals("")) {
					queryStr = queryStr + " and to_date('" + endTime + " 23:59:59','yyyy-MM-dd hh24:mi:ss') ";
				} else {
					queryStr = queryStr + " and to_date('2200-1-1','yyyy-MM-dd') ";
				}
				if (!"".equals(conditionTask)) {
					queryStr = queryStr + " and t.czrw like '%" + conditionTask.replace(" ", "%").replace("　", "%") + "%' ";
				}
				if (!"".equals(npr)) {
					queryStr = queryStr + " and t.npr='" + npr + "'";
				}
				if (!"".equals(devType)) {
					queryStr = queryStr + " and b.equiptype_flag='" + devType + "'";
				}
				User user = CBSystemConstants.getUser();

				String uisql = "";
				if (user.getUnitCode().equalsIgnoreCase("system")) {
					uisql = "u )";
				} else {
					uisql = "u where u.unicode ='" + user.getUnitCode() + "')";
				}
				String usersql = "select rolecode from "+CBSystemConstants.opcardUser+"t_a_userrole t where t.userid='" + user.getUserID() + "'";
				List urlist = DBManager.query(usersql);
				String rosql = "";
				Map map = new HashMap();
				if (urlist.size() > 0) {
					for (int i = 0; i < urlist.size(); i++) {
						map = (Map) urlist.get(i);
						if (i == 0) {
							rosql += " r  where r.rolecode = " + map.get("rolecode");
						} else {
							rosql += " or r.rolecode = " + map.get("rolecode");
						}
					}
				}
				String cardKind = "";
				// 用户类型为监控，查询为监控票
				if (CBSystemConstants.roleCode.equals("2")) {
					cardKind = " ";
				} else {// 调度票查询，非监控票
					cardKind = " and T.CARDKIND!='3' ";
				}

				String sql = "SELECT T.ZBID,T.BH,T.CZRW,DECODE(T.STATES,'0','  未归档','1','  已归档') AS STATES,DECODE(T.BUILDKIND,'0','手工拟票','图形成票') AS BUILDKIND ,"
						+ "T.NPR,"
						+ " (SELECT T2.Unitcode FROM "+CBSystemConstants.opcardUser+"t_a_POWERUSERINFO T2  WHERE T2.USERID = T.NPR) AS UNICODE,"
						+ "TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, "
						+ "(SELECT T2.USERNAME FROM "
						+ username
						+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DECODE(T.DMISFLAG,'0','未导入','1','已导入') AS DMISFLAG, "
						+ "DECODE(T.YLFLAG,'0','  未预令','1','  已预令','2','  已签收') AS YLFLAG " + "FROM " + username
						// +
						// "t_a_CZPZB T left join "+CBSystemConstants.opcardUser+"t_e_equipinfo a on t.equipid=a.equip_id left join "+CBSystemConstants.opcardUser+"t_e_equiptype b on a.equiptype_id=b.equiptype_id WHERE  "
						// edit 2014.6.24
						+ OPEService.getService().TicketDBManager1() + queryStr + "and t.opcode='"+CBSystemConstants.opCode+"' AND T.ISMODEL='0' " + cardKind + "ORDER BY T.NPSJ DESC";
				List czpList = DBManager.query(sql);
				Map temp = new HashMap();
				for (int i = 0; i < czpList.size(); i++) {
					temp = (Map) czpList.get(i);
					String[] tempStr = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
							StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
							StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
							StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")), StringUtils.ObjToString(temp.get("YLFLAG")) };

					results.add(tempStr);
				}
				return results;
			}


	/**
	 * 描述：查询操作票主表
	 * 
	 * @param zbid
	 *            主表ID
	 * @return [主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态、操作票类型、导入OMS标志]
	 */
	public String[] queryTicketZB(String zbid) {
		String[] results = null;
		String sql = "SELECT T.ZBID,T.BH,T.CZRW,T.STATES,DECODE(T.BUILDKIND,'0','  智能票','1','  手工票') AS BUILDKIND ," + "(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, " + "(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DMISFLAG,o.rolecode as DISPATCHTYPE,BZSX " + "FROM " + username
				+ "t_a_CZPZB T,"+CBSystemConstants.opcardUser+"t_a_opcodeinfo o WHERE  T.ZBID='" + zbid + "'  and o.opcode=t.opcode ";

		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		if (czpList.size() == 1) {
			temp = (Map) czpList.get(0);
			results = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")),
					StringUtils.ObjToString(temp.get("DISPATCHTYPE")), StringUtils.ObjToString(temp.get("BZSX")) };
		}
		return results;
	}
	
	/**
	 * 描述：查询操作票主表
	 * 
	 * @param zbid
	 *            主表ID
	 * @return [主表ID、序号、操作任务、拟票人、拟票时间、审核人、开票类型、状态、操作票类型、导入OMS标志]
	 */
	public String[] queryTicketZB_XGSS(String zbid) {
		String[] results = null;
		String sql = "SELECT T.ZBID,T.BH,T.CZRW,T.STATES,DECODE(T.BUILDKIND,'0','  智能票','1','  手工票') AS BUILDKIND ," + "(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ, " + "(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.SHR) SHR,CARDKIND,DMISFLAG,o.rolecode as DISPATCHTYPE,BZSX,XGSS " + "FROM " + username
				+ "t_a_CZPZB T,"+CBSystemConstants.opcardUser+"t_a_opcodeinfo o WHERE  T.ZBID='" + zbid + "'  and o.opcode=t.opcode ";

		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		if (czpList.size() == 1) {
			temp = (Map) czpList.get(0);
			results = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("shr")), StringUtils.ObjToString(temp.get("BUILDKIND")), StringUtils.ObjToString(temp.get("states")),
					StringUtils.ObjToString(temp.get("cardkind")), StringUtils.ObjToString(temp.get("DMISFLAG")),
					StringUtils.ObjToString(temp.get("DISPATCHTYPE")), StringUtils.ObjToString(temp.get("BZSX")),StringUtils.ObjToString(temp.get("XGSS")) };
		}
		return results;
	}
	/**
	 * 描述：查询操作票主表
	 * 
	 * @param zbid
	 *            主表ID
	 * @return [主表ID、操作编号、操作任务、拟票人、拟票时间、备注事项、关联检修票编号,操作票类型]
	 */
	public String[] queryTicketZB_GX(String zbid) {
		String[] results = null;
		String sql = "SELECT T.ZBID,T.BH,T.CZRW ," + "(SELECT T2.USERNAME FROM " + username
				+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD HH24:MI:SS') NPSJ ,BZSX,JXPNO,JXPNR,cardkind " + "FROM " + username
				+ "t_a_CZPZB T WHERE  T.ZBID='" + zbid + "'";

		List czpList = DBManager.query(sql);
		Map temp = new HashMap();
		if (czpList.size() == 1) {
			temp = (Map) czpList.get(0);
			results = new String[] { StringUtils.ObjToString(temp.get("zbid")), StringUtils.ObjToString(temp.get("BH")),
					StringUtils.ObjToString(temp.get("czrw")), StringUtils.ObjToString(temp.get("npr")), StringUtils.ObjToString(temp.get("npsj")),
					StringUtils.ObjToString(temp.get("BZSX")),StringUtils.ObjToString(temp.get("JXPNO")),StringUtils.ObjToString(temp.get("cardkind")),StringUtils.ObjToString(temp.get("JXPNR"))};
		}
		return results;
	}
	/**
	 * 描述：查询操作票明细表
	 * 
	 * @param zbid
	 *            主表ID
	 * @return
	 */
	public List<BaseCardModel> queryTicketMX(String zbid) {
		List<BaseCardModel> baseCardS = new ArrayList<BaseCardModel>();
		String sql = "SELECT T.MXID,T.CZSN,T.CZDW,T.CZNR,TO_CHAR(T.XLSJ,'MM-DD HH24:MI') XLSJ,TO_CHAR(T.HLSJ,'MM-DD HH24:MI') HLSJ,"
				+ "(SELECT T2.USERNAME FROM " + username + "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.XLR) XLR,T.SLR,T.HLR,"
				+ " DECODE(T.CARDSTATES,'-1','未下令','0','已下令','1','已受令','2','已回令') CARDSTATES,CARDITEM,CARDORDER FROM " + username + "t_a_CZPMX T"
				+ " WHERE T.F_ZBID='" + zbid + "' ORDER BY T.CARDORDER";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {

			temp = (Map) results.get(i);
			BaseCardModel bcm = new BaseCardModel();
			bcm.setMxid(StringUtils.ObjToString(temp.get("MXID")));
			bcm.setCardDesc(StringUtils.ObjToString(temp.get("CZNR")));
			bcm.setStationName(StringUtils.ObjToString(temp.get("CZDW")));
			bcm.setCzsn(StringUtils.ObjToString(temp.get("CZSN")));
			bcm.setXlr(StringUtils.ObjToString(temp.get("XLR")));
			bcm.setXlsj(StringUtils.ObjToString(temp.get("XLSJ")));
			bcm.setSlr(StringUtils.ObjToString(temp.get("SLR")));
			bcm.setHlr(StringUtils.ObjToString(temp.get("HLR")));
			bcm.setHlsj(StringUtils.ObjToString(temp.get("HLSJ")));
			bcm.setStatus(StringUtils.ObjToString(temp.get("CARDSTATES")));
			bcm.setCardSub(StringUtils.ObjToString(temp.get("CARDITEM")));
			bcm.setCardOrder(StringUtils.ObjToString(temp.get("CARDORDER")));
			baseCardS.add(bcm);
		}
		return baseCardS;
	}
	/**
	 * 描述：查询操作票明细表(广西)
	 * 
	 * @param zbid
	 *            主表ID
	 * @return
	 */
	public List<BaseCardModel> gxqueryTicketMX(String zbid) {
		List<BaseCardModel> baseCardS = new ArrayList<BaseCardModel>();
		String sql = "SELECT T.MXID,T.CZSN,T.CZDW,T.CZNR,T.REMARK,TO_CHAR(T.XLSJ,'MM-DD HH24:MI') XLSJ,TO_CHAR(T.HLSJ,'MM-DD HH24:MI') HLSJ,"
				+ "(SELECT T2.USERNAME FROM " + username + "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.XLR) XLR,T.SLR,T.HLR,"
				+ " DECODE(T.CARDSTATES,'-1','未下令','0','已下令','1','已受令','2','已回令') CARDSTATES,CARDITEM,CARDORDER FROM " + username + "t_a_CZPMX T"
				+ " WHERE T.F_ZBID='" + zbid + "' ORDER BY to_number(T.CARDORDER)";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {

			temp = (Map) results.get(i);
			BaseCardModel bcm = new BaseCardModel();
			bcm.setMxid(StringUtils.ObjToString(temp.get("MXID")));
			bcm.setCardDesc(StringUtils.ObjToString(temp.get("CZNR")));
			bcm.setStationName(StringUtils.ObjToString(temp.get("CZDW")));
			bcm.setCzsn(StringUtils.ObjToString(temp.get("CZSN")));
			bcm.setXlr(StringUtils.ObjToString(temp.get("XLR")));
			bcm.setXlsj(StringUtils.ObjToString(temp.get("XLSJ")));
			bcm.setSlr(StringUtils.ObjToString(temp.get("SLR")));
			bcm.setHlr(StringUtils.ObjToString(temp.get("HLR")));
			bcm.setHlsj(StringUtils.ObjToString(temp.get("HLSJ")));
			bcm.setStatus(StringUtils.ObjToString(temp.get("CARDSTATES")));
			bcm.setCardSub(StringUtils.ObjToString(temp.get("CARDITEM")));
			bcm.setCardOrder(StringUtils.ObjToString(temp.get("CARDORDER")));
			bcm.setRemark(StringUtils.ObjToString(temp.get("REMARK")));
			baseCardS.add(bcm);
		}
		return baseCardS;
	}

	/**
	 * 描述：查询典型操作票主表
	 * 
	 * @param beginTime
	 *            查询起始时间
	 * @param endTime
	 *            查询结束时间
	 * @param conditionTask
	 *            操作任务
	 * @param kind
	 *            类型
	 * @param npr
	 *            拟票人
	 * @param devType
	 *            设备类型
	 * @return [主表ID、操作任务、拟票人、拟票时间]
	 * @throws SQLException
	 */
	public List<String[]> queryDXTicketZB(String beginTime, String endTime, String conditionTask, String kind) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		if (!beginTime.equals("")) {
			queryStr = " NPSJ BETWEEN TO_DATE('" + beginTime + " 00:00:00','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = " NPSJ BETWEEN TO_DATE('1900-1-1','YYYY-MM-DD') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " AND TO_DATE('" + endTime + " 23:59:59','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = queryStr + " AND TO_DATE('2200-1-1','YYYY-MM-DD') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
		}
		String sql;
		if (CBSystemConstants.isOffline) {
			conn = DBManager.getLOCConnection();
			username = "";
			if (!beginTime.equals("")) {
				queryStr = " NPSJ BETWEEN TIME('" + beginTime + " 00:00:00') ";
			} else {
				queryStr = " NPSJ BETWEEN TIME('1900-1-1') ";
			}
			if (!endTime.equals("")) {
				queryStr = queryStr + " AND TIME('" + endTime + " 23:59:59') ";
			} else {
				queryStr = queryStr + " AND TIME('2200-1-1') ";
			}
			if (!"".equals(conditionTask)) {
				queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
			}
			sql = "SELECT T.ZBID,T.CZRW,NPR,T.NPSJ FROM " + username + "t_a_CZPZB T WHERE " + queryStr + "AND T.ISMODEL='1' ORDER BY T.NPSJ DESC";
		} else {
			conn = DBManager.getConnection();
			sql = "SELECT T.ZBID,T.CZRW,(SELECT T2.USERNAME FROM " + username
					+ "t_a_POWERUSERINFO T2 WHERE T2.USERID=T.NPR) NPR,TO_CHAR(T.NPSJ,'YYYY-MM-DD hh24:mi:ss') NPSJ " + "FROM " + username
					+ "t_a_CZPZB T WHERE " + queryStr + "AND T.ISMODEL='1' AND T.OPCODE='" + kind + "' ORDER BY T.NPSJ DESC";
		}
		try {
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sql);
			while (rs.next()) {
				String[] tempStr = new String[] { rs.getString("zbid"), rs.getString("czrw"), rs.getString("npr"), rs.getString("npsj") };
				results.add(tempStr);
			}
			conn.close();
			stmt.close();
			rs.close();
		} catch (SQLException e) {
			e.printStackTrace();
			try {
				conn.close();
				stmt.close();
				rs.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}

		return results;
	}

	// 典型票查询重载
	public List<String[]> queryDXTicketZB(String beginTime, String endTime, String conditionTask, String kind, String role) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		if (!beginTime.equals("")) {
			queryStr = " NPSJ BETWEEN TO_DATE('" + beginTime + " 00:00:00','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = " NPSJ BETWEEN TO_DATE('1900-1-1','YYYY-MM-DD') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " AND TO_DATE('" + endTime + " 23:59:59','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = queryStr + " AND TO_DATE('2200-1-1','YYYY-MM-DD') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
		}
		String sql;
		if (CBSystemConstants.isOffline) {
			conn = DBManager.getLOCConnection();
			username = "";
			if (!beginTime.equals("")) {
				queryStr = " NPSJ BETWEEN TIME('" + beginTime + " 00:00:00') ";
			} else {
				queryStr = " NPSJ BETWEEN TIME('1900-1-1') ";
			}
			if (!endTime.equals("")) {
				queryStr = queryStr + " AND TIME('" + endTime + " 23:59:59') ";
			} else {
				queryStr = queryStr + " AND TIME('2200-1-1') ";
			}
			if (!"".equals(conditionTask)) {
				queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
			}
			sql = "SELECT T.ZBID,T.CZRW,NPR,T.NPSJ FROM " + username + "t_a_CZPZB T WHERE " + queryStr + "AND T.ISMODEL='1' ORDER BY T.NPSJ DESC";
		} else {
			conn = DBManager.getConnection();
			// 典型票分为 本地和省调典型票
			sql = "SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL " + " FROM " + username + "t_a_CZPZB T," + username
					+ "t_a_opcodeinfo t1 WHERE " + queryStr + " AND (T.ISMODEL = '1' or T.ISMODEL = '2') AND T.CARDKIND = '1'  and t.opcode=t1.opcode "
					+ " and t1.areano='" + CBSystemConstants.getUser().getUnitCode() + "' and t1.rolecode='" + role + "'";
		}
		try {
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sql);
			while (rs.next()) {
				String[] tempStr = new String[] { rs.getString("zbid"), rs.getString("czrw"), "", rs.getString("npsj"), rs.getString("bzsx"),
						rs.getString("ISMODEL") };
				results.add(tempStr);
			}
			conn.close();
			stmt.close();
			rs.close();
		} catch (SQLException e) {
			e.printStackTrace();
			try {
				conn.close();
				stmt.close();
				rs.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}

		return results;
	}

	// 广西典型票主表查询
	public List<String[]> queryGXDXTicketZB(String beginTime, String endTime, String conditionTask, String kind, String role) {
		
		List<String[]> results = new ArrayList<String[]>();
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
//		if (!beginTime.equals("")) {
//			queryStr = " NPSJ BETWEEN TO_DATE('" + beginTime + " 00:00:00','YYYY-MM-DD HH24:MI:SS') ";
//		} else {
//			queryStr = " NPSJ BETWEEN TO_DATE('1900-1-1','YYYY-MM-DD') ";
//		}
//		if (!endTime.equals("")) {
//			queryStr = queryStr + " AND TO_DATE('" + endTime + " 23:59:59','YYYY-MM-DD HH24:MI:SS') ";
//		} else {
//			queryStr = queryStr + " AND TO_DATE('2200-1-1','YYYY-MM-DD') ";
//		}
//		if (!"".equals(conditionTask)) {
//			queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
//		}
		String sql;
		conn = DBManager.getConnection();
		// 典型票分为 本地和省调典型票
		sql = "SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL " + " FROM " + username + "t_a_CZPZB T WHERE  (T.ISMODEL = '1' or T.ISMODEL = '2')";
        try{
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sql);
			while (rs.next()) {
				String[] tempStr = new String[] { rs.getString("zbid"), rs.getString("czrw"), "", rs.getString("npsj"), rs.getString("bzsx"),
						rs.getString("ISMODEL") };
				results.add(tempStr);
		}
		conn.close();
		stmt.close();
		rs.close();
		} catch (SQLException e) {
			e.printStackTrace();
			try {
				conn.close();
				stmt.close();
				rs.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}

		return results;
		}

	
	/**
	 * tanfei
	 * 
	 * @since 2014-12-23 16:50 典型票 省调规范 查询重载 ismodel 值为"2"
	 * @param beginTime
	 * @param endTime
	 * @param conditionTask
	 * @param kind
	 * @param role
	 * @return
	 */
	public List<String[]> queryDXGFTicketZB(String beginTime, String endTime, String conditionTask, String kind, String role) {

		List<String[]> results = new ArrayList<String[]>();
		String queryStr = "";
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		if (!beginTime.equals("")) {
			queryStr = " NPSJ BETWEEN TO_DATE('" + beginTime + " 00:00:00','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = " NPSJ BETWEEN TO_DATE('1900-1-1','YYYY-MM-DD') ";
		}
		if (!endTime.equals("")) {
			queryStr = queryStr + " AND TO_DATE('" + endTime + " 23:59:59','YYYY-MM-DD HH24:MI:SS') ";
		} else {
			queryStr = queryStr + " AND TO_DATE('2200-1-1','YYYY-MM-DD') ";
		}
		if (!"".equals(conditionTask)) {
			queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
		}
		String sql;
		if (CBSystemConstants.isOffline) {
			conn = DBManager.getLOCConnection();
			username = "";
			if (!beginTime.equals("")) {
				queryStr = " NPSJ BETWEEN TIME('" + beginTime + " 00:00:00') ";
			} else {
				queryStr = " NPSJ BETWEEN TIME('1900-1-1') ";
			}
			if (!endTime.equals("")) {
				queryStr = queryStr + " AND TIME('" + endTime + " 23:59:59') ";
			} else {
				queryStr = queryStr + " AND TIME('2200-1-1') ";
			}
			if (!"".equals(conditionTask)) {
				queryStr = queryStr + " AND T.CZRW LIKE '%" + conditionTask + "%' ";
			}
			sql = "SELECT T.ZBID,T.CZRW,NPR,T.NPSJ FROM " + username + "t_a_CZPZB T WHERE " + queryStr + "AND T.ISMODEL='1' ORDER BY T.NPSJ DESC";
		} else {
			conn = DBManager.getConnection();
			if (CBSystemConstants.getUser().getOrganGrade().equals("3")) {
				sql = "(SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL,t.cardkind " + " FROM " + username + "t_a_CZPZB T," + username
						+ "t_a_opcodeinfo t1 WHERE " + queryStr + " AND T.ISMODEL = '1' AND T.CARDKIND != '3'  and t.opcode=t1.opcode " + " and t1.areano='"
						+ CBSystemConstants.getUser().getUnitCode() + "' and t1.rolecode='" + role + "'" + "union "
						+ "SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL,t.cardkind  " + " FROM " + username + "t_a_CZPZB T,"
						+ username + "t_a_opcodeinfo t1 WHERE " + queryStr + " AND T.ISMODEL = '2' AND T.CARDKIND != '3'  and t.opcode=t1.opcode "
						+ " and t1.rolecode='" + role + "' and to_char(t.NPSJ,'YYYYMMDD') = '20141217'" + "union "
						+ "SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL,t.cardkind  " + " FROM " + username + "t_a_CZPZB T,"
						+ username + "t_a_opcodeinfo t1 WHERE " + queryStr + " AND T.ISMODEL = '2' AND T.CARDKIND != '3'  and t.opcode=t1.opcode "
						+ " and t1.rolecode!='" + role + "' and to_char(t.NPSJ,'YYYYMMDD') = '20141223')" + " order by NPSJ desc ";

			} else {
				sql = "(SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL,t.cardkind  " + " FROM " + username + "t_a_CZPZB T,"
						+ username + "t_a_opcodeinfo t1 WHERE " + queryStr + " AND T.ISMODEL = '1' AND T.CARDKIND != '3'  and t.opcode=t1.opcode "
						+ " and t1.areano='" + CBSystemConstants.getUser().getUnitCode() + "' and t1.rolecode='" + role + "'" + "union "
						+ "SELECT T.ZBID,T.CZRW,TO_CHAR(T.NPSJ, 'YYYY-MM-DD') NPSJ,T.BZSX,t.ISMODEL,t.cardkind  " + " FROM " + username + "t_a_CZPZB T,"
						+ username + "t_a_opcodeinfo t1 WHERE " + queryStr + " AND T.ISMODEL = '2' AND T.CARDKIND != '3'  and t.opcode=t1.opcode "
						+ " and t1.rolecode='" + role + "' and to_char(t.NPSJ,'YYYYMMDD') = '20141217') " + " order by NPSJ desc ";
			}

		}
		try {
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sql);
			while (rs.next()) {
				String[] tempStr = new String[] { rs.getString("zbid"), rs.getString("czrw"), "", rs.getString("npsj"), rs.getString("bzsx"),
						rs.getString("ISMODEL"), rs.getString("cardkind") };
				results.add(tempStr);
			}
			conn.close();
			stmt.close();
			rs.close();
		} catch (SQLException e) {
			e.printStackTrace();
			try {
				conn.close();
				stmt.close();
				rs.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}

		return results;
	}
	/**
	 * 描述：查询典型操作票明细表(广西)
	 * 
	 * @param zbid
	 *            主表ID
	 * @return[表ID、操作单位、操作内容]
	 */
	public List<String[]> gxqueryDXTicketMX(String zbid) {
		List<String[]> baseCardS = new ArrayList<String[]>();
		String sql = "SELECT T.MXID,T.CARDORDER,T.CARDITEM,T.CZDW,T.CZNR,T.CZSN,T.REMARK,T.CARDSTATES FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid
				+ "' ORDER BY to_number(T.CARDORDER)";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			String mxid = StringUtils.ObjToString(temp.get("MXID"));
			String order = StringUtils.ObjToString(temp.get("CARDORDER"));
			String item = StringUtils.ObjToString(temp.get("CARDITEM"));
			String czdw = StringUtils.ObjToString(temp.get("CZDW"));
			String cznr = StringUtils.ObjToString(temp.get("CZNR"));
			String czsn = StringUtils.ObjToString(temp.get("CZSN"));
			String remark = StringUtils.ObjToString(temp.get("REMARK"));
			String twoDevice = StringUtils.ObjToString(temp.get("CARDSTATES"));
			baseCardS.add(new String[] { mxid, order, item, czdw, cznr, czsn, remark, twoDevice});
		}
		return baseCardS;
	}

	/**
	 * 描述：查询典型操作票明细表
	 * 
	 * @param zbid
	 *            主表ID
	 * @return[表ID、操作单位、操作内容]
	 */
	public List<String[]> queryDXTicketMX(String zbid) {
		List<String[]> baseCardS = new ArrayList<String[]>();
		String sql = "SELECT T.MXID,T.CARDORDER,T.CARDITEM,T.CZDW,T.CZNR,T.CZSN FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid
				+ "' ORDER BY T.CARDORDER";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			String mxid = StringUtils.ObjToString(temp.get("MXID"));
			String order = StringUtils.ObjToString(temp.get("CARDORDER"));
			String item = StringUtils.ObjToString(temp.get("CARDITEM"));
			String czdw = StringUtils.ObjToString(temp.get("CZDW"));
			String cznr = StringUtils.ObjToString(temp.get("CZNR"));
			String czsn = StringUtils.ObjToString(temp.get("CZSN"));
			baseCardS.add(new String[] { mxid, order, item, czdw, cznr, czsn });
		}
		return baseCardS;
	}

	/**
	 * 描述：更新操作票状态
	 * 
	 * @param zbid
	 * 
	 * @return[操作内容]
	 */
	@SuppressWarnings({ "rawtypes", "null" })
	public List<RuleBaseMode> updateActionState(String zbid) {
		List<RuleBaseMode> nr = new ArrayList<RuleBaseMode>();
		Connection conn = DBManager.getConnection();

		DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
		Date date = new Date();
		String sql = "SELECT T.MXID,T.CARDORDER,T.CZDW,T.CZNR FROM " + username + "t_a_CZPMX T WHERE T.F_ZBID='" + zbid + "' ORDER BY T.CARDORDER";
		@SuppressWarnings("rawtypes")
		List results = DBManager.query(sql);
		@SuppressWarnings("rawtypes")
		Map temp = new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			String mxid = StringUtils.ObjToString(temp.get("MXID"));
			String czdw = StringUtils.ObjToString(temp.get("CZDW"));
			String cznr = StringUtils.ObjToString(temp.get("CZNR"));
			String order = StringUtils.ObjToString(temp.get("CARDORDER"));
			nr.addAll(CZPService.getService().getRBMList(czdw, cznr));
			if (!nr.get(i).getBeginStatus().equals("") && !nr.get(i).getEndState().equals("")) {
				try {
					sql = "DELETE FROM " + username + "T_A_CZPACTIONSTATE  T WHERE T.CARDID='" + zbid + "'";
					DBManager.execute(sql);
					sql = "INSERT INTO " + username + "T_A_CZPACTIONSTATE(STATEID,EQUIPID,BEGINSTATUS,ENDSTATE,CARDID,CARDMXID,ACTIONTIME,STATEORDER) "
							+ "VALUES('" + java.util.UUID.randomUUID().toString() + "','" + nr.get(i).getPd().getPowerDeviceID() + "','"
							+ nr.get(i).getBeginStatus() + "','" + nr.get(i).getEndState() + "','" + zbid + "','" + mxid + "','" + dateFormat.format(date)
							+ "','" + order + "')";
					DBManager.execute(sql);
					conn.commit();
					DBManager.closeConnect();
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}

		return nr;
	}

	/**
	 * 操作票删除
	 * 
	 * @param MLPCode
	 */
	public void delTicket(String zbid) {
		String sqlzb = "delete from " + username + "t_a_czpzb t where t.zbid='" + zbid + "'";
		String sqlmx = "delete from " + username + "t_a_czpmx t where t.f_zbid='" + zbid + "'";
		// int
		// ishere=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid='"+zbid+"'");
		// int
		// num=DBManager.queryForInt("select count(*) from "+CBSystemConstants.opcardUser+"t_a_czpzb where zbid='"+zbid+"' and dmisflag=1");
		// if(ishere>0&&num==0){
		DBManager.execute(sqlzb);
		DBManager.execute(sqlmx);
		// }else if(ishere>0&&num>0){
		// ShowMessage.view("已导入OMS,不能删除！");
		// }

	}

	/**
	 * 更新操作票回令信息
	 * 
	 * @param mxID
	 *            表ID
	 * @param hlrName
	 *            回令人姓名
	 */
	public void updateHlrHLSJ(String mxID, String hlrName) {
		String sql = "UPDATE " + username + "t_a_CZPMX T SET T.HLR='" + hlrName + "', T.HLSJ=SYSDATE WHERE T.MXID='" + mxID + "'";
		DBManager.execute(sql);
	}

	/**
	 * 更新操作票受令信息
	 * 
	 * @param mxID
	 *            表ID
	 * @param hlrName
	 *            受令人姓名
	 */
	public void updateSlr(String mxID, String slrName) {
		String sql = "UPDATE " + username + "t_a_CZPMX T SET T.SLR='" + slrName + "' WHERE T.MXID='" + mxID + "'";
		DBManager.execute(sql);
	}

	/**
	 * 更新操作票下令信息
	 * 
	 * @param userID
	 *            用户ID
	 * @param mxID
	 *            明细表ID
	 */
	public void updateXlrXlsj(String userID, String mxID) {
		String sql = "UPDATE " + username + "t_a_CZPMX T SET T.XLR='" + userID + "',T.XLSJ=SYSDATE WHERE T.MXID='" + mxID + "'";
		DBManager.execute(sql);
	}

	/**
	 * 操作票审核
	 * 
	 * @param User
	 *            审核人对象
	 */
	public void updateShr(User shr, CodeNameModel cnm) {
		String sql = "UPDATE " + username + "t_a_CZPZB T SET T.SHR='" + shr.getUserID() + "' WHERE T.ZBID='" + cnm.getCode() + "'";
		DBManager.execute(sql);
	}

	/**
	 * 将操作票指令归档
	 * 
	 * @param equipid
	 *            设备ID
	 */
	public void updateState(String cardid) {
		String sql = "UPDATE " + username + "t_a_CZPZB T SET T.STATES=1 WHERE T.ZBID='" + cardid + "'";
		DBManager.execute(sql);
	}

	/**
	 * 描述：查询新操作票票号
	 * 
	 * @param zbid
	 *            主表ID
	 * @return[表ID、操作单位、操作内容]
	 */
	public int querySerialNumber(int cardkind) {
		int result;
		String sql = "";
		sql = "SELECT count(*) FROM " + username + "t_a_CZPNO T WHERE T.UNITCODE='" + CBSystemConstants.unitCode + "' and T.CARDKIND=" + cardkind;
		int count = DBManager.queryForInt(sql);
		if (count == 0) {
			sql = "INSERT INTO " + username + "t_a_CZPNO(CARDKIND,CARDNO,CARDDATE,UNITCODE) VALUES(" + cardkind + ",'1',sysdate,'" + CBSystemConstants.unitCode
					+ "')";
			DBManager.execute(sql);
		}
		sql = "SELECT count(*) FROM " + username + "t_a_CZPNO T WHERE T.UNITCODE='" + CBSystemConstants.unitCode + "' and T.CARDKIND=" + cardkind
				+ " and to_char(t.carddate,'yyyy')=to_char(sysdate,'yyyy')";
		count = DBManager.queryForInt(sql);
		if (count == 0) {
			result = 1;
			System.out.println("我又返回：" + result);
		} else {
			sql = "SELECT T.CARDNO FROM " + username + "t_a_CZPNO T WHERE T.UNITCODE='" + CBSystemConstants.unitCode + "' and T.CARDKIND=" + cardkind;
			result = DBManager.queryForInt(sql);
		}
		return result;
	}

	/**
	 * 描述：查询新操作票票号
	 * 
	 * @param
	 * 
	 * @return
	 */
	public int SerialNumber(int cardkind) {
		int result;
		String sql = "";
		sql = "SELECT T.CARDNO  FROM " + username + "t_a_CZPNO T WHERE T.UNITCODE='" + CBSystemConstants.getUser().getUnitCode() + "' and T.CARDKIND="
				+ cardkind;
		int count = DBManager.queryForInt(sql);
		if (count == 0) {
			result = 1;
		} else {
			sql = "SELECT T.CARDNO  FROM " + username + "t_a_CZPNO T WHERE T.UNITCODE='" + CBSystemConstants.getUser().getUnitCode() + "' and T.CARDKIND="
					+ cardkind;
			result = DBManager.queryForInt(sql);
		}
		return result;
	}

	/**
	 * 描述：查询操作票类型
	 * 
	 * @param zbid
	 *            主表ID
	 * @return[表ID、操作单位、操作内容]
	 */
	public String queryTicketKind(String zbid) {
		List<String[]> baseCardS = new ArrayList<String[]>();
		String sql = "select t.CARDKIND from "+CBSystemConstants.opcardUser+"t_a_CZPZB t where t.zbid = '" + zbid + "'";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		String CardKind = "";
		if (results.size() == 1) {
			temp = (Map) results.get(0);
			CardKind = StringUtils.ObjToString(temp.get("CARDKIND"));
		} else {
			CardKind = "异常";
		}

		return CardKind;
	}

	/**
	 * 生成操作票票号
	 * 
	 * @param cardid
	 *            操作票ID
	 */
	public void updateTicketBH(String cardid, String bh) {
		String sql = "UPDATE " + username + "t_a_CZPZB T SET T.BH='" + bh + "' WHERE T.ZBID='" + cardid + "'";
		DBManager.execute(sql);
	}
	
	/**
	 * 查询操作票信息
	 * 
	 * @param cardid
	 *            操作票ID
	 */
	public String queryTicketBH(String cardid) {
		String sql = "select t.BH from  " + username + "t_a_CZPZB T  WHERE T.ZBID='" + cardid + "'";
		List results = DBManager.query(sql);
		Map temp = new HashMap();
		String tempBH = "";
		if (results.size() == 1) {
			temp = (Map) results.get(0);
			tempBH = StringUtils.ObjToString(temp.get("BH"));
		} else {
			tempBH = "异常";
		}

		return tempBH;
	}

	/**
	 * 设置操作票已导入OMS
	 * 
	 * @param cardid
	 *            操作票ID
	 */
	public void updateTicketDMISFlag(String cardid) {
		String sql = "UPDATE " + username + "t_a_CZPZB T SET T.DMISFLAG = 1 WHERE T.ZBID='" + cardid + "'";
		DBManager.execute(sql);
	}

	/**
	 * 操作票票号
	 * 
	 * @param cardid
	 *            操作票ID
	 */
	public void updateSerialNumber(int cardkind, int cardno) {
		String sql = "update " + username + "t_a_CZPNO T set T.CARDNO= " + cardno + ",t.CARDDATE=sysdate WHERE T.UNITCODE='"
				+ CBSystemConstants.getUser().getUnitCode() + "' and T.CARDKIND=" + cardkind;
		DBManager.execute(sql);
	}

	/**
	 * 微术语防误信息添加
	 * */
	private List<String> getFWInfo(List<CardItemModel> list) {
		List<String> strs = new ArrayList<String>();
		for (int i = 0; i < list.size(); i++) {
			CardItemModel word = list.get(i);
			ArrayList<OperationInfo> operation = CheckWord.getOperation(word.getCardDesc());
			if (operation.size() == 0) {
				strs.add("");
				continue;
			}
			String equip = operation.get(0).getEquipName();
			String operator = operation.get(0).getOperation();

			if (equip.contains("接地刀闸")) {
				if (operator.contains("合上") || operator.contains("推上")) {
					strs.add("不能带电合接地刀闸");
				} else {
					strs.add("");
				}
			} else if (equip.contains("刀闸") || equip.contains("隔离开关")) {
				if (operator.contains("合上") || operator.contains("推上")) {
					strs.add("不能带地线合刀闸");
				} else if (operator.contains("拉开")) {
					strs.add("不能带负荷拉刀闸");
				} else {
					strs.add("");
				}
			} else if (equip.contains("开关") || equip.contains("断路器")) {
				if (operator.contains("合上") || operator.contains("推上")) {
					strs.add("不能合开关");
				} else {
					strs.add("");
				}
			}
		}
		return strs;
	}

	/**
	 * 保存修改的接地线数据
	 * */
	public void saveRemovableDevice(HashMap<String, HashMap<PowerDevice, String>> rms) {
		Iterator iter = rms.entrySet().iterator();
		while (iter.hasNext()) {
			Map.Entry entry = (Map.Entry) iter.next();
			String key = (String) entry.getKey();
			HashMap<PowerDevice, String> val = (HashMap<PowerDevice, String>) entry.getValue();
			for (PowerDevice rm : val.keySet()) {
				PowerDevice removeDevice = CBSystemConstants.getRMDeviceByDetailAndType(rm.getRmType(), rm.getPowerStationID(), rm.getKnife(), rm.getDevice());
				if (removeDevice == null) {
					insertPowerDevice(rm);
					// 修改缓存
					CBSystemConstants.getRMDevice().get(key).add(rm);
				} else {
					updatePowerDevice(rm);
					// 修改缓存
					removeDevice.setDeviceStatus(rm.getDeviceStatus());
				}
			}
		}
		rms.clear();
	}

	private void updatePowerDevice(PowerDevice rm) {
		String sql = "update "+CBSystemConstants.opcardUser+"t_a_PowerDevice t set  t.equipid=? ,t.terminalid=? ,t.romvestatus=?,t.removetype=? where t.removeid=?";
		String equip = rm.getDevice();
		String knife = rm.getKnife();
		String status = rm.getDeviceStatus();
		int rmType = PowerDevice.GROUNDLINE;
		String removeId = rm.getPowerDeviceID();
		DBManager.update(sql, equip, knife, status, rmType, removeId);
	}

	private void insertPowerDevice(PowerDevice rm) {
		String sql = "insert into  "+CBSystemConstants.opcardUser+"t_a_PowerDevice(removeid,equipid,terminalid,romvestatus,removetype) values(?,?,?,?,?)";
		String equip = rm.getDevice();
		String knife = rm.getKnife();
		String status = rm.getDeviceStatus();
		int rmType = rm.getRmType();
		String removeId = rm.getPowerDeviceID();
		DBManager.update(sql, removeId, equip, knife, status, rmType);
	}

	


	

	/**
	 * 描述 ：总调将操作票保存为典型票
	 * @param zbid
	 */
	
	public boolean DXTicketDB(String zbid){
		Connection conn = DBManager.getConnection();
		String kind = CBSystemConstants.apptype;
		try {
			conn.setAutoCommit(false);
			// 修改为典型票
		     //String dxpzbid = java.util.UUID.randomUUID().toString();// 主表ID
			String sql = "";
			sql = "UPDATE t_a_CZPZB SET ISMODEL='1' WHERE ZBID='"+ zbid+"'";
			DBManager.execute(sql);
			conn.commit();
			conn.close();
		} catch (SQLException e) {
			e.printStackTrace();
			try {
				conn.rollback();
				conn.close();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			return false;
		}
		return true;
		
		
		
	}
	/**
	

	/**
	 * 创建时间 2013年12月9日 下午2:11:59 存储开票时线路电源侧信息
	 * 
	 * <AUTHOR>
	 * @Title insertLineSrcLoad
	 * @param cardid
	 */
	public void insertLineSrcLoad(String cardid) {
		Map<String, PowerDevice> srcmap = CBSystemConstants.LineSource;
		String sql = "insert into "+CBSystemConstants.opcardUser+"T_A_LINE_SRC_LOAD(ZBID,XLID,SRC) values(?,?,?) ";
		for (Iterator<String> itr = srcmap.keySet().iterator(); itr.hasNext();) {
			String devid = (String) itr.next();
			if (srcmap.get(devid) == null)
				continue;
			String src = srcmap.get(devid).getPowerDeviceID();
			DBManager.update(sql, cardid, devid, src);
		}

	}

	/**
	 * 创建时间 2013年12月9日 下午2:31:05 是否为最大范围停电
	 * 
	 * <AUTHOR>
	 * @Title insertIsMaxLoadOff
	 * @param cardid
	 */
	public void insertIsMaxLoadOff(String cardid) {
		int isMaxOff;
		if (CBSystemConstants.isMaxRangeOffTicket) {
			isMaxOff = 0;
		} else {
			isMaxOff = 1;
		}
		String sql = "insert into "+CBSystemConstants.opcardUser+"T_A_IS_MAX_LOAD_OFF(ZBID,ISMAXLOADOFF) values(?,?)";
		try {
			DBManager.update(sql, cardid, isMaxOff);
		} catch (Exception ex) {

		}
	}

	
	

	

	

	@SuppressWarnings("rawtypes")
	public static int getRMStatus(String device, String knife, int rmType) {
		String knsql = "";
		if (knife != null && !"".equals(knife)) {
			knsql = "and T.TERMINALID='" + knife + "'";
		}
		String sql = "select t.ROMVESTATUS from "+CBSystemConstants.opcardUser+"T_A_REMOVABLEDEVICE t where T.REMOVETYPE='" + rmType + "'  and opcode='"
				+ CBSystemConstants.opCode + "' AND T.equipid='" + device + "'" + knsql;
		List list = DBManager.query(sql);
		if (list.size() > 0) {
			Map resultMap = (Map) list.get(0);
			return Integer.parseInt(resultMap.get("ROMVESTATUS").toString());
		}
		return -1;
	}

	/*
	 * public static boolean updateOmsJK(String cardid, List<CardItemModel>
	 * descList, String czrw) { String
	 * username=CBSystemConstants.getUser().getUserName(); //更新主表
	 * DBManager.executeOMS
	 * ("update  BJOMS.T_MonitorOperationTicket_Main t set operationTask='"
	 * +czrw+"',userName='"+username+"' where t.id='"+cardid+"'");
	 * DBManager.executeOMS
	 * ("delete from BJOMS.T_MonitorOperationTicket_Item t where t.ticketId='"
	 * +cardid+"'"); int group=1; for (int i=0;i<descList.size();i++) {
	 * CardItemModel cim = descList.get(i);
	 * if(i!=0&&!cim.getStationName().equals
	 * (descList.get(i-1).getStationName())){ group++; } String iteminfo =
	 * cim.getCardDesc(); String cbid = cim.getUuIds(); DBManager.executeOMS(
	 * "insert into BJOMS.T_MonitorOperationTicket_Item(id,operationItem,ticketId,indexNumber,groupNumber) values('"
	 * +cbid+"','"+iteminfo+"','"+cardid+"','"+(i+1)+"','"+group+"')"); } return
	 * true; }
	 */
	public static boolean updateJKCzpFromOms(List<Map<String, String>> list) {
		try {
			// List list =
			// DBManager.queryForListOMS("select * from BJOMS.T_MonitorOperationTicket_Main where id='"+cardid+"'");
			// if(list.size()==0)
			// return false;
			Map<String, String> map = list.get(0);
			String cardid = map.get("id");
			String czrw = StringUtils.ObjToString(map.get("operationtask"));
			// 更新主表
			List<Map> zblist = DBManager.queryForList("select * from  "+CBSystemConstants.opcardUser+"t_a_czpzb t  where t.zbid='" + cardid + "'");
			if (zblist.size() == 0)
				DBManager.execute("insert into  "+CBSystemConstants.opcardUser+"t_a_czpzb(czrw,zbid) values('" + czrw + "','" + cardid + "')");
			else
				DBManager.execute("update  "+CBSystemConstants.opcardUser+"t_a_czpzb t set t.czrw='" + czrw + "' where t.zbid='" + cardid + "'");
			// list=DBManager.queryForListOMS("select * from BJOMS.T_MonitorOperationTicket_Item where ticketId='"+cardid+"'");
			for (Map<String, String> item : list) {

				String mxid = item.get("fid");
				String rw = item.get("operationitem");
				String order = item.get("indexnumber");
				List<Map> cblist = DBManager.queryForList("select * from  "+CBSystemConstants.opcardUser+"t_a_czpmx t  where t.mxid='" + mxid + "'");
				String sql;
				if (cblist.size() == 0)
					sql = "insert into "+CBSystemConstants.opcardUser+"t_a_czpmx(cznr,cardorder,mxid,f_zbid) values('" + rw + "','" + order + "','" + mxid + "','" + cardid + "')";
				else
					sql = "update "+CBSystemConstants.opcardUser+"t_a_czpmx t set t.cznr='" + rw + "',t.cardorder='" + order + "' where t.mxid='" + mxid + "'";

				DBManager.execute(sql);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public static boolean updateJKOmsfromCZP(String zbid) {
		List list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.zbid=?", zbid);
		Map map = (Map) list.get(0);
		String czrw = StringUtils.ObjToString(map.get("czrw"));
		String username = CBSystemConstants.getUser().getUserName();
		StringBuffer xmlData = new StringBuffer("");
		xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		xmlData.append("<Datas>");

		/*
		 * List list_org = DBManager.queryForListOMS(
		 * "select * from BJOMS.T_MonitorOperationTicket_Main where id='"
		 * +zbid+"'"); if(list_org.size()==0){ DBManager.executeOMS(
		 * "insert into BJOMS.T_MonitorOperationTicket_Main(id,operationTask,userName) values('"
		 * +zbid+"','"+czrw+"','"+username+"') "); }else{ DBManager.executeOMS(
		 * "update BJOMS.T_MonitorOperationTicket_Main t set operationTask='"
		 * +czrw+"',userName='"+username+"' where t.id='"+zbid+"'"); }
		 * DBManager.executeOMS(
		 * "delete from BJOMS.T_MonitorOperationTicket_Item t where t.ticketId='"
		 * +zbid+"'");
		 */
		list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CZPMX t where t.f_zbid=?", zbid);
		int group = 1;
		for (int i = 0; i < list.size(); i++) {
			map = (Map) list.get(i);
			String cznr = StringUtils.ObjToString(map.get("cznr"));
			String cardorder = StringUtils.ObjToString(map.get("cardorder"));
			String mxid = StringUtils.ObjToString(map.get("mxid"));
			if (i != 0 && !map.get("czdw").equals(((Map) list.get(i - 1)).get("czdw"))) {
				group++;
			}
			xmlData.append("   <ITEM>");
			xmlData.append("             <id>").append(zbid).append("</id>");
			xmlData.append("             <operationtask>").append(czrw).append("</operationtask>");
			xmlData.append("             <fid>").append(mxid).append("</fid>");
			xmlData.append("             <operationitem>").append(cznr).append("</operationitem>");
			xmlData.append("             <indexnumber>").append(cardorder).append("</indexnumber>");
			xmlData.append("             <groupnumber>").append(group).append("</groupnumber>");

			xmlData.append("         </ITEM>");
			/*
			 * String sql=
			 * "insert into BJOMS.T_MonitorOperationTicket_Item(id,operationItem,ticketId,indexNumber,groupNumber) values('"
			 * +mxid+"','"+cznr+"','"+zbid+"','"+cardorder+"','"+group+"')";
			 * DBManager.executeOMS(sql);
			 */
		}
		xmlData.append("      </Datas>");
		try {
			String s = WebServiceUtil.webServiceExecute(WebServiceUtil.pro.getProperty("JK_UPDATE"), xmlData.toString(), "updateResource", "arg0");
			String cz = WebServiceUtil.getReqInfo(s, "ITEM").get(0).get("caozuo");
			if (!"操作成功".endsWith(cz)) {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		String sql = "update "+CBSystemConstants.opcardUser+"t_a_czpzb set dmisflag='1' where zbid ='" + zbid + "'";
		DBManager.execute(sql);
		return true;
	}

	public static boolean updateOmsDDFS(String zbid) {
		List list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.zbid=?", zbid);
		Map map = (Map) list.get(0);
		String czrw = StringUtils.ObjToString(map.get("czrw"));
		String username = CBSystemConstants.getUser().getUserName();
		StringBuffer xmlData = new StringBuffer("");
		xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
		xmlData.append("<Datas>");
		list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CZPMX t where t.f_zbid=?", zbid);
		int group = 1;
		for (int i = 0; i < list.size(); i++) {
			map = (Map) list.get(i);
			String cznr = StringUtils.ObjToString(map.get("cznr"));
			String cardorder = StringUtils.ObjToString(map.get("cardorder"));
			String mxid = StringUtils.ObjToString(map.get("mxid"));
			String czsn = StringUtils.ObjToString(map.get("czsn"));
			String czdw = StringUtils.ObjToString(map.get("czdw"));
			if (i != 0 && !map.get("czdw").equals(((Map) list.get(i - 1)).get("czdw"))) {
				group++;
			}
			xmlData.append("   <ITEM>");
			xmlData.append("             <zbid>").append(zbid).append("</zbid>");
			xmlData.append("             <czrw>").append(czrw).append("</czrw>");
			xmlData.append("             <username>").append(username).append("</username>");
			xmlData.append("             <mxid>").append(mxid).append("</mxid>");
			xmlData.append("             <czsn>").append(czsn).append("</czsn>");
			xmlData.append("             <czdw>").append(czdw).append("</czdw>");
			xmlData.append("             <cznr>").append(cznr).append("</cznr>");
			xmlData.append("             <cardorder>").append(cardorder).append("</cardorder>");
			xmlData.append("         </ITEM>");
		}
		xmlData.append("      </Datas>");
		try {
			String s = WebServiceUtil.webServiceExecute(WebServiceUtil.pro.getProperty("DD_SERVICE"), xmlData.toString(), "updateDaoZhaResource", "arg0");
			String cz = WebServiceUtil.getReqInfo(s, "ITEM").get(0).get("caozuo");
			if (!"操作成功".endsWith(cz)) {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		String sql = "update "+CBSystemConstants.opcardUser+"t_a_czpzb set dmisflag='1' where zbid ='" + zbid + "'";
		DBManager.execute(sql);

		return true;

	}

	public static boolean updateOmsDD(String zbid) {
		try {
			DBManager.getOMSConnection();
		} catch (Exception e) {
			StringBuffer xmlData = new StringBuffer("");
			xmlData.append("<?xml version=\"1.0\" encoding=\"GBK\" ?>");
			xmlData.append("<Datas>");
			xmlData.append("   <ITEM>");
			xmlData.append("             <zbid>").append(zbid).append("</zbid>");
			xmlData.append("   </ITEM>");
			xmlData.append("   </Datas>");
			WebServiceUtil.webServiceExecute(WebServiceUtil.pro.getProperty("CZP_SERVICE"), xmlData.toString(), "ddUpdate", "arg");
			return true;
		}
		List list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.zbid=?", zbid);
		Map map = (Map) list.get(0);
		String czrw = StringUtils.ObjToString(map.get("czrw"));
		String username = CBSystemConstants.getUser().getUserName();
		List list_org = DBManager.queryForListOMS("select * from runmode.T_DDZXCZP_MAIN where DDZXCZPID='" + zbid + "'");
		if (list_org.size() == 0) {
			DBManager.executeOMS("insert into runmode.T_DDZXCZP_MAIN(DDZXCZPID,OPERATIONTASK,NIPIAOREN) values('" + zbid + "','" + czrw + "','" + username
					+ "') ");
		} else {
			DBManager.executeOMS("update runmode.T_DDZXCZP_MAIN t set OPERATIONTASK='" + czrw + "',NIPIAOREN='" + username + "' where t.DDZXCZPID='" + zbid
					+ "'");
		}
		DBManager.executeOMS("delete from runmode.T_DDZXCZP_CB1 t where t.DDZXCZPID='" + zbid + "'");
		list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CZPMX t where t.f_zbid=?", zbid);
		for (int i = 0; i < list.size(); i++) {
			map = (Map) list.get(i);
			String cznr = StringUtils.ObjToString(map.get("cznr"));
			String cardorder = StringUtils.ObjToString(map.get("cardorder"));
			String mxid = StringUtils.ObjToString(map.get("mxid"));
			String czdw = StringUtils.ObjToString(map.get("czdw"));
			String sql = "insert into runmode.T_DDZXCZP_CB1(DDZXCZPID,DDZXCZPCBID,SHUNXU,COMPANY,CAOZUOZHILING) values('" + zbid + "','" + mxid + "','"
					+ cardorder + "','" + czdw + "','" + cznr + "')";
			DBManager.executeOMS(sql);
		}
		String sql = "update "+CBSystemConstants.opcardUser+"t_a_czpzb set dmisflag='1' where zbid ='" + zbid + "'";
		DBManager.execute(sql);
		return true;
	}

	public static boolean updateOmsDDGD(String zbid) {
		List list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_czpzb t where t.zbid=?", zbid);
		Map map = (Map) list.get(0);
		String czrw = StringUtils.ObjToString(map.get("czrw"));
		int states = Integer.valueOf(StringUtils.ObjToString(map.get("states")));
		int dmisflag = Integer.valueOf(StringUtils.ObjToString(map.get("dmisflag")));
		int ylflag = Integer.valueOf(StringUtils.ObjToString(map.get("ylflag")));
		int cardkind = Integer.valueOf(StringUtils.ObjToString(map.get("cardkind")));
		int buildkind = Integer.valueOf(StringUtils.ObjToString(map.get("buildkind")));
		if (cardkind == 0) {
			buildkind = 12;
		} else if (cardkind == 1) {
			cardkind = 2;
		}
		int islock = Integer.valueOf(StringUtils.ObjToString(map.get("islock")));
		int ismodel = Integer.valueOf(StringUtils.ObjToString(map.get("ismodel")));
		String unitcode = StringUtils.ObjToString(map.get("opcode"));
		String npr = StringUtils.ObjToString(map.get("npr"));
		String npsj = StringUtils.ObjToString(map.get("npsj")).replace(".0", "");
		String shr = StringUtils.ObjToString(map.get("shr"));
		String bh = StringUtils.ObjToString(map.get("bh"));
		String shsj = StringUtils.ObjToString(map.get("shsj")).replace(".0", "");
		int cardstates = Integer.valueOf(StringUtils.ObjToString(map.get("cardstates")));
		if (cardstates == 0) {
			cardstates = 3;
		}
		String equipid = StringUtils.ObjToString(map.get("equipid"));

		String username = CBSystemConstants.getUser().getUserName();
		List list_org = DBManager.queryForListOMS("select * from poweroperationcard.czpzb where zbid='" + zbid + "'");
		if (list_org.size() == 0) {
			DBManager
					.executeOMS("insert into poweroperationcard.czpzb(zbid,czrw,states,dmisflag,ylflag,cardkind,buildkind,islock,ismodel,unitcode,npr,npsj,shr,bh,shsj,cardstates,equipid)"
							+ " values('"
							+ zbid
							+ "','"
							+ czrw
							+ "',"
							+ states
							+ ","
							+ dmisflag
							+ ","
							+ ylflag
							+ ","
							+ cardkind
							+ ","
							+ buildkind
							+ ","
							+ islock
							+ ","
							+ ismodel
							+ ",'"
							+ unitcode
							+ "','"
							+ npr
							+ "',to_date('"
							+ npsj
							+ "','yyyy-mm-dd hh24:mi:ss'),'"
							+ shr
							+ "','"
							+ bh
							+ "',to_date('" + shsj + "','yyyy-mm-dd hh24:mi:ss')," + cardstates + ",'" + equipid + "') ");
		} else {
			DBManager.executeOMS("update poweroperationcard.czpzb t set czrw='" + czrw + "',states=" + states + ",dmisflag=" + dmisflag + ",ylflag=" + ylflag
					+ ",cardkind=" + cardkind + ",buildkind=" + buildkind + ",islock=" + islock + ",ismodel=" + ismodel + ",unitcode='" + unitcode + "',npr='"
					+ username + "',npsj=to_date('" + npsj + "','yyyy-mm-dd hh24:mi:ss'),shr='" + shr + "',bh='" + bh + "',shsj=to_date('" + shsj
					+ "','yyyy-mm-dd hh24:mi:ss'),cardstates=" + cardstates + ",equipid='" + equipid + "' where t.zbid='" + zbid + "'");
		}
		DBManager.executeOMS("delete from poweroperationcard.czpmx t where t.f_zbid='" + zbid + "'");
		list = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CZPMX t where t.f_zbid=?", zbid);
		for (int i = 0; i < list.size(); i++) {
			map = (Map) list.get(i);
			String mxid = StringUtils.ObjToString(map.get("mxid"));
			String f_cbid = StringUtils.ObjToString(map.get("f_cbid"));
			String czdw = StringUtils.ObjToString(map.get("czdw"));
			String cznr = StringUtils.ObjToString(map.get("cznr"));
			String xlr = StringUtils.ObjToString(map.get("xlr"));
			String xlsj = StringUtils.ObjToString(map.get("xlsj")).replace(".0", "");
			String hlr = StringUtils.ObjToString(map.get("hlr"));
			String hlsj = StringUtils.ObjToString(map.get("hlsj"));
			String cardstatescb = StringUtils.ObjToString(map.get("cardstates"));
			String remark = StringUtils.ObjToString(map.get("remark"));
			String slr = StringUtils.ObjToString(map.get("slr"));
			int cardorder = Integer.valueOf(StringUtils.ObjToString(map.get("cardorder")));
			int carditem = Integer.valueOf(StringUtils.ObjToString(map.get("carditem")));
			String sql = "insert into poweroperationcard.czpmx(f_zbid,mxid,f_cbid,czdw,cznr,cardorder,carditem) values('" + zbid + "','" + mxid + "','"
					+ f_cbid + "','" + czdw + "','" + cznr + "'," + cardorder + "," + carditem + ")";
			DBManager.executeOMS(sql);
		}
		String sql = "update "+CBSystemConstants.opcardUser+"t_a_czpzb set dmisflag='1' where zbid ='" + zbid + "'";
		DBManager.execute(sql);
		return true;
	}
	/**
	 * 描述：保存手工拟票操作票含旧票新编《江西操作票相关方式和备注事项字段增加》
	 * <AUTHOR> 2016-04-06
	 * @param conn
	 *            数据库连接
	 * @param list
	 *            操作票指令集合
	 * @param czrw
	 *            操作任务
	 * @param isModel
	 *            典型票还是正常票
	 * @return 主表ID
	 * @throws SQLException
	 */
	public String InsertTicketNewDB(Connection conn, String zbid, List<CardItemModel> list, String czrw, String isModel, String cardKind, String buildKind,
			String equipID, String jxpNo,String xgfs,String bzsx) throws SQLException {
		Statement state;
		String date = "sysdate";
		state = conn.createStatement();
		if (CBSystemConstants.isOffline) {
			username = "";
			date = "date('now')";
		}
		// 插入操作票主表
		String zbsql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,BUILDKIND,NPR,NPSJ,ISMODEL,CARDKIND,OPCODE,EQUIPID,JXPNO,XGSS,BZSX) VALUES('" + zbid + "','"
				+ czrw + "'," + buildKind + ",'" + CBSystemConstants.getUser().getUserID() + "'," + date + ",'" + isModel + "'," + cardKind + ",'"
				+ CBSystemConstants.opCode + "','" + equipID + "','" + jxpNo + "','"+xgfs+"','"+bzsx+"')";
		state.execute(zbsql);

		/*
		 * List<String> sqls=new ArrayList<String>(); String changzhan="";
		 * String caozuozhiling=""; String cbid=""; String sql=
		 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
		 * ;
		 */
		for (int i = 0; i < list.size(); i++) {
			CardItemModel bcm = list.get(i);
			String operateNum = bcm.getCardNum();
			String operateItem = bcm.getCardItem();
			String operateUnit = bcm.getStationName();
			String operateShow = bcm.getShowName();
			String operateContent = bcm.getCardDesc();
			String remark = bcm.getRemark();
			String mxid = bcm.getUuIds();
			String mxsql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) " + "VALUES('" + mxid + "','" + zbid + "','"
					+ operateUnit + "','" + operateContent + "','" + operateNum + "','" + operateItem + "','" + operateShow + "')";
			state.execute(mxsql);
			/*
			 * if(!changzhan.equals("")&&!changzhan.equals(operateUnit)){
			 * sql=sql
			 * +"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
			 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
			 * cbid.length()-1); sqls.add(sql); sql=
			 * "http://127.0.0.1:8080/powernet-graphic-czpinerfaceomsClient/sampleGetDataProxy/test4.jsp?&"
			 * ; changzhan=""; caozuozhiling=""; cbid=""; }
			 * changzhan=operateUnit; caozuozhiling+=(operateContent+";");
			 * cbid+=(mxid+";");
			 */
		}
		/*
		 * sql=sql+"changzhan="+changzhan+"&caozuozhiling="+caozuozhiling.substring
		 * (0, caozuozhiling.length()-1)+"&cbid="+cbid.substring(0,
		 * cbid.length()-1); sqls.add(sql); for (String s : sqls) {
		 * System.out.println(s); }
		 */

		state.close();
		return zbid;
	}
	
	/**
	 * 描述：将操作票保存为典型票
	 * 
	 * @param zbid
	 *            主表ID
	 */
	public boolean InsertDXTicketDBJX(String zbid) {
		Connection conn = DBManager.getConnection();
		Statement state = null;
		String kind = CBSystemConstants.apptype;
		try {
			conn.setAutoCommit(false);
			state = conn.createStatement();
			// 插入操作票主表
			String dxpzbid = java.util.UUID.randomUUID().toString();// 主表ID
			String sql = "";
			if (kind.equals("0")) {
				sql = "INSERT INTO "
						+ username
						+ "t_a_CZPZB(ZBID,CARDKIND,CZRW,BZSX,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID,XGSS) "// 增加了BZSX
						+ " SELECT '" + dxpzbid + "',CARDKIND,CZRW" + ",BZSX,'" + CBSystemConstants.getUser().getUserID() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID,XGSS FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			} else {
				sql = "INSERT INTO " + username + "t_a_CZPZB(ZBID,CZRW,NPR,NPSJ,ISMODEL,OPCODE,EQUIPID,CARDKIND) " + " SELECT '" + dxpzbid + "',CZRW,'"
						+ CBSystemConstants.getUser().getUserID() + "',sysdate,'1','"
						+ CBSystemConstants.opCode + "',EQUIPID ,'3' FROM " + username
						+ "t_a_CZPZB WHERE ZBID='" + zbid + "'";
			}
			state.execute(sql);
			sql = "SELECT '' ID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN FROM " + username + "t_a_CZPMX WHERE F_ZBID='" + zbid + "'";
			// tanfei 2014-09-03
			// 保存为典型票的时候报错，达梦数据库不支持"+CBSystemConstants.opcardUser+"GUID函数写法
			// sql = "INSERT INTO " + username
			// + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) "
			// + "SELECT " + username + "GUID,'" + dxpzbid
			// + "',CZDW,CZNR,CARDORDER,CARDITEM,CZSN FROM " + username
			// + "t_a_CZPMX WHERE F_ZBID='" + zbid + "'";
			List czpList = DBManager.query(sql);
			Map temp = new HashMap();
			for (int i = 0; i < czpList.size(); i++) {
				temp = (Map) czpList.get(i);
				String zbid2 = java.util.UUID.randomUUID().toString();// 主表ID
				String[] tempStr = new String[] { java.util.UUID.randomUUID().toString(), dxpzbid, StringUtils.ObjToString(temp.get("CZDW")),
						StringUtils.ObjToString(temp.get("CZNR")), StringUtils.ObjToString(temp.get("CARDORDER")),
						StringUtils.ObjToString(temp.get("CARDITEM")), StringUtils.ObjToString(temp.get("CZSN")), };
				sql = "INSERT INTO " + username + "t_a_CZPMX(MXID,F_ZBID,CZDW,CZNR,CARDORDER,CARDITEM,CZSN) values('" + tempStr[0] + "'," + "'" + tempStr[1]
						+ "'," + "'" + tempStr[2] + "'," + "'" + tempStr[3] + "'," + "'" + tempStr[4] + "'," + "'" + tempStr[5] + "'," + "'" + tempStr[6]
						+ "')";
				state.execute(sql);
				conn.commit();
			}
			state.close();
			conn.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				conn.rollback();
				state.close();
				conn.close();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			return false;
		}
		return true;
	}
}
